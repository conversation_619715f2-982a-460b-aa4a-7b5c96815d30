<p-confirmDialog />
<p-toast/>
<app-inactivity-monitor
    [inactivityTimeoutLimit]="confirmDialogTimeoutLimit"
    [startChecking]="startCheckingInactivity"
    (expired)="closeConfirmationDialog()"
></app-inactivity-monitor>
<app-inactivity-monitor
    [inactivityTimeoutLimit]="auditTrailService.confirmDialogTimeoutLimit"
    [startChecking]="auditTrailService.startCheckingInactivity"
    (expired)="auditTrailService.closeConfirmationDialog()"
></app-inactivity-monitor>

<div *ngIf="isLoading || isLoadingNewSubjectFile">
  <app-loading-spinner [isLoading]="isLoading"></app-loading-spinner>
</div>

<div *ngIf="!isLoading else loadingSpinner">
    <div *ngIf="subjectFiles.length > 0 else noData">
        <div *ngFor="let file of subjectFiles" class="my-3">
            <p-accordion>
                <p-accordionTab header="{{ getLabel(file.type) }}">
                    @if(file.files.length > 0){
                        <div class="flex flex-row justify-content-between flex-wrap gap-2 mb-3">
                            <div class="flex flex-row align-items-center">
                                <div class="pr-3">
                                    <label class="subcontainer-title">{{ getLabel(file.type) }}</label>
                                </div>
                            </div>
                            <div class="flex flex-row align-items-center">
                                <div class="pr-3">
                                    <p-button
                                        [disabled]="!readAndWritePermissions"
                                        [style]="{'color': '#FFFFFF', 'border': 'none', 'background': '#0AB4BA', 'font-family': 'Open Sans', 'font-size': '14px'}"
                                        label="{{ 'content.uploadNewFile' | translate }}"
                                        icon="pi pi-plus"
                                        [rounded]="true"
                                        (onClick)="uploadNew(file.type)"
                                    ></p-button>
                                </div>
                            </div>
                        </div>

                        <p-table
                            [value]="file.files"
                            (onFilter)="onFilter($event)"
                            dataKey="id"
                            [rowHover]="true"
                            [paginator]="true"
                            [rows]="10"
                            [rowsPerPageOptions]="[5, 10, 20]"
                            [scrollable]="true"
                            scrollHeight="flex"
                            scrollDirection="horizontal"
                            [tableStyle]="{ 'min-width': '75rem' }"
                            styleClass="fixed-table"
                            [showCurrentPageReport]="true"
                            currentPageReportTemplate="{{ 'content.showing' | translate }} {first} {{ 'content.to' | translate }} {last} {{ 'content.of' | translate}} {totalRecords} {{ 'content.records' | translate }}"
                            [sortField]="'number'" [sortOrder]="1">
                            <ng-template pTemplate="header">
                                <tr>
                                    <th class="fixed-column-sm" pSortableColumn="name">{{'content.name' | translate}}<p-sortIcon field="name"></p-sortIcon></th>
                                    <th class="fixed-column-sm" pSortableColumn="number">{{ 'content.number' | translate }}<p-sortIcon field="number"></p-sortIcon></th>
                                    <th class="fixed-column-sm" pSortableColumn="alias">{{ 'content.alias' | translate }}<p-sortIcon field="alias"></p-sortIcon></th>
                                    <th class="fixed-column-sm" pSortableColumn="description">{{ 'content.description' | translate }}<p-sortIcon field="description"></p-sortIcon></th>
                                    <th class="fixed-column-sm" pSortableColumn="mimeType">{{ 'content.mimeType' | translate }}<p-sortIcon field="mimeType"></p-sortIcon></th>
                                    <th class="fixed-column-sm" pSortableColumn="preview">{{ 'content.preview' | translate }}<p-sortIcon field="content"></p-sortIcon></th>
                                    <th class="fixed-column-sm" pSortableColumn="download">{{ 'content.download' | translate }}<p-sortIcon field="content"></p-sortIcon></th>
                                    <th class="fixed-column-sm" pSortableColumn="createdAt">{{ 'created_at' | translate }}<p-sortIcon field="createdAt"></p-sortIcon></th>
                                    <th class="fixed-column-sm" pSortableColumn="updatedAt">{{ 'updated_at' | translate }}<p-sortIcon field="updatedAt"></p-sortIcon></th>
                                    <th pFrozenColumn [frozen]="true"></th>
                                </tr>
                                <tr>
                                    <th>
                                        <p-columnFilter type="text" field="name" [showMenu]="false" matchMode="contains">
                                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                                <input pInputText type="text" (input)="filter($event.target.value)" [value]="value">
                                            </ng-template>
                                        </p-columnFilter>
                                    </th>
                                    <th>
                                        <p-columnFilter type="number" field="number" [showMenu]="false" matchMode="contains">
                                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                                <input pInputText type="number" (input)="filter($event.target.value)" [value]="value">
                                            </ng-template>
                                        </p-columnFilter>
                                    </th>
                                    <th>
                                        <p-columnFilter type="text" field="alias" [showMenu]="false" matchMode="contains">
                                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                                <input pInputText type="text" (input)="filter($event.target.value)" [value]="value">
                                            </ng-template>
                                        </p-columnFilter>
                                    </th>
                                    <th>
                                        <p-columnFilter type="text" field="description" [showMenu]="false" matchMode="contains">
                                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                                <input pInputText type="text" (input)="filter($event.target.value)" [value]="value">
                                            </ng-template>
                                        </p-columnFilter>
                                    </th>
                                    <th>
                                        <p-columnFilter type="text" field="mimeType" [showMenu]="false" matchMode="contains">
                                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                                <input pInputText type="text" (input)="filter($event.target.value)" [value]="value">
                                            </ng-template>
                                        </p-columnFilter>
                                    </th>
                                    <th></th>
                                    <th></th>
                                    <th>
                                        <p-columnFilter type="date" field="createdAt" [showMenu]="false" matchMode="contains">
                                            <ng-template pTemplate="filter" let-filter="filterCallback" [formGroup]="formGroupDate2">
                                                <p-calendar
                                                    formControlName="date"
                                                    selectionMode="range"
                                                    (onSelect)="applyDateRangeFilter(dt, 'createdAt')"
                                                    (onInput)="applyDateRangeFilter(dt, 'createdAt')"
                                                    (onClickOutside)="applyDateRangeFilter(dt, 'createdAt')"
                                                    placeholder="{{ 'content.select' | translate }}"
                                                    dateFormat="{{ 'dateFormat' | translate }}"
                                                    appendTo="body"
                                                ></p-calendar>
                                            </ng-template>
                                        </p-columnFilter>
                                    </th>
                                    <th>
                                        <p-columnFilter type="date" field="updatedAt" [showMenu]="false" matchMode="customDateRange">
                                            <ng-template pTemplate="filter" let-filter="filterCallback" [formGroup]="formGroupDate">
                                                <p-calendar
                                                    formControlName="date"
                                                    selectionMode="range"
                                                    (onSelect)="applyDateRangeFilter(dt, 'updatedAt')"
                                                    (onInput)="applyDateRangeFilter(dt, 'updatedAt')"
                                                    (onClickOutside)="applyDateRangeFilter(dt, 'updatedAt')"
                                                    placeholder="{{ 'content.select' | translate }}"
                                                    dateFormat="{{ 'dateFormat' | translate }}"
                                                    appendTo="body"
                                                ></p-calendar>
                                            </ng-template>
                                        </p-columnFilter>
                                    </th>
                                    <th alignFrozen="right" pFrozenColumn [frozen]="true"></th>
                                </tr>
                            </ng-template>
                            <ng-template pTemplate="body" let-data>
                                <tr [pSelectableRow]="data">
                                    <td (click)="onPreview(data)" showDelay="1000" pTooltip="{{getLabel(data.name)}}" tooltipPosition="top" class="ellipsis-cell">{{ getLabel(data.name) }}</td>
                                    <td (click)="onPreview(data)" showDelay="1000" pTooltip="{{data.number}}" tooltipPosition="top" class="ellipsis-cell">{{ data.number }}</td>
                                    <td (click)="onPreview(data)" showDelay="1000" pTooltip="{{data.alias}}" tooltipPosition="top" class="ellipsis-cell">{{ data.alias }}</td>
                                    <td (click)="onPreview(data)" showDelay="1000" pTooltip="{{data.description}}" tooltipPosition="top" class="ellipsis-cell">{{ data.description }}</td>
                                    <td (click)="onPreview(data)" showDelay="1000" pTooltip="{{data.mimeType}}" tooltipPosition="top" class="ellipsis-cell">{{ data.mimeType }}</td>
                                    <td (click)="onPreview(data)" showDelay="500" pTooltip="{{'content.preview' | translate}}" tooltipPosition="top" class="ellipsis-cell">
                                        <div  class="flex justify-content-center">
                                            <button pButton pRipple icon="pi pi-folder-open" [text]="true" class="mr-2" style="padding: 0; width: 1.5rem;" (click)="onPreview(data)" [disabled]="!readAndWritePermissions"></button>
                                        </div>
                                    </td>
                                    <td showDelay="500" pTooltip="{{'content.download' | translate}}" tooltipPosition="top" class="ellipsis-cell">
                                        <div class="flex justify-content-center">
                                            <button pButton pRipple icon="pi pi-cloud-download" [text]="true" class="mr-2" style="padding: 0; width: 1.5rem;" (click)="onDownload(data)" [disabled]="!readAndWritePermissions"></button>
                                        </div>
                                    </td>
                                    <td (click)="onPreview(data)" showDelay="1000" pTooltip="{{data.createdAt | date:('dateTimeFormat' | translate)}}" tooltipPosition="top" class="ellipsis-cell">{{ data.createdAt | date:('dateTimeFormat' | translate) }}</td>
                                    <td (click)="onPreview(data)" showDelay="1000" pTooltip="{{data.updatedAt | date:('dateTimeFormat' | translate)}}" tooltipPosition="top" class="ellipsis-cell">{{ data.updatedAt | date:('dateTimeFormat' | translate) }}</td>
                                    <td alignFrozen="right" pFrozenColumn [frozen]="true" class="custom-border">
                                        <div *ngIf="readAndWritePermissions" class="flex flex-row">
                                            <!-- <button pButton pRipple icon="pi pi-pencil" [text]="true" class="mr-2" style="padding: 0; width: 1.5rem;" (click)="onEdit(data)"></button> -->
                                            <button pButton pRipple icon="pi pi-trash" [text]="true" class="mr-2" style="padding: 0; width: 1.5rem;" (click)="onDelete(data)"></button>
                                        </div>
                                    </td>
                                </tr>
                            </ng-template>
                            <ng-template pTemplate="emptymessage">
                                <tr>
                                    <td colspan="5" class="py-4">{{ 'content.noDataAvailable' | translate }}</td>
                                </tr>
                            </ng-template>
                        </p-table>
                    }
                    @else {
                        <div>
                            <app-empty
                                contentHeight="25vh"
                                [readAndWritePermissions]="readAndWritePermissions"
                                buttonLabel="content.uploadNewFile"
                                titleLabel="content.no_files_available"
                                (clicked)="uploadNew(file.type)"
                            ></app-empty>
                        </div>
                    }

                </p-accordionTab>
            </p-accordion>
        </div>
    </div>
</div>

<!-- Upload New -->
 <div>
    <app-upload-files
        [readAndWritePermissions]="readAndWritePermissions"
        [showUploadDialog]="showUploadDialog"
        [acceptedFiles]="acceptedFiles"
        [maxFileSize]="maxFileSize"
        (onCancel)="onCancelDialog()"
        (onUpload)="onUpload($event)"
        (onSelect)="onSelectedFiles($event)"
    ></app-upload-files>
 </div>

<!-- Preview -->
 <p-dialog [(visible)]="showPreviewDialog" styleClass="p-fluid" [modal]="true" [closable]="true" [style]="{'width': '70vw'}" (onHide)="onCancelPreviewDialog()">
    <ng-template pTemplate="header">
        <div>
            <label for="" [style]="{'color':'#204887', 'font-weight':'700', 'font-size':'20px'}">
                {{ 'content.preview' | translate }}
            </label>
        </div>
    </ng-template>
    <ng-template pTemplate="content">
        <div>
            <div class="flex flex-row justify-content-center align-items-center" style="height: 60vh;">
                <ngx-doc-viewer
                    [url]="previewUrl"
                    viewer="{{ getViewer(previewFile?.mimeType) }}"
                    style="width:100%;height:100%;"
                ></ngx-doc-viewer>
            </div>
        </div>
    </ng-template>
    <ng-template pTemplate="footer">
        <div class="flex flex-row justify-content-end">
            <button pButton pRipple label="{{ 'content.download' | translate }}" icon="pi pi-cloud-download" [outlined]="true" (click)="onDownload(previewFile)" [disabled]="!readAndWritePermissions"></button>
            <button pButton pRipple label="{{ 'cancel' | translate }}" [outlined]="true" class="p-button-secondary" (click)="onCancelPreviewDialog()"></button>
        </div>
    </ng-template>
</p-dialog>

<ng-template #noData>
    <div class="flex justify-content-center align-items-center" style="height: 50vh;">
        {{ 'content.noSubjectFileGroupsConfigured' | translate }}
    </div>
</ng-template>

<ng-template #loadingSpinner>
    <div class="flex justify-content-center align-items-center" style="height: 50vh;">
        <p-progressSpinner styleClass="w-5rem h-5rem" strokeWidth="8" fill="var(--surface-ground)" animationDuration=".5s" ariaLabel="loading" />
    </div>
</ng-template>