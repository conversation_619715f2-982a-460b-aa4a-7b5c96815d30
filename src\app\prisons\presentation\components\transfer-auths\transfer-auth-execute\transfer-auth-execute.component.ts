import { Component, EventEmitter, Input, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit, Output } from "@angular/core";
import { FormBuilder, FormGroup } from "@angular/forms";
import { TranslateService } from "@ngx-translate/core";
import { FilterService, MenuItem, MessageService, TreeNode } from "primeng/api";
import { ExtraData } from "src/verazial-common-frontend/core/general/actionsV2/domain/entity/extra-data.interface";
import { ReplaceAttributesRequestEntity } from "src/verazial-common-frontend/core/general/biometric/domain/entity/replace-attributes-request.entity";
import { ReplaceAttributesUseCase } from "src/verazial-common-frontend/core/general/biometric/domain/use-cases/replace-attributes.use-case";
import { RoleEntity } from "src/verazial-common-frontend/core/general/common/entity/role.entity";
import { AttributeData } from "src/verazial-common-frontend/core/general/flow/common/models/attribute-data.model";
import { KonektorPropertiesEntity } from "src/verazial-common-frontend/core/general/konektor/domain/entity/konektor-properties.entity";
import { GetKonektorPropertiesUseCase } from "src/verazial-common-frontend/core/general/konektor/domain/use-cases/get-konektor-properties.use-case";
import { GeneralSettings } from "src/verazial-common-frontend/core/general/manager/common/models/general-settings.model";
import { AuthStatus } from "src/verazial-common-frontend/core/general/prisons/common/enums/transfer-auth-status.enum";
import { TransferAuthUserSubjectEntity } from "src/verazial-common-frontend/core/general/prisons/domain/entity/transfer-auth/transfer-auth-user-subject.entity";
import { TransferAuthEntity } from "src/verazial-common-frontend/core/general/prisons/domain/entity/transfer-auth/transfer-auth.entity";
import { GetTransferAuthByIdUseCase } from "src/verazial-common-frontend/core/general/prisons/domain/use-cases/transfer-auth/get-transfer-auth-by-id.use-case";
import { UpdateTransferAuthUseCase } from "src/verazial-common-frontend/core/general/prisons/domain/use-cases/transfer-auth/update-transfer-auth-by-id.use-case";
import { UpdateTransferAuthUserSubjectUseCase } from "src/verazial-common-frontend/core/general/prisons/domain/use-cases/transfer-auth/update-transfer-auth-user-subject.use-case";
import { EntriesExitsEntity } from "src/verazial-common-frontend/core/general/subject/domain/entity/entries-exits.entity";
import { entriesExitsEnum } from "src/verazial-common-frontend/core/general/subject/domain/entity/entries-exits.enum";
import { SubjectLocationEntity } from "src/verazial-common-frontend/core/general/subject/domain/entity/subject-location.entity";
import { SubjectEntity } from "src/verazial-common-frontend/core/general/subject/domain/entity/subject.entity";
import { GetMultipleSubjectsByNumIdUseCase } from "src/verazial-common-frontend/core/general/subject/domain/use-cases/get-multiple-subjects-by-num-id.use-case";
import { GetSubjectByNumIdUseCase } from "src/verazial-common-frontend/core/general/subject/domain/use-cases/get-subject-by-num-id.use-case";
import { GetSubjectLocationsBySubjectIdUseCase } from "src/verazial-common-frontend/core/general/subject/domain/use-cases/get-subject-location-by-subject-id.use-case";
import { SaveSubjectLocationUseCase } from "src/verazial-common-frontend/core/general/subject/domain/use-cases/save-subject-location.use-case";
import { UpdateSubjectLocationUseCase } from "src/verazial-common-frontend/core/general/subject/domain/use-cases/update-subject-location.use-case";
import { UpdateSubjectUseCase } from "src/verazial-common-frontend/core/general/subject/domain/use-cases/update-subject.use-case";
import { AuditTrailActions } from "src/verazial-common-frontend/core/models/audit-trail-actions.enum";
import { AuditTrailFields } from "src/verazial-common-frontend/core/models/audit-trail-fields.enum";
import { InsideEnum } from "src/verazial-common-frontend/core/models/inside.enum";
import { OperationStatus } from "src/verazial-common-frontend/core/models/operation-status.interface";
import { Status } from "src/verazial-common-frontend/core/models/status.enum";
import { AuditTrailService, ReasonActionTypeEnum, ReasonTypeEnum } from "src/verazial-common-frontend/core/services/audit-trail.service";
import { ConsoleLoggerService } from "src/verazial-common-frontend/core/services/console-logger.service";
import { LocalStorageService } from "src/verazial-common-frontend/core/services/local-storage.service";
import { NewLocationsService } from "src/verazial-common-frontend/core/services/new-locations.service";
import { BioSignatureResult } from "src/verazial-common-frontend/modules/shared/components/bio-signatures/bio-signatures/bio-signatures.component";
import { ValidatorService } from "src/verazial-common-frontend/modules/shared/services/validator.service";

@Component({
    selector: 'app-transfer-auth-execute',
    templateUrl: './transfer-auth-execute.component.html',
    styleUrl: './transfer-auth-execute.component.css'
})
export class TransferAuthExecuteComponent implements OnInit, OnChanges, OnDestroy {

    // Inputs
    @Input() canReadAndWrite: boolean = false;
    @Input() userIsVerified: boolean = false;
    @Input() transferAuth?: TransferAuthEntity;
    @Input() listLocations: TreeNode[] = [];
    @Input() listLocationsDestiny: TreeNode[] = [];
    @Input() listOfAllSubjects: SubjectEntity[] = [];
    @Input() listOfAllRoles: RoleEntity[] = [];
    @Input() managerSettings?: GeneralSettings;
    // Outputs
    @Output() operationStatus = new EventEmitter<OperationStatus>();

    isLoading: boolean = false;
    editEnabled: boolean = true;
    isExpired: boolean = false;
    isInProgress: boolean = false;
    modified: boolean = false;

    konektorProperties?: KonektorPropertiesEntity;

    canUpdate: boolean = false;

    createUpdateButtonTitle = this.translateService.instant('content.execute');

    stepOptions?: AttributeData[];
    activeIndex: number = 0;

    public form: FormGroup = this.fb.group({
        stepOptions: ['0'],
    });

    AuthStatus = AuthStatus;

    listOfTransferAuthSubjectNumIds: string[] = [];
    listOfTransferAuthSubjects: SubjectEntity[] = [];

    constructor(
        private fb: FormBuilder,
        private translateService: TranslateService,
        private messageService: MessageService,
        private localStorageService: LocalStorageService,
        private loggerService: ConsoleLoggerService,
        public auditTrailService: AuditTrailService,
        private getKonektorPropertiesUseCase: GetKonektorPropertiesUseCase,
        private getTransferAuthByIdUseCase: GetTransferAuthByIdUseCase,
        private updateTransferAuthUseCase: UpdateTransferAuthUseCase,
        private newLocationsService: NewLocationsService,
        private updateTransferAuthUserSubjectUseCase: UpdateTransferAuthUserSubjectUseCase,
        private updateSubjectUseCase: UpdateSubjectUseCase,
        private getSubjectLocationsBySubjectIdUseCase: GetSubjectLocationsBySubjectIdUseCase,
        private updateSubjectLocationUseCase: UpdateSubjectLocationUseCase,
        private saveSubjectLocationUseCase: SaveSubjectLocationUseCase,
        private replaceAttributesUseCase: ReplaceAttributesUseCase,
        private getMultipleSubjectsByNumIdUseCase: GetMultipleSubjectsByNumIdUseCase,

    ) {
        window.addEventListener('beforeunload', this.handleBeforeUnload.bind(this));
    }

    ngOnInit(): void {
        this.getKonektorPropertiesUseCase.execute().subscribe({
            next: (data) => {
                if (data) {
                    this.konektorProperties = data;
                    if (data.apiGatewayGrpc) {
                        this.localStorageService.setApiGatewayURL(data.apiGatewayGrpc);
                    }
                    else {
                        this.localStorageService.destroyApiGatewayURL();
                    }
                }
            },
            error: (e) => {
                this.loggerService.error(e);
            },
        });
        this.isLoading = true;
        if (this.transferAuth && this.transferAuth.id) {
            setTimeout(() => {
                this.getTransferAuthById(this.transferAuth?.id!);
            }, 300);
        } else {
            this.onCancel();
        }
        this.ngOnChanges();


        this.stepOptions = [
            { key: this.translateService.instant('content.listOfPrisoners'), value: "0" },
            { key: this.translateService.instant('content.listOfResponsiblePersonel'), value: "1" },
        ];
    }

    handleBeforeUnload(event: Event) {
      this.ngOnDestroy();
    }

    ngOnDestroy(): void {
      this.updateModified(false);
      // Clean up the timeout if the component is destroyed
      this.auditTrailService.resetInactivityMonitor();
    }

    ngOnChanges(): void {

    }

    getTransferAuthById(id: string) {
        this.getTransferAuthByIdUseCase.execute({ id: id }).then(
            (data) => {
                this.loggerService.debug(data);
                this.transferAuth = data;
                console.log("transferAuth");
                console.log(this.transferAuth);
                let listOfPrisonerNumIds = this.transferAuth?.listOfPrisoners?.map((prisoner) => prisoner.subjectId!);
                let listOfResponsibleNumIds = this.transferAuth?.listOfResponsibleUsers?.map((responsible) => responsible.subjectId!);
                this.listOfTransferAuthSubjectNumIds = [...listOfPrisonerNumIds!, ...listOfResponsibleNumIds!];
                this.getMultipleSubjectsByNumIdUseCase.execute({ numIds: this.listOfTransferAuthSubjectNumIds }).then(
                    (data) => {
                        this.listOfTransferAuthSubjects = data;
                    },
                    (e) => {
                        this.loggerService.error(e);
                    }
                )
                .finally(() => {
                    this.fillFields();
                });
            },
            (e) => {
                this.loggerService.error(e);
            }
        )
        .finally(() => {
            this.fillFields();
        });
    }

    fillFields() {
        if (!this.transferAuth) {
            this.onCancel();
            return;
        }

        this.transferAuth.listOfPrisoners?.forEach((prisoner) => {
            prisoner.userId = this.listOfAllSubjects.find((subject) => subject.id == prisoner.subjectId)?.numId;
        });

        this.transferAuth.listOfResponsibleUsers?.forEach((responsible) => {
            responsible.userId = this.listOfAllSubjects.find((subject) => subject.id == responsible.subjectId)?.numId;
        });

        this.ngOnChanges();
        this.checkAllSignatures();
        this.isLoading = false;
    }

    onCancel() {
        this.transferAuth = undefined;
        this.isLoading = false;
    }

    onClose() {
        this.onCancel();
        let result: OperationStatus = {
            status: Status.SUCCESS,
            message: 'CLOSE'
        }
        this.operationStatus.emit(result);
    }

    async saveTransferAuth() {
        this.isLoading = true;

        let auth = {...this.transferAuth!};

        if (await this.updatePPL()) {

            if(this.transferAuth?.status === AuthStatus.AUTHORIZED)
            {
                auth.status = AuthStatus.IN_PROGRESS;
                auth.actualDepartureDateTime = new Date();
            }
            else if(this.transferAuth?.status === AuthStatus.IN_PROGRESS)
            {
                auth.status = AuthStatus.COMPLETED;
                auth.isCompleted = true;
                auth.actualArrivalDateTime = new Date();
            }
            this.getTransferAuthByIdUseCase.execute({ id: auth.id! }).then(
                (data) => {
                    const at_attributes: ExtraData[] = [
                        { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(data) },
                        { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(auth) },
                    ];
                    this.auditTrailService.auditTrailSelectReason(
                        ReasonTypeEnum.CONFIG,
                        AuditTrailActions.MOD_TRANSFER_AUTHORIZATION,
                        ReasonActionTypeEnum.UPDATE,
                        () => {
                            this.updateTransferAuthUseCase.execute({ transferAuth: auth! }).then(
                                (data) => {
                                    let status: OperationStatus = {
                                        status: Status.SUCCESS,
                                        message: this.translateService.instant('messages.success_transfer_authorization_updated')
                                    };
                                    this.operationStatus.emit(status);
                                },
                                (e) => {
                                    this.loggerService.error(e);
                                }
                            )
                        },
                        at_attributes
                    );
                },
                (e) => {
                    this.loggerService.error(e);
                    let responseStatus: OperationStatus = {
                        status: Status.ERROR,
                        message: e.message
                    };
                    this.operationStatus.emit(responseStatus);
                    const at_attributes: ExtraData[] = [
                        { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
                        { name: AuditTrailFields.RECORD_ID, value: auth.id!.toString() },
                    ];
                    this.auditTrailService.registerAuditTrailAction(
                        this.localStorageService.getUser().numId,
                        AuditTrailActions.GET_TRANSFER_AUTHORIZATION_BY_ID,
                        0,
                        'ERROR',
                        '',
                        at_attributes
                    );
                }
            );
        }
    }


    async updatePPL(): Promise<boolean> {
        try {
            if (!this.transferAuth?.listOfPrisoners) return false;

            for (const prisoner of this.transferAuth.listOfPrisoners) {
                const subject = this.listOfAllSubjects.find(s => s.numId === prisoner.userId);
                if (!subject) return false;

                if(this.transferAuth?.status === AuthStatus.AUTHORIZED)
                {
                    subject.inside = InsideEnum.TRANSFER;
                    subject.entryDate = undefined;
                    subject.exitDate = new Date();
                    subject.visitTime = this.transferAuth.plannedArrivalDateTime;
                    subject.locationId = this.transferAuth.destinyLocationId;
                    subject.center = this.newLocationsService.getRootKeyPath(
                        this.transferAuth.destinyLocationId!,
                        this.listLocationsDestiny
                    );
                }
                else if(this.transferAuth?.status === AuthStatus.IN_PROGRESS)
                {
                    subject.inside = InsideEnum.INSIDE;
                    subject.entryDate = undefined;
                    subject.exitDate = undefined;
                    //center and location already set
                }

                await this.updateSubjectUseCase.execute({ subject });
                const listOfLocations = await this.getSubjectLocationsBySubjectIdUseCase.execute({ id: subject.id! });


                if(this.transferAuth?.status === AuthStatus.AUTHORIZED)
                {
                    const actualLocation = listOfLocations.find(loc => loc.actualLocation);

                    if (actualLocation) {
                        actualLocation.actualLocation = false;
                        actualLocation.exitDate = new Date();
                        await this.updateSubjectLocationUseCase.execute({ subjectLocation: actualLocation });
                    }

                    const newLocation = new SubjectLocationEntity();
                    newLocation.subjectId = subject.id!;
                    newLocation.locationId = this.transferAuth.destinyLocationId;
                    newLocation.entryDate = new Date();
                    newLocation.actualLocation = false;
                    newLocation.pending = true;

                    if (actualLocation) {
                        newLocation.classification = actualLocation.classification;
                        newLocation.regime = actualLocation.regime;
                    }

                    await this.saveSubjectLocationUseCase.execute({ subjectLocation: newLocation });
                }
                else
                {
                    const pendingLocation = listOfLocations.find(loc => loc.pending);

                    if(pendingLocation)
                    {
                        pendingLocation.pending = false;
                        pendingLocation.actualLocation = true;
                        pendingLocation.entryDate = new Date();
                        await this.updateSubjectLocationUseCase.execute({ subjectLocation: pendingLocation });
                    }
                }

                var newExit = new EntriesExitsEntity();
                newExit.subjectId = subject.id!;
                newExit.entryDate = new Date();
                newExit.transferId = this.transferAuth.id;
                newExit.roleId = this.managerSettings?.continued1?.prisonsSettings?.prisonerProfileId;
                newExit.transferLimitDate = this.transferAuth.plannedArrivalDateTime;

                if(this.transferAuth?.status === AuthStatus.AUTHORIZED)
                {
                    newExit.destinyLocationId = this.transferAuth.destinyLocationId;
                    newExit.locationId = this.transferAuth.originLocationId;
                    newExit.type = entriesExitsEnum.TRANSFER_EXIT;
                    newExit.subjectSignatureTech = prisoner.actualDepartureTech;
                    newExit.subjectSignatureDate = prisoner.actualDepartureDateTime;
                    newExit.userSignatureNumId = this.transferAuth!.listOfResponsibleUsers![0].userId;
                    newExit.userSignatureTech = this.transferAuth!.listOfResponsibleUsers![0].actualDepartureTech;
                    newExit.userSignatureDate = this.transferAuth!.listOfResponsibleUsers![0].actualDepartureDateTime;
                }
                else
                {
                    //Mirar lo que hay que registrar cuando se hace una entrada de transfer
                    newExit.locationId = this.transferAuth.destinyLocationId;
                    newExit.type = entriesExitsEnum.TRANSFER_ENTRY;
                    newExit.subjectSignatureTech = prisoner.actualArrivalTech;
                    newExit.subjectSignatureDate = prisoner.actualArrivalDateTime;
                    newExit.userSignatureNumId = this.transferAuth!.listOfResponsibleUsers![0].userId;
                    newExit.userSignatureTech = this.transferAuth!.listOfResponsibleUsers![0].actualArrivalTech;
                    newExit.userSignatureDate = this.transferAuth!.listOfResponsibleUsers![0].actualArrivalDateTime;
                }

                //REGISTRAR SALIDA
                await this.auditTrailService.registerEntryExitAuditTrailAction(newExit);
                //console.log(newExit);

                //UPDATE Attributes al biometrico directamente
                const request = new ReplaceAttributesRequestEntity(subject.biometricId!,
                    [
                        { name: 'CENTER', value: subject.center! },
                        { name: 'PRESENTABSENT', value: subject.inside! }
                    ]
                );
                await this.replaceAttributesUseCase.execute(request);
            }

            return true;
        } catch (e) {
            this.loggerService.error(e);
            const at_attributes: ExtraData[] = [{ name: AuditTrailFields.ERROR, value: JSON.stringify(e) }];
            this.auditTrailService.registerAuditTrailAction(
                this.localStorageService.getUser().numId,
                AuditTrailActions.GET_SUBJECT_LOCATION_BY_SUBJECT_ID,
                0,
                'ERROR',
                '',
                at_attributes
            );
            this.messageService.add({
                severity: 'error',
                summary: this.translateService.instant('content.errorTitle'),
                detail: `${this.translateService.instant('messages.error_general')}: ${e}`,
                life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
            });
            return false;
        }
    }


    isValidDate(dateString: string): boolean {
        const date = new Date(dateString);
        return !isNaN(date.getTime()) && date.toISOString().split('T')[0] != (new Date(0)).toISOString().split('T')[0];
    }

    userAuthSignatureResult(event: BioSignatureResult, prisoner: TransferAuthUserSubjectEntity ) {
        if(this.transferAuth?.status === AuthStatus.AUTHORIZED)
        {
            prisoner.actualDepartureTech = event.tech;
            prisoner.actualDepartureDateTime = new Date();
        }
        else if(this.transferAuth?.status === AuthStatus.IN_PROGRESS)
        {
            prisoner.actualArrivalTech = event.tech;
            prisoner.actualArrivalDateTime = new Date();
        }


        console.log("prisoner");
        console.log(prisoner);

        this.updateTransferAuthUserSubjectUseCase.execute({ transferAuthUserSubject: prisoner }).then(
            (data) => {
                this.loggerService.debug(data);
                /*let status: OperationStatus = {
                    status: Status.SUCCESS,
                    message: this.translateService.instant('messages.success_transfer_authorization_updated')
                }*/
                //this.operationStatus.emit(status);
            },
            (e) => {
                this.loggerService.error(e);
                /*let status: OperationStatus = {
                    status: Status.ERROR,
                    message: this.translateService.instant('messages.error_transfer_authorization_updated')
                }*/

                this.messageService.add({
                    severity: 'error',
                    summary: this.translateService.instant('content.errorTitle'),
                    detail: `${this.translateService.instant('messages.error_transfer_authorization_updated')}: ${e}`,
                    life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
                });

                //this.operationStatus.emit(status);
            }
        );

        this.checkAllSignatures();
    }

    checkAllSignatures() {
        let allPrisonersSigned = this.transferAuth?.listOfPrisoners?.every((prisoner) => {
            return this.hasAuthSignaturePrisioner(prisoner);
        });

        let allResponsiblesSigned = this.transferAuth?.listOfResponsibleUsers?.every((responsible) => {
            return this.hasAuthSignaturePrisioner(responsible);
        });

        if(allPrisonersSigned && allResponsiblesSigned) {
            this.canUpdate = true;
        }
    }

    getSignatureData(prisoner: TransferAuthUserSubjectEntity) : BioSignatureResult | undefined {
        if(this.transferAuth?.status === AuthStatus.AUTHORIZED)
        {
            if(!prisoner.actualDepartureTech)
            {
                return undefined;
            }
            else
            {
                return {
                    tech: prisoner.actualDepartureTech,
                    date: prisoner.actualDepartureDateTime,
                    numId: prisoner.userId
                };
            }
        }
        else if(this.transferAuth?.status === AuthStatus.IN_PROGRESS)
        {
            if(!prisoner.actualArrivalTech)
            {
                return undefined;
            }
            else
            {
                return {
                    tech: prisoner.actualArrivalTech,
                    date: prisoner.actualArrivalDateTime,
                    numId: prisoner.userId
                };
            }
        }
        else
        {
            return undefined;
        }
    }

    getTransferAuthStatus(): AuthStatus {
        if (this.transferAuth?.status != '' && this.transferAuth?.status != null && this.transferAuth?.status != undefined) {
            if (this.transferAuth?.isCompleted || this.transferAuth?.isCancelled) {
                return this.transferAuth?.status as AuthStatus;
            }
            if (this.transferAuth.status == AuthStatus.IN_PROGRESS || this.transferAuth.status == AuthStatus.EXPIRED) {
                return this.transferAuth?.status as AuthStatus;
            }
        }
        if (this.transferAuth?.isCancelled) {
            return AuthStatus.CANCELLED;
        }
        if (this.transferAuth?.authUserSignatureTech) {
            return AuthStatus.AUTHORIZED;
        }
        return AuthStatus.CREATED;
    }

    hasAuthSignaturePrisioner(prisoner: TransferAuthUserSubjectEntity): boolean {
        if(this.transferAuth?.status === AuthStatus.AUTHORIZED)
            return !!prisoner?.actualDepartureTech;
        else if (this.transferAuth?.status === AuthStatus.IN_PROGRESS)
            return !!prisoner?.actualArrivalTech;
        else
            return false;
    }

    getSubjectNames(numId: string): string {
        let subject = this.listOfAllSubjects.find((subject) => subject.numId == numId);
        if (!subject) {
            return '';
        }
        return subject?.names + ' ' + subject?.lastNames;
    }


    /**
     * Convert an array of form controls to an object
     * @param arr Array of form controls
     * @returns Object with the form controls
     */
    convertFormControlArrayToObject(arr: any[]) {
      const result: any = {};
      arr.forEach(item => {
        result[item.key] = item;
      });
      return result;
    }

    updateModified(modified: boolean) {
      this.modified = modified;
    }

    onActiveTabIndexChange(event: any){
        this.activeIndex = Number(event.value);
        this.form.get('stepOptions')?.setValue(this.activeIndex.toString());
      }
}