import { DOCUMENT } from '@angular/common';
import { Component, EventEmitter, Inject, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { FormControl, FormGroup } from '@angular/forms';
import { Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { MessageService, ConfirmationService, MenuItem, FilterService } from 'primeng/api';
import { Table } from 'primeng/table';
import { TaskFlowEntity } from 'src/verazial-common-frontend/core/general/flow/domain/entity/task-flow.entity';
import { GetAllPublishedTaskFlowsUseCase } from 'src/verazial-common-frontend/core/general/flow/domain/use-cases/task-flow/get-all-published-task-flows.use-case';
import { AssignmentType } from 'src/verazial-common-frontend/core/general/assignment//group/common/enums/assignment-type.enum';
import { AssignmentInfo } from 'src/verazial-common-frontend/core/general/assignment//group/common/interfaces/assignment-info';
import { AssignmentMapper } from 'src/verazial-common-frontend/core/general/assignment//group/data/mapper/assignment.mapper';
import { AssignmentElementEntity } from 'src/verazial-common-frontend/core/general/assignment//group/domain/entity/assignment-elements.entity';
import { AssignmentEntity } from 'src/verazial-common-frontend/core/general/assignment//group/domain/entity/assignment.entity';
import { CreateAssignmentUseCase } from 'src/verazial-common-frontend/core/general/assignment//group/domain/use-cases/create-assignment.use-case';
import { DeleteAssignmentByIdUseCase } from 'src/verazial-common-frontend/core/general/assignment//group/domain/use-cases/delete-assignment-by-id.use-case';
import { DeleteAssignmentElementByIdUseCase } from 'src/verazial-common-frontend/core/general/assignment//group/domain/use-cases/delete-assignment-element-by-id.use-case';
import { GroupCategoryEntity } from 'src/verazial-common-frontend/core/general/assignment/categories/domain/entity/group-category.entity';
import { GetAllGroupsCategoriesUseCase } from 'src/verazial-common-frontend/core/general/assignment/categories/domain/use-cases/get-all-groups-categories.use-case';
import { ExtraData } from 'src/verazial-common-frontend/core/general/actionsV2/domain/entity/extra-data.interface';
import { AuditTrailActions } from 'src/verazial-common-frontend/core/models/audit-trail-actions.enum';
import { AuditTrailFields } from 'src/verazial-common-frontend/core/models/audit-trail-fields.enum';
import { AuditTrailService, ReasonActionTypeEnum, ReasonTypeEnum } from 'src/verazial-common-frontend/core/services/audit-trail.service';
import { ConsoleLoggerService } from 'src/verazial-common-frontend/core/services/console-logger.service';
import { LocalStorageService } from 'src/verazial-common-frontend/core/services/local-storage.service';

@Component({
  selector: 'app-list-assignments',
  templateUrl: './list-assignments.component.html',
  styleUrl: './list-assignments.component.css',
  providers: [MessageService, ConfirmationService]
})
export class ListAssignmentsComponent implements OnInit, OnDestroy {
  @Input() readAndWritePermissions: boolean = false;
  @Input() listAssignments: AssignmentEntity[] = [];
  @Output() onNewAssignment = new EventEmitter<AssignmentEntity>();
  @Output() onEditAssignment = new EventEmitter<AssignmentEntity>();

  assignmentMapper = new AssignmentMapper();

  showNewAssignmentDialog: boolean = false;
  showAssignmentDetailsDialog: boolean = false;

  assignmentInfoData: AssignmentInfo | undefined;
  assignmentConfigData: AssignmentEntity | undefined;
  selectedFlows: AssignmentElementEntity[] = [];
  selectedCategories: AssignmentElementEntity[] = [];
  selectedConfigSchedules: AssignmentElementEntity[] = [];
  selectedConfigUsers: AssignmentElementEntity[] = [];

  selectedObjectFlows: TaskFlowEntity[] = [];
  selectedObjectCategories: GroupCategoryEntity[] = [];

  disableContinue: boolean = true;

  items: MenuItem[] | undefined;
  activeIndex: number = 0;

  isNewAssignment: boolean = false;

  isTabActive: boolean = false

  dialogBoxTitle: string = "";

  secondaryButtonLabel: string = "";

  selectedAssignment: AssignmentEntity | undefined;

  assigmentType: AssignmentType | undefined;

  listGroupsCategories: GroupCategoryEntity[] = [];
  listTaskFlows: TaskFlowEntity[] = [];

  resquestTaskFlowsCompleted: boolean = false;
  resquestCategoriesCompleted: boolean = false;
  resquestGroupsCompleted: boolean = false;
  isLoading = true;
  showFlowCategoryDialog: boolean = false;

  flowCategoryMessage: string = ""
  showCreateFlowButton: boolean = false;
  showCreateCategoryButton: boolean = false;

  assignmentName: string = "";

  searchValue: string | undefined;

  // Date Range Filter
  formGroup: FormGroup = new FormGroup({
    date: new FormControl<Date[] | null>(null)
  });
  dateFilterValues = {
    startDate: null,
    endDate: null
  };
  rangeDates: Date[] | null = null;

  confirmDialogTimeoutLimit: number = 0;
  startCheckingInactivity: boolean = false;

  constructor(
    @Inject(DOCUMENT) private document: any,
    private router: Router,
    private messageService: MessageService,
    private translateService: TranslateService,
    private confirmationService: ConfirmationService,
    private createAssignmentUseCase: CreateAssignmentUseCase,
    private deleteAssignmentByIdUseCase: DeleteAssignmentByIdUseCase,
    private getAllPublishedTaskFlowsUseCase: GetAllPublishedTaskFlowsUseCase,
    private getAllGroupsCategoriesUseCase: GetAllGroupsCategoriesUseCase,
    private deleteAssignmentElementByIdUseCase: DeleteAssignmentElementByIdUseCase,
    private filterService: FilterService,
    private localStorageService: LocalStorageService,
    private loggerService: ConsoleLoggerService,
    public auditTrailService: AuditTrailService,
  ) {
    this.filterService.register('customDateRange', (value: any, filter: any): boolean => {
      if (!filter || (!filter.startDate && !filter.endDate)) {
        return true; // If no filter, show all
      }
      const dateValue = new Date(value).getTime();
      const startDate = filter.startDate ? new Date(filter.startDate).getTime() : null;
      const endDate = filter.endDate ? new Date(filter.endDate).getTime() : null;
      if (startDate && endDate) {
        return dateValue >= startDate && dateValue <= endDate;
      } else if (startDate) {
        return dateValue >= startDate;
      } else if (endDate) {
        return dateValue <= endDate;
      }
      return false;
    });
  }

  ngOnInit(): void {
    this.setStepOptions();
    this.getTaskFlows();
    this.getGroupCategories();
  }

  ngOnDestroy() {
    // Clean up the timeout if the component is destroyed
    this.closeConfirmationDialog();
    this.auditTrailService.resetInactivityMonitor();
  }

  resetInactivityMonitor() {
    this.startCheckingInactivity = false;
    this.confirmDialogTimeoutLimit = 0;
  }

  setStepOptions() {
    this.items = [
      {
        label: this.translateService.instant('assignment.general'),
      },
      {
        label: this.translateService.instant('assignment.flows'),
      },
      {
        label: this.translateService.instant('assignment.categories'),
      },
      {
        label: this.translateService.instant('assignment.configuration'),
      },
    ];
  }

  getTaskFlows() {
    this.getAllPublishedTaskFlowsUseCase.execute().then(
      (data) => {
        this.listTaskFlows = data;
        this.resquestTaskFlowsCompleted = true;
        this.loadingStatus();
      },
      (e) => {
        this.loggerService.error(e);
        const at_attributes: ExtraData[] = [{ name: AuditTrailFields.ERROR, value: JSON.stringify(e) }];
        this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.GET_ALL_PUBLISHED_TASK_FLOWS, 0, 'ERROR', '', at_attributes);
      }
    )
  }

  getGroupCategories() {
    this.getAllGroupsCategoriesUseCase.execute().then(
      (data) => {
        this.listGroupsCategories = data;
        this.resquestCategoriesCompleted = true;
        this.loadingStatus();
      },
      (e) => {
        this.loggerService.error(e);
        const at_attributes: ExtraData[] = [{ name: AuditTrailFields.ERROR, value: JSON.stringify(e) }];
        this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.GET_ALL_GROUP_CATEGORIES, 0, 'ERROR', '', at_attributes);
      })
  }

  loadingStatus() {
    if (this.resquestCategoriesCompleted && this.resquestTaskFlowsCompleted) {
      this.isLoading = false;
    }
  }

  createNewAssignment() {
    this.setStepOptions();
    if (this.listTaskFlows.length == 0 && this.listGroupsCategories.length == 0) {
      this.flowCategoryMessage = "messages.category_flow_required";
      this.showFlowCategoryDialog = true;
      this.showCreateCategoryButton = true;
      this.showCreateFlowButton = true;
      return
    } else if (this.listTaskFlows.length == 0) {
      this.flowCategoryMessage = "messages.flow_required";
      this.showFlowCategoryDialog = true;
      this.showCreateCategoryButton = false;
      this.showCreateFlowButton = true;
      return
    } else if (this.listGroupsCategories.length == 0) {
      this.flowCategoryMessage = "messages.category_required";
      this.showFlowCategoryDialog = true;
      this.showCreateCategoryButton = true;
      this.showCreateFlowButton = false;
      return
    }

    this.secondaryButtonLabel = this.translateService.instant('next');
    this.disableContinue = true;
    this.isNewAssignment = true;
    this.showNewAssignmentDialog = true;
    this.dialogBoxTitle = this.translateService.instant('assignment.new_assignment')
  }

  editAssignment(assignment: AssignmentEntity) {
    this.setStepOptions();
    this.selectedAssignment = assignment;
    this.dialogBoxTitle = this.translateService.instant('assignment.update_assignment')
    this.secondaryButtonLabel = this.translateService.instant('save');
    this.disableContinue = false;
    this.isNewAssignment = false;
    this.showNewAssignmentDialog = true;

    if (assignment.elements && assignment.elements.length > 0) {
      this.selectedCategories = assignment.elements?.filter(element => element.type == AssignmentType.CATEGORY);
      this.selectedFlows = assignment.elements?.filter(element => element.type == AssignmentType.FLOW);
      this.selectedConfigSchedules = assignment.elements?.filter(element => element.type == AssignmentType.CONFIG_SCHEDULES);
      this.selectedConfigUsers = assignment.elements?.filter(element => element.type == AssignmentType.CONFIG_USERS);
    }

    this.assignmentInfoData = {
      id: assignment.id,
      name: assignment.name ? assignment.name : "",
      description: assignment.description ? assignment.description : "",
      isRequiredWithinSchedule: assignment.isRequiredWithinSchedule ? assignment.isRequiredWithinSchedule : false,
      alertIfAllPerformedWithinSchedule: assignment.alertIfAllPerformedWithinSchedule ? assignment.alertIfAllPerformedWithinSchedule : false,
      alertIfNotPerformedWithinSchedule: assignment.alertIfNotPerformedWithinSchedule ? assignment.alertIfNotPerformedWithinSchedule : false,
      alertIfPerformedOutsideSchedule: assignment.alertIfPerformedOutsideSchedule ? assignment.alertIfPerformedOutsideSchedule : false
    }

    this.assignmentConfigData = new AssignmentEntity();
    this.assignmentConfigData.isRequiredWithinSchedule = assignment.isRequiredWithinSchedule;
    this.assignmentConfigData.alertIfAllPerformedWithinSchedule = assignment.alertIfAllPerformedWithinSchedule;
    this.assignmentConfigData.alertIfNotPerformedWithinSchedule = assignment.alertIfNotPerformedWithinSchedule;
    this.assignmentConfigData.alertIfPerformedOutsideSchedule = assignment.alertIfPerformedOutsideSchedule;
  }

  onAddCategory() {
    this.router.navigate(['/proccess/categories']);
  }

  onAddFlow() {
    this.router.navigate(['/proccess/flow']);
  }

  deleteAssignment(assignment: AssignmentEntity) {
    this.confirmationService.confirm({
      message: this.translateService.instant('messages.message_remove') + " <b>" + assignment.name + `</b>?`,
      header: this.translateService.instant('assignment.delete_assignment'),
      icon: 'pi pi-exclamation-triangle',
      acceptIcon: "none",
      rejectIcon: "none",
      rejectButtonStyleClass: "p-button-text",
      acceptButtonStyleClass: "ng-confirm-button",
      acceptLabel: this.translateService.instant('delete'),
      rejectLabel: this.translateService.instant('no'),
      accept: () => {
        this.resetInactivityMonitor();
        const at_attributes: ExtraData[] = [
          { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(assignment) },
        ];
        this.auditTrailService.auditTrailSelectReason(ReasonTypeEnum.CONFIG, AuditTrailActions.DEL_ASG, ReasonActionTypeEnum.DELETE, () => { this.deleteAssignmentById(assignment); }, at_attributes);
      },
      reject: () => {
        this.resetInactivityMonitor();
      }
    });

    this.confirmDialogTimeoutLimit = (this.localStorageService.getSessionSettings()?.timeoutMessages ?? 10) * 1000
    this.startCheckingInactivity = false;
    setTimeout(() => this.startCheckingInactivity = true, 0);
  }

  deleteAssignmentById(assignment: AssignmentEntity) {
    const id = assignment.id!!;
    this.deleteAssignmentByIdUseCase.execute({ id: id }).then(
      (response) => {
        if (response) {
          this.messageService.add({
            severity: 'success', summary: this.translateService.instant("titles.success_operation"), detail: this.translateService.instant("messages.success_general"),
            life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
          });
          this.listAssignments = [...this.listAssignments.filter((v) => v.id != id)];
        }
      },
      (e) => {
        this.loggerService.error(e);
        this.messageService.add({
          severity: 'error', summary: this.translateService.instant("titles.error_operation"), detail: e.message,
          life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
        });
        this.loggerService.error(e);
        const at_attributes: ExtraData[] = [
          { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
          { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(assignment) },
        ];
        this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.DEL_ASG, 0, 'ERROR', '', at_attributes);
      }
    )
  }

  getNumElements(elements: AssignmentElementEntity[], type: string): number {
    let value: number = 0;
    if (type == "FLOW") {
      value = elements?.filter(element => element.type == AssignmentType.FLOW).length;
    } else if (type == "CATEGORY") {
      value = elements.filter(element => element.type == AssignmentType.CATEGORY).length;
    }
    return value;
  }

  onCancel() {
    this.showNewAssignmentDialog = false;
    this.activeIndex = 0;
  }

  onNext() {
    this.activeIndex += 1;
  }

  onBack() {
    this.activeIndex -= 1;
    if (!this.isNewAssignment) {
      this.setStyleButton(this.activeIndex);
    }
  }

  assignmentInfoDataValid(event: boolean) {
    this.disableContinue = !event;
  }

  getGroupData(event: AssignmentInfo) {
    this.assignmentInfoData = event;
  }

  getGroupConfigData(event: AssignmentEntity) {
    this.assignmentConfigData = event;
    this.selectedConfigSchedules = this.assignmentConfigData.elements?.filter(element => element.type == AssignmentType.CONFIG_SCHEDULES) ?? [];
    this.selectedConfigUsers = this.assignmentConfigData.elements?.filter(element => element.type == AssignmentType.CONFIG_USERS) ?? [];
  }

  getSelectedFlows(flows: TaskFlowEntity[]) {
    this.selectedFlows = [];
    flows.forEach(flow => {
      const assignmentElement = new AssignmentElementEntity();

      assignmentElement.type = AssignmentType.FLOW;
      assignmentElement.subtype = undefined;
      assignmentElement.elementId = flow.id;

      this.selectedFlows.push(assignmentElement);
    })
  }

  getSelectedCategories(categories: GroupCategoryEntity[]) {
    this.selectedCategories = [];
    categories.forEach(category => {
      const assignmentElement = new AssignmentElementEntity();

      assignmentElement.type = AssignmentType.CATEGORY;
      assignmentElement.subtype = category.type;
      assignmentElement.elementId = category.id;

      this.selectedCategories.push(assignmentElement);
    })
  }

  onFirstTabClick() {
    this.setStyleButton(0);
  }

  onSecondTabClick() {
    if (this.disableContinue == false) {
      this.setStyleButton(1);
    }
  }

  onThirdTabClick() {
    if (this.disableContinue == false) {
      this.setStyleButton(2);
    }
  }

  onFourthTabClick() {
    if (this.disableContinue == false) {
      this.setStyleButton(3);
    }
  }

  onUpdate() {
    let assignment = new AssignmentEntity();

    assignment.id = this.selectedAssignment?.id
    assignment.name = this.assignmentInfoData?.name!!;
    assignment.description = this.assignmentInfoData?.description;
    assignment.isRequiredWithinSchedule = this.assignmentConfigData?.isRequiredWithinSchedule;
    assignment.alertIfAllPerformedWithinSchedule = this.assignmentConfigData?.alertIfAllPerformedWithinSchedule;
    assignment.alertIfNotPerformedWithinSchedule = this.assignmentConfigData?.alertIfNotPerformedWithinSchedule;
    assignment.alertIfPerformedOutsideSchedule = this.assignmentConfigData?.alertIfPerformedOutsideSchedule;
    assignment.elements = this.selectedFlows.concat(this.selectedCategories).concat(this.selectedConfigSchedules).concat(this.selectedConfigUsers);

    let updatedListOfElements = assignment.elements.map(a => {
      const replacement = this.selectedAssignment?.elements?.find(b => b.elementId == a.elementId && b.type == a.type);
      return replacement ? replacement : a;
    });

    assignment.elements = updatedListOfElements;

    let elementsToRemove = this.selectedAssignment?.elements?.filter(element =>
      !assignment.elements?.some(item => element.elementId == item.elementId && element.type == item.type)
    )

    if (elementsToRemove && elementsToRemove.length > 0) {
      this.deleteAssigmentElements(elementsToRemove);
    }

    const at_attributes: ExtraData[] = [
      { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(assignment) },
    ];
    this.auditTrailService.auditTrailSelectReason(ReasonTypeEnum.CONFIG, AuditTrailActions.ADD_ASG, ReasonActionTypeEnum.CREATE, () => {
      this.createAssignmentUseCase.execute({ assignment: assignment }).then(
        (response) => {
          if (response) {
            this.listAssignments = [...this.listAssignments.filter((v) => v.id != response.id)];
            this.listAssignments = [...this.listAssignments, response];
            this.messageService.add({
              severity: 'success', summary: this.translateService.instant("titles.success_operation"), detail: this.translateService.instant("messages.success_general"),
              life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
            });
          }
          this.showNewAssignmentDialog = false;
        },
        (e) => {
          this.messageService.add({
            severity: 'error', summary: this.translateService.instant("titles.error_operation"), detail: e.message,
            life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
          });
          this.loggerService.error(e);
          const at_attributes: ExtraData[] = [
            { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
            { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(assignment) },
          ];
          this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.ADD_ASG, 0, 'ERROR', '', at_attributes);
        }
      )
    }, at_attributes);
  }

  deleteAssigmentElements(elements: AssignmentElementEntity[]) {
    const at_attributes: ExtraData[] = [
      { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(elements) },
    ];
    this.auditTrailService.auditTrailSelectReason(ReasonTypeEnum.CONFIG, AuditTrailActions.DEL_ASG_ELM, ReasonActionTypeEnum.DELETE, () => {
      this.deleteAssignmentElementByIdUseCase.execute({ elements: elements }).then(
        (_) => { },
        (e) => {
          this.loggerService.error(e);
          const at_attributes: ExtraData[] = [
            { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
            { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(elements) },
          ];
          this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.DEL_ASG_ELM, 0, 'ERROR', '', at_attributes);
        }
      )
    }, at_attributes);
  }

  setStyleButton(index: number) {
    this.activeIndex = index;
    this.setStyle("first", index == 0);
    this.setStyle("second", index == 1);
    this.setStyle("third", index == 2);
    this.setStyle("fourth", index == 3);
  }

  setStyle(id: string, isActive: boolean) {
    if (isActive) {
      let element = document.getElementById(id) as HTMLElement;
      element.style.background = '#818EA1';
      element.style.color = '#FFFFFF';
    } else {
      let element = document.getElementById(id) as HTMLElement;
      element.style.background = '#FFFFFF';
      element.style.color = '#495057';
    }

  }

  resetStates() {
    this.activeIndex = 0;
    this.selectedCategories = [];
    this.selectedFlows = [];
    this.selectedConfigSchedules = [];
    this.selectedConfigUsers = [];
    this.assignmentInfoData = undefined;
    this.disableContinue = true;
  }

  onSave() {
    let assignment = new AssignmentEntity();
    assignment.name = this.assignmentInfoData?.name!!;
    assignment.description = this.assignmentInfoData?.description;
    assignment.isRequiredWithinSchedule = this.assignmentConfigData?.isRequiredWithinSchedule;
    assignment.alertIfAllPerformedWithinSchedule = this.assignmentConfigData?.alertIfAllPerformedWithinSchedule;
    assignment.alertIfNotPerformedWithinSchedule = this.assignmentConfigData?.alertIfNotPerformedWithinSchedule;
    assignment.alertIfPerformedOutsideSchedule = this.assignmentConfigData?.alertIfPerformedOutsideSchedule;
    assignment.elements = this.selectedFlows.concat(this.selectedCategories).concat(this.selectedConfigSchedules).concat(this.selectedConfigUsers);

    const at_attributes: ExtraData[] = [
      { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(assignment) },
    ];
    this.auditTrailService.auditTrailSelectReason(ReasonTypeEnum.CONFIG, AuditTrailActions.ADD_ASG, ReasonActionTypeEnum.CREATE, () => {
      this.createAssignmentUseCase.execute({ assignment: assignment }).then(
        (data) => {
          if (data) {
            this.listAssignments = [...this.listAssignments, data];
            this.messageService.add({
              severity: 'success', summary: this.translateService.instant("titles.success_operation"), detail: this.translateService.instant("messages.success_general"),
              life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
            });
          }
          this.showNewAssignmentDialog = false;
        },
        (e) => {
          this.messageService.add({
            severity: 'error', summary: this.translateService.instant("titles.error_operation"), detail: e.message,
            life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
          });
          this.loggerService.error(e);
          const at_attributes: ExtraData[] = [
            { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
            { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(assignment) },
          ];
          this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.ADD_ASG, 0, 'ERROR', '', at_attributes);
        }
      )
    }, at_attributes);

  }

  showAssignmentDetails(assignment: AssignmentEntity) {

    this.selectedObjectFlows = this.listTaskFlows.filter(flow => assignment.elements?.some(element => flow.id == element.elementId));
    this.selectedObjectCategories = this.listGroupsCategories.filter(category => assignment.elements?.some(element => category.id == element.elementId));

    this.selectedAssignment = assignment;
    this.showAssignmentDetailsDialog = true;
    this.assignmentName = assignment.name!;
  }

  closeAssignmentDetails() {
    this.showAssignmentDetailsDialog = false;
  }

  /* Search */
  onFilter(event: any, dt: Table) {
    if (!event.filters['updatedAt'].value) {
      this.rangeDates = null;
      this.formGroup.reset();
    }
  }

  /* Date Range Filter */
  applyDateRangeFilter(dt: Table, field: string) {
    this.rangeDates = this.formGroup.get('date')?.value;
    dt.filter({
      startDate: this.rangeDates ? this.rangeDates[0] : null,
      endDate: this.rangeDates ? this.rangeDates[1] : null
    }, field, 'customDateRange');
  }

  closeConfirmationDialog() {
    this.confirmationService.close();
    this.resetInactivityMonitor();
  }
}
