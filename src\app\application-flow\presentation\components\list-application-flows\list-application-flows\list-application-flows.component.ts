import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit, Output } from '@angular/core';
import { <PERSON><PERSON><PERSON>er, FormControl, FormGroup, Validators } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { MessageService, ConfirmationService, FilterService } from 'primeng/api';
import { Table } from 'primeng/table';
import { ValidatorService } from 'src/verazial-common-frontend/modules/shared/services/validator.service';
import { ExtraData } from 'src/verazial-common-frontend/core/general/actionsV2/domain/entity/extra-data.interface';
import { ApplicationEntity } from 'src/verazial-common-frontend/core/general/application/domain/entities/application.entity';
import { DeleteApplicationByIdUseCase } from 'src/verazial-common-frontend/core/general/application/domain/use-cases/delete-application-by-id.use-case';
import { GetAllApplicationsUseCase } from 'src/verazial-common-frontend/core/general/application/domain/use-cases/get-all-applications.use-case';
import { UpdateApplicationByIdUseCase } from 'src/verazial-common-frontend/core/general/application/domain/use-cases/update-application-by-id.use-case';
import { DataSourceEntity } from 'src/verazial-common-frontend/core/general/data-source/domain/entities/data-source.entity';
import { GetAllDataSourcesUseCase } from 'src/verazial-common-frontend/core/general/data-source/domain/use-cases/get-all-data-sources.use-case';
import { AuditTrailActions } from 'src/verazial-common-frontend/core/models/audit-trail-actions.enum';
import { AuditTrailFields } from 'src/verazial-common-frontend/core/models/audit-trail-fields.enum';
import { GenericKeyValue } from 'src/verazial-common-frontend/core/models/key-value.interface';
import { AuditTrailService, ReasonActionTypeEnum, ReasonTypeEnum } from 'src/verazial-common-frontend/core/services/audit-trail.service';
import { ConsoleLoggerService } from 'src/verazial-common-frontend/core/services/console-logger.service';
import { LocalStorageService } from 'src/verazial-common-frontend/core/services/local-storage.service';
import { AppRegistryEntity } from 'src/verazial-common-frontend/core/general/app-registry/domain/entities/app-registry.entity';
import { GetAppRegistriesUseCase } from 'src/verazial-common-frontend/core/general/app-registry/domain/use-cases/get-app-registries.use-case';

@Component({
  selector: 'app-list-application-flows',
  templateUrl: './list-application-flows.component.html',
  styleUrl: './list-application-flows.component.css'
})
export class ListApplicationFlowsComponent implements OnInit, OnDestroy {
  // Outputs
  @Output() onNewDataFlow = new EventEmitter<boolean>();
  @Output() onEdit = new EventEmitter<any>();

  selectedData: any;

  dataSources: DataSourceEntity[] = [];

  listApplications: ApplicationEntity[] = [];

  showSaveApplicationDialog: boolean = false;

  selectedDataSource: DataSourceEntity | undefined;

  selectedApplicationType: GenericKeyValue | undefined;

  selectedTechnology: GenericKeyValue | undefined;

  selectedApplication!: ApplicationEntity;

  selectedFlowType: GenericKeyValue | undefined;

  listOfApplicationFlowTypes: GenericKeyValue[] = [];

  loading: boolean= false;

  searchValue: string | undefined;

  listTechnologies: GenericKeyValue[] = [
    {key: '.NET', value: '.NET'},
    // {key: 'PYTHON', value: 'Python'},
    {key: 'JAVA', value: 'Java'},
    // {key: 'C++', value: 'C++'},
    {key: 'HTML', value: 'HTML'}
  ];

  listApplicationTypes: GenericKeyValue[] = [
    {key: 'DESKTOP', value: 'Desktop'},
    {key: 'WEB', value: 'Web'}
  ];

  listAppRegistry: AppRegistryEntity[] = [];
  selectedAppRegistry: AppRegistryEntity | undefined;

  // Date Range Filter
  formGroup: FormGroup = new FormGroup({
    date: new FormControl<Date[] | null>(null)
  });
  formGroupDate: FormGroup = new FormGroup({
    date: new FormControl<Date[] | null>(null)
  });
  dateFilterValues = {
    startDate: null,
    endDate: null
  };
  rangeDates: Date[] | null = null;

  dataSourceOptions: GenericKeyValue[] = [];

  confirmDialogTimeoutLimit: number = 0;
  startCheckingInactivity: boolean = false;

  constructor(
    private fb: FormBuilder,
    private validator: ValidatorService,
    private messageService: MessageService,
    private confirmationService: ConfirmationService,
    private translate: TranslateService,
    private getAllDataSourceUseCase: GetAllDataSourcesUseCase,
    private deleteApplicationByIdUseCase: DeleteApplicationByIdUseCase,
    private updateApplicationByIdUseCase: UpdateApplicationByIdUseCase,
    private getAllApplicationsUseCase: GetAllApplicationsUseCase,
    private filterService: FilterService,
    private localStorageService: LocalStorageService,
    private loggerService: ConsoleLoggerService,
    public auditTrailService: AuditTrailService,
    private getAppRegistriesUseCase: GetAppRegistriesUseCase,
  ){
    this.filterService.register('customDateRange', (value: any, filter: any): boolean => {
      if (!filter || (!filter.startDate && !filter.endDate)) {
        return true; // If no filter, show all
      }
      const dateValue = new Date(value).getTime();
      const startDate = filter.startDate ? new Date(filter.startDate).getTime() : null;
      const endDate = filter.endDate ? new Date(filter.endDate).getTime() : null;
      if (startDate && endDate) {
        return dateValue >= startDate && dateValue <= endDate;
      } else if (startDate) {
        return dateValue >= startDate;
      } else if (endDate) {
        return dateValue <= endDate;
      }
      return false;
    });
  }

  ngOnInit(): void {
    this.getAllApplications()
    this.getAllDataSources();
    this.getAppRegistries();

    // Load flow types
    const flowTypes = this.localStorageService.getSessionSettings()?.continued1?.applicationFlowTypes?.map(str => ({key: str, value: str})) ?? [];
    this.listOfApplicationFlowTypes = flowTypes;

  }

  ngOnDestroy() {
    // Clean up the timeout if the component is destroyed
    this.closeConfirmationDialog();
    this.auditTrailService.resetInactivityMonitor();
  }

  resetInactivityMonitor() {
    this.startCheckingInactivity = false;
    this.confirmDialogTimeoutLimit = 0;
  }


  public form: FormGroup = this.fb.group({
    applicationTitle: [],
    applicationName: ['', Validators.required],
    appRegistryId: [],
    applicationPath:['', Validators.required],
    technology: ['', Validators.required],
    applicationType: ['', Validators.required],
    dataSource: [],
    isPublished:[],
    flowType: [],
  });

  isValid(field: string) {
    return this.validator.isValidField(this.form, field);
  }

  getAppRegistries() {
    this.getAppRegistriesUseCase.execute().then(
      (data) => {
        this.listAppRegistry = data;
      },
      (e) => {
        this.messageService.add({
          severity: 'error',
          summary: this.translate.instant('content.errorTitle'),
          detail: e.message,
          life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
        });
        this.loggerService.error(e);
        const at_attributes: ExtraData[] = [{ name: AuditTrailFields.ERROR, value: JSON.stringify(e) }];
        this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.GET_ALL_APP_REGISTRIES, 0, 'ERROR', '', at_attributes);
      }
    );
  }

  getAllDataSources(){
    this.getAllDataSourceUseCase.execute().then(
      (data)=>{
        this.dataSources = data;
        this.dataSources.forEach((element) => {
          this.dataSourceOptions.push({key: element.id, value: element.name});
        });
      },
      (e) =>{
        this.messageService.add({
          severity: 'error',
          summary: this.translate.instant('content.errorTitle'),
          detail: e.message,
          life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
        });
        this.loggerService.error(e);
        const at_attributes: ExtraData[] = [{ name: AuditTrailFields.ERROR, value: JSON.stringify(e) }];
        this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.GET_ALL_DATA_SOURCES, 0, 'ERROR', '', at_attributes);
      }
    );
  }

  getAllApplications(){
    this.getAllApplicationsUseCase.execute().then(
      (data)=>{
        this.listApplications = data;
        this.loading = false;
      },
      (e)=>{
        this.messageService.add({
          severity: 'error',
          summary: this.translate.instant('content.errorTitle'),
          detail: e.message,
          life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
        });
        this.loggerService.error(e);
        const at_attributes: ExtraData[] = [{ name: AuditTrailFields.ERROR, value: JSON.stringify(e) }];
        this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.GET_ALL_APPLICATIONS, 0, 'ERROR', '', at_attributes);
      }
    )
  }

  getSeverity(status: boolean) {
    switch (status) {
      case true: {
        return '#F59E0B';
        // return 'success';
      }
      case false: {
        return '#64748B';
        // return 'info';
      }
      default: {
        return 'var(--primary-color)';
      }
    }
  }

  deleteApplication(data: ApplicationEntity) {
    this.confirmationService.confirm({
      message: this.translate.instant('messages.message_remove') + " <b>" + data.applicationName + '</b>?',
      header: this.translate.instant('pass_application.remove_application_flow'),
      icon: 'pi pi-exclamation-triangle',
      acceptIcon: "none",
      rejectIcon: "none",
      rejectButtonStyleClass: "p-button-text",
      acceptButtonStyleClass: "ng-confirm-button",
      acceptLabel: this.translate.instant('yes'),
      rejectLabel: this.translate.instant('no'),
      accept: () => {
        this.resetInactivityMonitor();
        const at_attributes: ExtraData[] = [
          { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(data) },
        ];
        this.auditTrailService.auditTrailSelectReason(ReasonTypeEnum.PASS_APP_MANAGEMENT, AuditTrailActions.DEL_APP, ReasonActionTypeEnum.DELETE, () => { this.deleteApplicationById(data) }, at_attributes);
      },
      reject: () => {
        this.resetInactivityMonitor();
      }
    });

    this.confirmDialogTimeoutLimit = (this.localStorageService.getSessionSettings()?.timeoutMessages ?? 10) * 1000
    this.startCheckingInactivity = false;
    setTimeout(() => this.startCheckingInactivity = true, 0);
  }

  deleteApplicationById(data: ApplicationEntity){
    const id = data.id!!;
    this.deleteApplicationByIdUseCase.execute({id: id}).then(
      (data)=>{
        this.messageService.add({
          severity: 'success',
          summary: this.translate.instant('content.successTitle'),
          detail: this.translate.instant('content.successfully_removed'),
          life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
        });
        this.listApplications = [...this.listApplications.filter((_v,k)=>_v.id!=id)];
      },
      (e)=>{
        this.messageService.add({
          severity: 'error',
          summary: this.translate.instant('content.errorTitle'),
          detail: e.message,
          life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
        });
        this.loggerService.error(e);
        const at_attributes: ExtraData[] = [
          { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
          { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(data) },

        ];
        this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.DEL_APP, 0, 'ERROR', '', at_attributes);
      }
    );
  }

  editApplication(data: ApplicationEntity){
    // this.onEdit.emit(data);
    this.selectedApplication = data;

    this.showSaveApplicationDialog = true;

    this.form.controls['applicationName'].setValue(data.applicationName);
    this.form.controls['applicationPath'].setValue(data.fullPath);
    this.selectedAppRegistry = this.listAppRegistry.find(appReg => appReg.id == data.appRegistryId);
    this.selectedDataSource = this.dataSources.find(ds => ds.id == data.dataSourceId);
    this.selectedTechnology = this.listTechnologies.find(tech => tech.key == data.technology);
    this.selectedFlowType = this.listOfApplicationFlowTypes.find(type => type.key == data?.flowType);
    this.selectedApplicationType = this.listApplicationTypes.find(type => type.key == data.applicationType);
    this.form.controls['isPublished'].setValue(data.status);
  }

  showApplicationFlow(data: ApplicationEntity){
    this.onEdit.emit(data);
  }

  createNewFlow(){
    this.onNewDataFlow.emit(true);
  }

  closeSaveApplicationDialog(){
    this.showSaveApplicationDialog = false;
  }

  acceptSaveApplication(){

    if( !this.isValid('applicationName') || !this.isValid('applicationPath')){
          return
    }

    let tmpApplication: ApplicationEntity = {
      id: this.selectedApplication.id,
      applicationName: this.form.controls['applicationName'].value,
      flowType: this.form.controls['flowType'].value.key!=undefined?
        this.form.controls['flowType'].value.key: this.selectedApplication.flowType,
      appRegistryId: this.form.controls['appRegistryId'].value!=undefined?
        this.form.controls['appRegistryId'].value.id:this.selectedApplication.appRegistryId,
      fullPath: this.form.controls['applicationPath'].value,
      technology: this.form.controls['technology'].value.key!=undefined?
        this.form.controls['technology'].value.key: this.selectedApplication.technology,
      dataSourceId: this.form.controls['dataSource'].value!=undefined?
        this.form.controls['dataSource'].value.id:this.selectedApplication.dataSourceId,
      applicationType: this.form.controls['applicationType'].value.key!=undefined?
        this.form.controls['applicationType'].value.key: this.selectedApplication.applicationType,
      status: this.form.controls['isPublished'].value
    }
    const at_attributes: ExtraData[] = [
      { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(this.selectedApplication) },
      { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(tmpApplication) },
    ];
    this.auditTrailService.auditTrailSelectReason(ReasonTypeEnum.PASS_APP_MANAGEMENT, AuditTrailActions.MOD_APP, ReasonActionTypeEnum.UPDATE, () => {
      this.updateApplicationByIdUseCase.execute(tmpApplication).then(
        ()=>{
          this.loading = true;
          this.messageService.add({
            severity: 'success',
            summary: this.translate.instant('content.successTitle'),
            detail: this.translate.instant('messages.updated_successfully'),
            life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
          });
          this.getAllApplications();
        },
        (e)=>{
          this.messageService.add({
            severity: 'error',
            summary: this.translate.instant('content.errorTitle'),
            detail: e.message,
            life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
          });
          this.loggerService.error(e);
          const at_attributes: ExtraData[] = [
            { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
            { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(this.selectedApplication) },
            { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(tmpApplication) },
          ];
          this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.MOD_APP, 0, 'ERROR', '', at_attributes);
        }
      );
      this.showSaveApplicationDialog = false;
    }, at_attributes);
  }

  transformDate(inputDate: any){
    const date = new Date(inputDate);

    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    const hours = date.getHours().toString().padStart(2, '0');
    const minutes = date.getMinutes().toString().padStart(2, '0');
    const seconds = date.getSeconds().toString().padStart(2, '0');
    const milliseconds = date.getMilliseconds().toString().padStart(3, '0');

    return `${year}/${month}/${day}  ${hours}:${minutes}.${seconds}.${milliseconds}`;
  }

  ApplicationStatus(status: boolean){
    if(status){
      return this.translate.instant('flow.published');
    }else{
      return this.translate.instant('flow.no_published');
    }
  }

  getDataSourceName(dataSourceId: string){
    let data = [...this.dataSources.filter((_v,k)=>_v.id == dataSourceId)]
    if(data.length == 0){
      return ""
    }
    return data[0].name;
  }

  getAppRegistryName(appRegistryId: string){
    let data = [...this.listAppRegistry.filter((_v,k)=>_v.id == appRegistryId)]
    if(data.length == 0){
      return ""
    }
    return data[0].name;
  }

  /* Search */
  onFilter(event: any, dt: Table) {
    if(!event.filters['createdAt'].value){
      this.rangeDates = null;
      this.formGroup.reset();
    }
    if(!event.filters['updatedAt'].value){
      this.rangeDates = null;
      this.formGroupDate.reset();
    }
  }

  /* Date Range Filter */
  applyDateRangeFilter(dt: Table, field: string) {
    if(field === 'createdAt'){
      this.rangeDates = this.formGroup.get('date')?.value;
    }
    else if(field === 'updatedAt'){
      this.rangeDates = this.formGroupDate.get('date')?.value;
    }
    dt.filter({
      startDate: this.rangeDates ? this.rangeDates[0] : null,
      endDate: this.rangeDates ? this.rangeDates[1] : null
    }, field, 'customDateRange');
  }

  closeConfirmationDialog() {
    this.confirmationService.close();
    this.resetInactivityMonitor();
  }
}
