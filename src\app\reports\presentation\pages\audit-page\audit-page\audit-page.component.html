<div *ngIf="isLoading">
    <app-loading-spinner [isLoading]="isLoading"></app-loading-spinner>
</div>

<p-toast></p-toast>
<app-inactivity-monitor
    [inactivityTimeoutLimit]="auditTrailService.confirmDialogTimeoutLimit"
    [startChecking]="auditTrailService.startCheckingInactivity"
    (expired)="auditTrailService.closeConfirmationDialog()"
></app-inactivity-monitor>
<div class="main-container">
    <div class="topBarItems" id="topBar">
        <div class="leftSideItems">
            <div class="recrodsTitle">
                {{'menu.reports_audit_trail' | translate}}
            </div>
        </div>
        <div class="rightSideItems">
            <div [formGroup]="datesForm" class="centerDiv searchButtonGroupSmall">

                <div class="dateSelectorContainer">
                    <label for="icondisplay" class="dateSelectorLabel">{{ 'signaturesTable.applicationId' | translate
                        }}</label>
                    <div class="dateSelectorGroup">
                        <p-dropdown [style]="{'width': '320px'}" id="application" formControlName="application"
                            [(ngModel)]="selectedApp" [options]="appOptions" optionLabel="name" dataKey="name"
                            optionValue="name" placeholder="">
                            <ng-template pTemplate="selectedItem">
                                <div class="flex align-items-center gap-2" *ngIf="selectedApp">
                                    <div>{{ selectedApp | translate}}</div>
                                </div>
                            </ng-template>
                            <ng-template let-appOption pTemplate="item">
                                <div class="flex align-items-center gap-2">
                                    <div>{{ appOption.name | translate }}</div>
                                </div>
                            </ng-template>
                        </p-dropdown>
                    </div>
                </div>
                <div class="dateSelectorContainer">
                    <label for="icondisplay" class="dateSelectorLabel">{{ 'signaturesTable.actionId' | translate
                        }}</label>
                    <div class="dateSelectorGroup">
                        <p-dropdown [style]="{'width': '320px'}" id="action" formControlName="action"
                            [(ngModel)]="selectedAction" [options]="actionOptions" optionLabel="name" dataKey="name"
                            optionValue="name" placeholder="">
                            <ng-template pTemplate="selectedItem">
                                <div class="flex align-items-center gap-2" *ngIf="selectedAction">
                                    <div>{{ selectedAction | translate }}</div>
                                </div>
                            </ng-template>
                            <ng-template let-actionOption pTemplate="item">
                                <div class="flex align-items-center gap-2">
                                    <div>{{ actionOption.name | translate }}</div>
                                </div>
                            </ng-template>
                        </p-dropdown>
                    </div>
                </div>
                <div class="dateSelectorContainer">
                    <label for="icondisplay" class="dateSelectorLabel">{{ 'signaturesTable.receiverId' | translate
                        }}</label>
                    <div class="dateSelectorGroup">
                        <input pInputText type="text"
                        formControlName="subject"
                    />
                    </div>
                </div>
                <div class="dateSelectorContainer">
                    <div class="dateSelectorGroup">
                        <p-calendar appendTo="body" formControlName="rangeDates" [iconDisplay]="'input'" [showIcon]="true"
                            [(ngModel)]="dates" selectionMode="range" [class.ng-invalid]="dateError"
                            [class.ng-dirty]="dateError" aria-describedby="date-help1" [readonlyInput]="true"
                            inputId="multiple" dateFormat="{{ 'dateFormat' | translate }}" [showButtonBar]="true" [showTime]="true"
                            [hourFormat]="'24'"></p-calendar>
                        <div *ngIf="dateError">
                            <small class="error" id="date-help1">{{ dateErrorMessage | translate }}</small>
                        </div>
                    </div>
                </div>
                <!-- <ngx-verazial-ui-field label="{{ 'signaturesTable.endDate' | translate }}" id="endDate" formControlName="endDate" type="date" item></ngx-verazial-ui-field> -->
                <p-button [style]="{'background': '#0AB4BA', 'border-color': '#0AB4BA'}"
                    class="searchButton searchButtonNormal" label="{{ 'update' | translate }}" [rounded]="true"
                    (click)="getAllActions()"></p-button>
                <p-button [style]="{'background': '#0AB4BA', 'border-color': '#0AB4BA'}"
                    class="searchButton searchButtonSmall" icon="pi pi-refresh" [rounded]="true"
                    (click)="getAllActions()"></p-button>
                <!-- <ngx-verazial-ui-button class="searchButton" text="{{ 'update' | translate }}" backgroundColor="#162746" fontColor="white" (click)="getAllActions()" button></ngx-verazial-ui-button> -->
            </div>
        </div>
    </div>

    <app-list-actions [readOnly]="true" [readAndWritePermissions]="false" [listOfActions]="virtualActionsData"
        [audit]="true" (details)="showDetails($event)" [isAddingData]="addingData"></app-list-actions>

    <div class="card" *ngIf="!addingData && canExportReport">
        <div class="rightSideItems">
            <div [formGroup]="exportForm" class="centerDiv searchButtonGroupSmallTransparent">
                <p-button [style]="{'background': '#64748B', 'border-color': '#64748B'}"
                    class="searchButton searchButtonNormal" label="{{ 'reports.export' | translate }}" [rounded]="true"
                    (click)="createReport(false)"></p-button>
                <p-button [style]="{'background': '#64748B', 'border-color': '#64748B'}"
                    class="searchButton searchButtonSmall" icon="pi pi-download" [rounded]="true"
                    (click)="createReport(false)"></p-button>
                <p-button [style]="{'background': '#64748B', 'border-color': '#64748B'}"
                    class="searchButton searchButtonNormal" label="{{ 'reports.export2' | translate }}" [rounded]="true"
                    (click)="createReport(true)"></p-button>
                <p-button [style]="{'background': '#64748B', 'border-color': '#64748B'}"
                    class="searchButton searchButtonSmall" icon="pi pi-download" [rounded]="true"
                    (click)="createReport(true)"></p-button>
            </div>
        </div>


    </div>
</div>

<p-dialog [(visible)]="showDetailsDialog" styleClass="p-fluid custom-dialog" [modal]="true" [closable]="true">
    <ng-template pTemplate="content">
        <div class="flex flex-column m-4" style="margin-top: 0px !important">
            <p class="detail-header">{{'signaturesTable.details' | translate}}</p>
            <pre
                style="white-space: pre-wrap; word-wrap: break-word; word-break: break-word; max-width: 100%">{{ detailsDialogText }}</pre>
        </div>
    </ng-template>
</p-dialog>