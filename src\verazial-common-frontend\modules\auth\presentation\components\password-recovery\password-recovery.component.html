<div *ngIf="isLoading">
    <app-loading-spinner [isLoading]="isLoading"></app-loading-spinner>
</div>
<app-inactivity-monitor
    [inactivityTimeoutLimit]="auditTrailService.confirmDialogTimeoutLimit"
    [startChecking]="auditTrailService.startCheckingInactivity"
    (expired)="auditTrailService.closeConfirmationDialog()"
></app-inactivity-monitor>
<div class="flex flex-column align-items-center" [formGroup]="dataForm">
    <div class="resetPasswordTitle mt-1 mb-3">
        {{ 'content.new_password' | translate }}
    </div>
    <div class="flex flex-column gap-1 mt-3">
        <div class="flex flex-row align-items-center gap-2 label-field" [style]="{'font-weight': '600'}">
            <i class="pi pi-key" style="font-size: 1rem"></i>
            <label>{{ 'content.new_password' | translate}}</label>
        </div>
        <p-password
            formControlName="password" inputStyleClass="w-full md:w-17rem"
            [ngClass]="!isValid('password') && dataForm.controls['password'].touched? 'ng-invalid ng-dirty':'' "
            [toggleMask]="true" [feedback]="false"
            (ngModelChange)="trackDataChange()"
            (copy)="disableCopyPaste($event)"
            (cut)="disableCopyPaste($event)"
            (paste)="disableCopyPaste($event)"
        />
        <small *ngIf="!isValid('password') && dataForm.controls['password'].touched" style="color:red">{{ 'messages.error_isRequiredField' | translate }}</small>
    </div>
    <div class="flex flex-column gap-1 mt-3">
        <div class="flex flex-row align-items-center gap-2 label-field" [style]="{'font-weight': '600'}">
            <i class="pi pi-key" style="font-size: 1rem"></i>
            <label>{{ 'content.repeat_password' | translate}}</label>
        </div>
        <p-password
            formControlName="repeatPassword" inputStyleClass="w-full md:w-17rem"
            [ngClass]="!isValid('repeatPassword') && dataForm.controls['repeatPassword'].touched? 'ng-invalid ng-dirty':'' "
            [toggleMask]="true" [feedback]="false"
            (ngModelChange)="trackDataChange()"
            (copy)="disableCopyPaste($event)"
            (cut)="disableCopyPaste($event)"
            (paste)="disableCopyPaste($event)"
        />
        <small *ngIf="!isValid('repeatPassword') && dataForm.controls['repeatPassword'].touched" style="color:red">{{ 'messages.error_isRequiredField' | translate }}</small>
    </div>
    @if(!isValid('password') && dataForm.controls['password'].touched){
        <small class="mt-3" [style]="{'color': 'red'}">{{ errorMessage }}</small>
    }
    @if(passwordNotMatch){
        <small class="mt-1 text-center" [style]="{'color': 'red'}">{{ 'messages.password_not_match' | translate }}</small>
    }
    <div class="flex align-items-center justify-content-center mt-5">
        <p-button
            (onClick)="updatePassword()"
            [style]="{'width':'17rem','height':'38px', 'color': '#FFFFFF' , 'border': 'none', 'background': '#15294A', 'font-family': 'Open Sans', 'font-size': '14px'}"
            label="{{'update_password' | translate}}"
        />
    </div>
    <div class="flex align-items-center justify-content-center mt-1">
        <p-button
            (onClick)="cancel()"
            [style]="{'width':'17rem','height':'38px', 'color': '#64748B' , 'border': 'none', 'background': '#FFFFFF', 'font-family': 'Open Sans', 'font-size': '14px'}"
            label="{{'loginForm.back_to_login' | translate}}"
        />
    </div>
</div>