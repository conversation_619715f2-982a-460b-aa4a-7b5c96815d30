<app-loading-spinner [isLoading]="isLoading"></app-loading-spinner>
<p-toast/>
<app-inactivity-monitor
    [inactivityTimeoutLimit]="confirmDialogTimeoutLimit"
    [startChecking]="startCheckingInactivity"
    (expired)="closeConfirmationDialog()"
></app-inactivity-monitor>
<app-inactivity-monitor
    [inactivityTimeoutLimit]="auditTrailService.confirmDialogTimeoutLimit"
    [startChecking]="auditTrailService.startCheckingInactivity"
    (expired)="auditTrailService.closeConfirmationDialog()"
></app-inactivity-monitor>
<app-list-user-subject
    [isLoading]="isLoading"
    [type]="type"
    [lazyLoad]="useLazyLoad"
    [readAndWritePermissions]="canReadAndWrite"
    [readOnly]="readOnly"
    [userIsVerified]="userIsVerified"
    [listOfUsersSubjects]="listOfUser"
    [totalRecords]="totalRecords"
    [offset]="getUsersRequest.offset"
    [limit]="getUsersRequest.limit"
    [allRoles]="listRoles"
    [managerSettings]="managerSettings"
    [konektorProperties]="konektorProperties"
    (onAdd)="onSubmitAddNewUser($event)"
    (onMainAction)="onEditUser($event)"
    (onSecondaryAction)="confirmDelete($event)"
    (onBioSearch)="navigateToUser($event)"
    (onTableLazyLoadEvent)="onTableLazyLoadEvent($event)"
    (userVerified)="userVerified($event)"
    (onPictureUploaded)="onPictureUploaded($event)"
></app-list-user-subject>