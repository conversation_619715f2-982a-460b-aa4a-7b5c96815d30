import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { AuthPageRoutingModule } from './auth-page-routing.module';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { UiVerifyModule } from 'ngx-verazial-ui-lib';
import { ButtonModule } from 'primeng/button';
import { InputTextModule } from 'primeng/inputtext';
import { MessagesModule } from 'primeng/messages';
import { PasswordModule } from 'primeng/password';
import { AuthPageComponent } from './auth-page/auth-page.component';
import { SigninComponent } from '../../components/signin/signin.component';
import { SignupComponent } from '../../components/signup/signup.component';
import { BiometricComponent } from '../../components/biometric/biometric.component';
import { LoadingSpinnerModule } from 'src/verazial-common-frontend/modules/shared/components/loading-spinner/loading-spinner.module';
import { DialogModule } from 'primeng/dialog';
import { RoleSelectionModule } from 'src/verazial-common-frontend/modules/shared/components/role-selection/role-selection.module';
import { ToastModule } from 'primeng/toast';
import { MessageService } from 'primeng/api';
import { UpdatePasswordComponent } from '../../components/update-password/update-password.component';
import { WidgetMatchModule } from 'src/verazial-common-frontend/modules/shared/components/widget-match/widget-match.module';
import { WidgetSearchModule } from 'src/verazial-common-frontend/modules/shared/components/widget-search/widget-search.module';
import { ResetPasswordComponent } from '../../components/reset-password/reset-password.component';
import { PasswordRecoveryComponent } from '../../components/password-recovery/password-recovery.component';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { InactivityMonitorModule } from 'src/verazial-common-frontend/modules/shared/components/inactivity-monitor/inactivity-monitor.module';


@NgModule({
  declarations: [
    AuthPageComponent,
    SigninComponent,
    SignupComponent,
    BiometricComponent,
    UpdatePasswordComponent,
    ResetPasswordComponent,
    PasswordRecoveryComponent,
  ],
  imports: [
    CommonModule,
    AuthPageRoutingModule,
    /** NG modules */
    InputTextModule,
    ButtonModule,
    PasswordModule,
    MessagesModule,
    DialogModule,
    ToastModule,
    ConfirmDialogModule,
    /** Forms */
    ReactiveFormsModule,
    FormsModule,
    /** Translator module */
    TranslateModule,
    /** Custom components */
    UiVerifyModule,
    LoadingSpinnerModule,
    RoleSelectionModule,
    WidgetMatchModule,
    WidgetSearchModule,
    InactivityMonitorModule,
  ],
  exports: [
  ]
})
export class AuthPageModule { }
