import { Component, EventEmitter, Input, On<PERSON><PERSON>roy, OnInit, Output } from "@angular/core";
import { Form<PERSON><PERSON>er, FormGroup, Validators } from "@angular/forms";
import { TranslateService } from "@ngx-translate/core";
import { MessageService } from "primeng/api";
import { environment } from "src/environments/environment";
import { KonektorPropertiesEntity } from "src/verazial-common-frontend/core/general/konektor/domain/entity/konektor-properties.entity";
import { GeneralSettings } from "src/verazial-common-frontend/core/general/manager/common/models/general-settings.model";
import { LanguageRecordModel, TranslationGroup } from "src/verazial-common-frontend/core/general/manager/common/models/translation.model";
import { PasswordRecoveryModel } from "src/verazial-common-frontend/core/general/user/domain/entity/password-recovery.entity";
import { PasswordRecoveryUseCase } from "src/verazial-common-frontend/core/general/user/domain/use-cases/password-recovery.use-case";
import { AuditTrailActions } from "src/verazial-common-frontend/core/models/audit-trail-actions.enum";
import { AuditTrailFields } from "src/verazial-common-frontend/core/models/audit-trail-fields.enum";
import { AuditTrailService, ReasonActionTypeEnum, ReasonTypeEnum } from "src/verazial-common-frontend/core/services/audit-trail.service";
import { ConsoleLoggerService } from "src/verazial-common-frontend/core/services/console-logger.service";
import { ValidatorService } from "src/verazial-common-frontend/modules/shared/services/validator.service";

@Component({
    selector: 'app-password-recovery',
    templateUrl: './password-recovery.component.html',
    styleUrl: './password-recovery.component.css'
})
export class PasswordRecoveryComponent implements OnInit, OnDestroy {

    @Input() token: string | undefined;
    @Input() recoveryToken: string | undefined;
    @Input() validatorPattern: string | RegExp = /^(?=.*?[A-Z])(?=.*?[a-z])(?=.*?[0-9])(?=.*?[#?!@$%^&*-]).{8,}$/;
    @Input() generalSettings: GeneralSettings | undefined;
    @Input() konektorProperties?: KonektorPropertiesEntity;
    @Output() onSuccess = new EventEmitter<boolean>();
    @Output() onCancel = new EventEmitter<boolean>();

    isLoading: boolean = false;
    passwordNotMatch: boolean = false;

    errorMessage: string = "messages.invalidPasswordComplexity";

    public dataForm: FormGroup = this.fb.group({
        password: ['',[Validators.required, Validators.pattern(this.validatorPattern)]],
        repeatPassword: ['', [Validators.required, Validators.pattern(this.validatorPattern)]],
    });

    constructor(
        private fb: FormBuilder,
        private validatorService: ValidatorService,
        private messageService: MessageService,
        private loggerService: ConsoleLoggerService,
        private translateService: TranslateService,
        public auditTrailService: AuditTrailService,
        private passwordRecoveryUseCase: PasswordRecoveryUseCase,
    ) { }

    ngOnInit(): void {
        var lang = this.translateService.currentLang;
        if(!lang) {
            lang = this.translateService.getDefaultLang();
        }
        var translation = this.generalSettings?.continued1?.passwordComplexity?.errorMessage?.translations?.find((t: LanguageRecordModel) => t.languageCode == lang);
        if(translation)
            this.errorMessage = translation.value!;
        else
            this.errorMessage = this.translateService.instant(this.errorMessage);
    }

    ngOnDestroy(): void {
        this.auditTrailService.resetInactivityMonitor();
    }

    updatePassword() {
        this.dataForm.markAllAsTouched();
        if (!this.passwordNotMatch && this.dataForm.valid) {
            this.isLoading = true;
            const recovery: PasswordRecoveryModel = {
                token: this.recoveryToken,
                password: this.dataForm.get('password')?.value,
            }
            const reasons = this.generalSettings?.continued1?.passwordManagementReasons;
            const translations = this.generalSettings?.continued1?.translations?.find((t: TranslationGroup) => t.id === 'passwordManagementReasons')?.translations;
            const executorNumId = this.konektorProperties ? this.konektorProperties.locationId + ' ' + this.konektorProperties.segmentId + ' ' + this.konektorProperties.deviceId : '';
            const at_attributes = [
                { name: AuditTrailFields.RECOVERY_TOKEN, value: this.recoveryToken },
                { name: AuditTrailFields.APPLICATION_ID, value: environment.application},
            ];
            this.auditTrailService.auditTrailSelectReason(ReasonTypeEnum.OTHER, AuditTrailActions.MOD_PASS, ReasonActionTypeEnum.UPDATE, () => {
                this.passwordRecoveryUseCase.execute({ request: recovery, authToken: this.token }).then(
                    (data) => {
                        this.onSuccess.emit(true)
                    },
                    (e) => {
                        this.loggerService.error("Users MS");
                        this.loggerService.error(e);
                        const at_attributes = [
                            { name: AuditTrailFields.RECOVERY_TOKEN, value: this.recoveryToken },
                            { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
                        ];
                        this.auditTrailService.registerAuditTrailAction('NO_SUBJECT_ID', AuditTrailActions.PW_RECOVERY, 0, 'ERROR', e.message, at_attributes);
                        if((e.code != '' && e.code != '561' && e.code != '0')){
                            this.messageService.add({
                                severity: 'error',
                                summary: this.translateService.instant("titles.unauthorized"),
                                detail: this.translateService.instant("ms_errors." + e.code)
                            });
                        }
                        else {
                            this.onSuccess.emit(false)
                        }
                    }
                )
                .finally(() => {
                    this.isLoading = false;
                });
            }, at_attributes, true, reasons, translations, executorNumId);
        }
    }

    trackDataChange() {
        if (this.dataForm.get('password')?.value != null && this.dataForm.get('repeatPassword')?.value != null &&
            this.dataForm.get('password')?.value != this.dataForm.get('repeatPassword')?.value) {
            this.passwordNotMatch = true;
        } else {
            this.passwordNotMatch = false;
        }
    }

    isValid(field: string): boolean {
        return this.validatorService.isValidField(this.dataForm, field);
    }

    checkSpecificError(field: string, error: string): boolean {
        if (error == 'minLength') {
          let result = true;
          let control = this.dataForm.controls[field]
          if(control.errors) {
            const object = control.errors['minlength'];
            if (object) {
              result = object.requiredLength > object.actualLength;
            }
            else {
              result = false;
            }
          }
          return result;
        }
        return this.validatorService.checkSpecificError(this.dataForm, field, error);
    }

    disableCopyPaste(event: ClipboardEvent): void {
        if (!this.generalSettings?.continued1?.passwordComplexity?.isEnabled) {
            event.preventDefault();
        }
    }

    cancel() {
        this.onCancel.emit(true);
    }

}