import { DOCUMENT } from '@angular/common';
import { Component, Inject, LOCALE_ID, OnDestroy, OnInit } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { environment } from 'src/environments/environment';
import { TranslateService } from '@ngx-translate/core';
import { HttpClient } from '@angular/common/http';
import { ReportsService } from 'src/verazial-common-frontend/core/services/reports.service';
import { ActionEntity } from 'src/verazial-common-frontend/core/general/actionsV2/domain/entity/action.entity';
import { AndEntity, FilterEntity, NotEntity } from 'src/verazial-common-frontend/core/general/actionsV2/domain/entity/filter.entity';
import { SearchActionsRequestEntity } from 'src/verazial-common-frontend/core/general/actionsV2/domain/entity/search-actions-request.entity';
import { SearchActionsUseCase } from 'src/verazial-common-frontend/core/general/actionsV2/domain/use-cases/search-actions.use-case';
import { CheckPermissionsService } from 'src/verazial-common-frontend/core/services/check-permissions-service';
import { AccessIdentifier } from 'src/verazial-common-frontend/core/models/access-identifier.enum';
import { v4 as uuidv4 } from 'uuid';
import { LocalStorageService } from 'src/verazial-common-frontend/core/services/local-storage.service';
import { MessageService } from 'primeng/api';
import * as XLSX from 'xlsx';
import { AuditTrailService, ReasonActionTypeEnum, ReasonTypeEnum } from 'src/verazial-common-frontend/core/services/audit-trail.service';
import { AuditTrailActions } from 'src/verazial-common-frontend/core/models/audit-trail-actions.enum';
import { ExtraData } from 'src/verazial-common-frontend/core/general/actionsV2/domain/entity/extra-data.interface';
import { CountActionsRequestEntity } from 'src/verazial-common-frontend/core/general/actionsV2/domain/entity/count-actions-request.entity';
import { CountActionsUseCase } from 'src/verazial-common-frontend/core/general/actionsV2/domain/use-cases/count-actions.use-case';
import { Group } from 'src/verazial-common-frontend/core/general/actionsV2/domain/entity/count-actions-response.entity';
import { ConsoleLoggerService } from 'src/verazial-common-frontend/core/services/console-logger.service';


interface ExportColumn {
    title: string;
    dataKey: string;
}
interface Column {
    field: string;
    header: string;
}

interface PdfClass {
    locationId: string;
    segmentId: string;
    deviceId: string;
    actionName: string;
    executorId: string;
    receiverId: string;
    createdAt: string;
    actionResult: string;
}


@Component({
    selector: 'app-audit-page',
    templateUrl: './audit-page.component.html',
    styleUrls: ['./audit-page.component.scss', './audit-page.component.css'],
    providers: [MessageService]
})
export class AuditPageComponent implements OnInit, OnDestroy {

    AuditTrailActions = AuditTrailActions;

    selectedApp: string = "";
    appOptions: Array<any> = [];
    selectedAction: string = "";
    actionOptions: Array<any> = [];

    chart: any;
    pieChart: any;
    hChart: any;
    dChart: any;
    lChart: any;
    isLoading: boolean = false;
    dateError: boolean = false;
    dateErrorMessage: string = "";
    verificationComputedNumber: string = "";
    identificationComputedNumber: string = "";
    inComputedNumber: string = "";
    outComputedNumber: string = "";;
    deletionComputedNumber: string = "";
    deletionSamplesComputedNumber: string = "";
    newSubjectComputedNumber: string = "";
    newPictureComputedNumber: string = "";
    modBioComputedNumber: string = "";
    newSampleComputedNumber: string = "";
    passwordLoginComputedNumber: string = "";
    actionsComputedNumber: string = "";
    noDevComputedNumber: string = "";
    imageBase64: string | undefined = "";

    showSpinners: boolean = true;
    endDate: Date = new Date(new Date().getTime());
    initDate: Date = new Date(new Date().getTime() - (environment.rangeDaysBefore * 24 * 60 * 60 * 1000));
    dates: Date[] = [this.initDate, this.endDate];
    datesForm: FormGroup = this.fb.group({
        rangeDates: this.dates,
        application: [],
        action: [],
        subject: '',
    });

    exportForm: FormGroup = this.fb.group({

    });
    exportColumns!: ExportColumn[];
    cols: Column[] = [];

    actionsData: any[] = [];
    virtualActionsData: ActionEntity[] = [];
    pdfActionsData: PdfClass[] = [];
    csvActionsData: PdfClass[] = [];

    showNoData1: boolean = false;
    showNoData2: boolean = false;
    showNoData3: boolean = false;
    showNoData4: boolean = false;

    addingData: boolean = true;

    showDetailsDialog: boolean = false;
    detailsDialogAction: ActionEntity = new ActionEntity();
    detailsDialogText: string = "";

    canExportReport: boolean = false;
    access_identifier = AccessIdentifier.REPORTS_AUDIT_TRAIL_EXPORT;
    extraData: ExtraData[] = [];

    constructor(
        @Inject(LOCALE_ID) private locale: string,
        //private getActionByUseCase: getActionsByUseCase,
        @Inject(DOCUMENT) private document: Document,
        private translate: TranslateService,
        private fb: FormBuilder,
        private reportsService: ReportsService,
        private searchActionsUseCase: SearchActionsUseCase,
        private countActionsUseCase: CountActionsUseCase,
        private checkPermissions: CheckPermissionsService,
        private localStorageService: LocalStorageService,
        private messageService: MessageService,
        public auditTrailService: AuditTrailService,
        private http: HttpClient,
        private loggerService: ConsoleLoggerService,
    ) { }

    ngOnInit(): void {
        this.loggerService.info("Entrando a Verázial Reports v" + environment.version);
        this.appOptions.push({ "name": "menu.all" });
        this.actionOptions.push({ "name": "menu.all" });

        this.selectedApp = environment.applicationDefault;
        this.selectedAction = environment.actionDefault;

        this.canExportReport = this.checkPermissions.hasReadAndWritePermissions(this.access_identifier);

        setTimeout(() => {
            this.getAllActions();
        }, 1000);
    }

    ngOnDestroy() {
        this.auditTrailService.resetInactivityMonitor();
    }

    async getActionNameOptions(dateStartDate: Date, dateEndDate: Date): Promise<void> {
        let paramCount: CountActionsRequestEntity = new CountActionsRequestEntity();
        paramCount.startTime = dateStartDate;
        paramCount.endTime = dateEndDate;
        paramCount.groupByAttributePath.push("actionName");

        const filters: FilterEntity[] = [];

        // Subject filter
        const subjectValue = this.datesForm.controls['subject'].value;
        if (subjectValue != "") {
            const subjectFilter: FilterEntity = {
                condition: {
                    path: "commonAttributes.executorId",
                    value: subjectValue,
                },
            };
            this.loggerService.debug(subjectFilter);
            filters.push(subjectFilter);
        }

        // Exclude ClockAction and PassAction
        const excludeActions: FilterEntity[] = [
            { condition: { path: "extraAttributes.actionType", value: "ClockAction" } },
            { condition: { path: "extraAttributes.actionType", value: "PassAction" } },
        ];

        if (excludeActions.length > 0) {
            filters.push({ condition: new NotEntity(excludeActions) });
        }

        // Assign single filter or AND combination
        if (filters.length === 1) {
            paramCount.filter = filters[0];
        } else if (filters.length > 1) {
            paramCount.filter = { condition: new AndEntity(filters) };
        }

        return this.countActionsUseCase.execute(paramCount).then((countActions) => {
            this.actionOptions = [{ name: "menu.all" }];
            countActions.groupByResults.forEach((action: Group) => {
                this.actionOptions.push({ name: action.groupValue });
            });
        });
    }

    async getActionApplicationOptions(dateStartDate: Date, dateEndDate: Date): Promise<void> {
        let paramCount: CountActionsRequestEntity = new CountActionsRequestEntity();
        paramCount.startTime = dateStartDate;
        paramCount.endTime = dateEndDate;
        paramCount.groupByAttributePath.push("applicationId");

        const filters: FilterEntity[] = [];

        // Subject filter
        const subjectValue = this.datesForm.controls['subject'].value;
        if (subjectValue != "") {
            const subjectFilter: FilterEntity = {
                condition: {
                    path: "commonAttributes.executorId",
                    value: subjectValue,
                },
            };
            this.loggerService.debug(subjectFilter);
            filters.push(subjectFilter);
        }

        // Exclude ClockAction and PassAction
        const excludeActions: FilterEntity[] = [
            { condition: { path: "extraAttributes.actionType", value: "ClockAction" } },
            { condition: { path: "extraAttributes.actionType", value: "PassAction" } },
        ];

        if (excludeActions.length > 0) {
            filters.push({ condition: new NotEntity(excludeActions) });
        }

        // Assign single filter or AND combination
        if (filters.length === 1) {
            paramCount.filter = filters[0];
        } else if (filters.length > 1) {
            paramCount.filter = { condition: new AndEntity(filters) };
        }

        return this.countActionsUseCase.execute(paramCount).then((countActions) => {
            this.appOptions = [{ name: "menu.all" }];
            countActions.groupByResults.forEach((action: Group) => {
                this.appOptions.push({ name: action.groupValue });
            });
        });
    }

    async getListActions(dateStartDate: Date, dateEndDate: Date): Promise<void> {
        let paramSearch: SearchActionsRequestEntity = new SearchActionsRequestEntity();
        paramSearch.startTime = dateStartDate;
        paramSearch.endTime = dateEndDate;
        paramSearch.pageNumber = 0;
        paramSearch.pageSize = environment.maxTable;

        const filters: FilterEntity[] = [];

        // Application filter
        if (this.selectedApp !== "menu.all") {
            filters.push({
                condition: { path: "applicationId", value: this.selectedApp }
            });
        }

        // Action filter
        if (this.selectedAction !== "menu.all") {
            filters.push({
                condition: { path: "actionName", value: this.selectedAction }
            });
        }

        // Subject filter
        const subjectValue = this.datesForm.controls['subject'].value;
        if (subjectValue !== "") {
            const subjectFilter: FilterEntity = {
                condition: { path: "commonAttributes.executorId", value: subjectValue }
            };
            this.loggerService.debug(subjectFilter);
            filters.push(subjectFilter);
        }

        // Exclude ClockAction and PassAction
        const excludeActions: FilterEntity[] = [
            { condition: { path: "extraAttributes.actionType", value: "ClockAction" } },
            { condition: { path: "extraAttributes.actionType", value: "PassAction" } },
        ];

        filters.push({ condition: new NotEntity(excludeActions) });

        // Assign single filter or AND combination
        if (filters.length === 1) {
            paramSearch.filter = filters[0];
        } else if (filters.length > 1) {
            paramSearch.filter = { condition: new AndEntity(filters) };
        }

        return this.searchActionsUseCase.execute(paramSearch).then((searchActions) => {
            this.virtualActionsData = searchActions;
        });
    }


    async getAllActions() {

        //const initDate = new Date()
        this.enableSpinners();

        const dateStartDate = new Date(this.datesForm.controls['rangeDates'].value[0]);
        const dateEndDate = new Date(this.datesForm.controls['rangeDates'].value[1]);

        this.dateError = false;
        this.dateErrorMessage = "";

        if (this.reportsService.monthDiff(dateStartDate, dateEndDate) > environment.rangeMaxMonths) {

            this.dateError = true;
            this.dateErrorMessage = "messages.error_dateRangeError2";
            this.hideSpinners();
            return;
        }


        if (dateStartDate < dateEndDate) {
            try {
                await Promise.all([
                    this.getActionApplicationOptions(dateStartDate, dateEndDate),
                    this.getActionNameOptions(dateStartDate, dateEndDate),
                    this.getListActions(dateStartDate, dateEndDate)
                ]);
                this.hideSpinners();
            } catch (error) {
                this.loggerService.error("Error getting actions: ");
                this.loggerService.error(error!);
                this.messageService.add({
                    severity: 'error',
                    summary: this.translate.instant("titles.error_operation"),
                    detail: this.translate.instant("messages.error_getting_actions"),
                    life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
                });
            }
        }
        else {
            this.dateError = true;
            this.dateErrorMessage = "messages.error_dateRangeError";
            this.hideSpinners();
        }
    }

    formatDate(date: Date): string {
        const lang = this.translate.currentLang || 'en';
        const options: Intl.DateTimeFormatOptions = {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            hourCycle: 'h23', // Ensures 24-hour format
        };

        if (lang === 'es') {
            // Spanish format: DD-MM-YYYY HH:mm
            return date.toLocaleDateString('es-ES', options).replace(',', '');
        } else {
            // English format: MM-DD-YYYY HH:mm
            return date.toLocaleDateString('en-US', options).replace(',', '');
        }
    }

    createReport(excel: boolean) {
        this.extraData = [];

        this.auditTrailService.auditTrailSelectReason(ReasonTypeEnum.EXPORT_REPORT, AuditTrailActions.EXPORT_REPORT, ReasonActionTypeEnum.CREATE, async () => {
            if(excel) {
                this.extraData.push({ name: 'reportType', value: 'EXCEL' });
                this.downloadExcel();
            } else {
                this.extraData.push({ name: 'reportType', value: 'PDF' });
                this.downloadPdf();
            }
        }, this.extraData);
    }


    downloadPdf() {

        var startRep = Date.now();

        this.pdfActionsData = [];

        this.virtualActionsData.forEach((action: any) => {

            let pdfAction: PdfClass = {
                locationId: action.data.commonAttributes.locationId,
                segmentId: action.data.commonAttributes.segmentId,
                deviceId: action.data.commonAttributes.deviceId,
                actionName: action.data.actionName,
                executorId: action.data.commonAttributes.executorId,
                receiverId: action.data.commonAttributes.receiverId,
                createdAt: this.formatDate(action.createdAt),
                actionResult: action.data.commonAttributes.actionResult
            }

            this.pdfActionsData.push(pdfAction);

        });

        try {

            var eDate = this.translate.instant("reports.emissionDate");
            var customer = this.translate.instant("reports.customer");
            var rType = this.translate.instant("reports.reportType");
            var iDate = this.translate.instant("reports.initDate");
            var fDate = this.translate.instant("reports.endDate");
            var aType = this.translate.instant("reports.actionType");
            var rTitle1 = this.translate.instant("reports.reportTitle1");
            var rTitle2 = this.translate.instant("reports.reportTitle2");
            var rMet = this.translate.instant("reports.reportMetadata");
            var rName = this.translate.instant("reports.reportNameAudit");
            var appSel = this.translate.instant("reports.reportColumn1");

            var rC2 = this.translate.instant("reports.reportColumn2");
            var rC3 = this.translate.instant("reports.reportColumn3");
            var rC4 = this.translate.instant("reports.reportColumn4");
            var rC5 = this.translate.instant("reports.reportColumn5");
            var rC6 = this.translate.instant("reports.reportColumn6");
            var rC7 = this.translate.instant("reports.reportColumn7");
            var rC8 = this.translate.instant("reports.reportColumn8");
            var rC9 = this.translate.instant("reports.reportColumn9");
            var rC12 = this.translate.instant("reports.reportColumn12");
            var rC13 = this.translate.instant("reports.reportColumn13");
            var rC14 = this.translate.instant("reports.reportColumn14");

            var reportIdentifier = this.translate.instant("reports.reportIdentifier");
            var rId = uuidv4();

            this.cols = [
                { field: 'locationId', header: rC2 },
                { field: 'segmentId', header: rC3 },
                { field: 'deviceId', header: rC4 },
                { field: 'actionName', header: rC5 },
                { field: 'executorId', header: rC6 },
                //{ field: 'executorProfile', header: rC7 },
                { field: 'receiverId', header: rC8 },
                //{ field: 'receiverProfile', header: rC9 },
                { field: 'createdAt', header: rC12 },
                { field: 'actionResult', header: rC13 },
            ];

            this.exportColumns = this.cols.map((col) => ({ title: col.header, dataKey: col.field }));

            import('jspdf').then((jsPDF) => {
                import('jspdf-autotable').then((x) => {
                    const doc = new jsPDF.default('l', 'px', 'a3');


                    this.http.get("verazial-common-frontend/assets/images/reports/reportPortrait.png", { responseType: "blob" }).subscribe(

                        (res: Blob) => {

                            var reader = new FileReader();
                            reader.readAsDataURL(res);
                            let ref = this;

                            reader.onloadend = function () {

                                if (reader.result != null) {

                                    doc.addMetadata(rMet);
                                    doc.setFontSize(8);

                                    ref.imageBase64 = reader.result.toString();

                                    var width = doc.internal.pageSize.getWidth();
                                    var height = doc.internal.pageSize.getHeight();
                                    doc.addImage(ref.imageBase64, "PNG", 0, 0, width, height);
                                    doc.addPage();

                                    let finalY = (doc as any).lastAutoTable.finalY; // The y position on the page

                                    if (finalY == null || finalY == undefined)
                                        finalY = 0;
                                    //doc.text("Datos del reporte", 30, 20, undefined, null)

                                    const columnsSummary = [rTitle1, ""];
                                    const rowsSummary = [
                                        [customer, environment.customerName],
                                        [eDate, new Date().toISOString().split('T')[0]],
                                        [rType, rName],
                                        [reportIdentifier, rId]
                                    ];

                                    // Set the table options
                                    const options = {
                                        startY: finalY + 40, // Vertical position to start the table (in mm)
                                    };

                                    // Generate the table
                                    (doc as any).autoTable(columnsSummary, rowsSummary, options);

                                    finalY = (doc as any).lastAutoTable.finalY; // The y position on the page

                                    const options2 = {
                                        startY: finalY + 40, // Vertical position to start the table (in mm)
                                    };

                                    //doc.text("Filtros del reporte", 30, finalY + 20, undefined, null)

                                    const columnsSummaryFilter = [rTitle2, ""];
                                    const rowsSummaryFilter = [
                                        [iDate, ref.initDate],
                                        [fDate, ref.endDate],
                                        [aType, ref.translate.instant(ref.selectedAction)],
                                        [appSel, ref.selectedApp]
                                    ];

                                    // Generate the table
                                    (doc as any).autoTable(columnsSummaryFilter, rowsSummaryFilter, options2);


                                    doc.addPage();


                                    (doc as any).autoTable(ref.exportColumns, ref.pdfActionsData);
                                    doc.save(`${new Date().toISOString()}_audit_trail_report.pdf`);


                                    ref.extraData.push({ name: 'reportIdentifier', value: rId });

                                    ref.auditTrailService.registerAuditTrailAction(ref.localStorageService.getUser().numId, ref.AuditTrailActions.EXPORT_REPORT, Date.now() - startRep, 'SUCCESS', '', ref.extraData);

                                }
                                else {
                                    console.log("Error: Image for report not available");
                                }

                            },
                                reader.onerror = function () {

                                    console.log("Error: Image for report not loaded");
                                }
                        }
                    )




                });
            });

        }
        catch (Exception) {
            this.loggerService.error("Error: " + Exception);
        }
    }

    /*downloadCsv() {
        this.csvActionsData = [];

        this.virtualActionsData.forEach((action: any) => {
            let csvAction = {
                locationId: action.data.commonAttributes.locationId,
                segmentId: action.data.commonAttributes.segmentId,
                deviceId: action.data.commonAttributes.deviceId,
                actionName: action.data.actionName,
                executorId: action.data.commonAttributes.executorId,
                receiverId: action.data.commonAttributes.receiverId,
                createdAt: this.formatDate(action.createdAt),
                actionResult: action.data.commonAttributes.actionResult,
            };

            this.csvActionsData.push(csvAction);
        });

        try {
            const rTitle2 = this.translate.instant("reports.reportTitle2");
            const iDate = this.translate.instant("reports.initDate");
            const fDate = this.translate.instant("reports.endDate");
            const aType = this.translate.instant("reports.actionType");
            const appSel = this.translate.instant("reports.reportColumn1");

            const rC2 = this.translate.instant("reports.reportColumn2");
            const rC3 = this.translate.instant("reports.reportColumn3");
            const rC4 = this.translate.instant("reports.reportColumn4");
            const rC5 = this.translate.instant("reports.reportColumn5");
            const rC6 = this.translate.instant("reports.reportColumn6");
            const rC8 = this.translate.instant("reports.reportColumn8");
            const rC12 = this.translate.instant("reports.reportColumn12");
            const rC13 = this.translate.instant("reports.reportColumn13");

            var reportIdentifier = this.translate.instant("reports.reportIdentifier");
            var rId = uuidv4();

            // Summary table (top rows)
            const columnsSummaryFilter = [rTitle2, ""];
            const rowsSummaryFilter = [
                [iDate, this.initDate],
                [fDate, this.endDate],
                [aType, this.translate.instant(this.selectedAction)],
                [appSel, this.selectedApp],
                [reportIdentifier, rId]
            ];

            const summaryTable = [
                columnsSummaryFilter.join(','),
                ...rowsSummaryFilter.map(row => row.join(',')),
                '', // Empty row for spacing
            ].join('\n');

            // Main table headers
            const headers = [
                rC2,
                rC3,
                rC4,
                rC5,
                rC6,
                rC8,
                rC12,
                rC13,
            ];

            // Add background color and text color to headers (via HTML syntax)
            const styledHeaders = `<tr><th style="background-color:#4CAF50;color:white;">${headers.join('</th><th style="background-color:#4CAF50;color:white;">')}</th></tr>`;

            // Main table rows
            const dataRows = this.csvActionsData.map((row) =>
                [
                    row.locationId,
                    row.segmentId,
                    row.deviceId,
                    row.actionName,
                    row.executorId,
                    row.receiverId,
                    row.createdAt,
                    row.actionResult,
                ].join(',')
            );

            const mainTable = [
                headers.join(','), // Standard CSV headers
                ...dataRows,
            ].join('\n');

            // Combine summary table and main table
            const csvContent = `${summaryTable}\n${mainTable}`;

            // Create a Blob from the CSV string
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });

            // Create a link element to download the CSV
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', `${new Date().toISOString()}_audit_trail_report.csv`);
            link.style.visibility = 'hidden';

            // Append the link to the DOM, trigger the click, and remove the link
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            console.log("CSV generated successfully.");
        } catch (error) {
            console.log("Error generating CSV: " + error);
        }
    }*/

    downloadExcel() {
        var startRep = Date.now();
        this.csvActionsData = [];

        this.virtualActionsData.forEach((action: any) => {
            let excelAction = {
                locationId: action.data.commonAttributes.locationId,
                segmentId: action.data.commonAttributes.segmentId,
                deviceId: action.data.commonAttributes.deviceId,
                actionName: action.data.actionName,
                executorId: action.data.commonAttributes.executorId,
                receiverId: action.data.commonAttributes.receiverId,
                createdAt: this.formatDate(action.createdAt),
                actionResult: action.data.commonAttributes.actionResult,
            };

            this.csvActionsData.push(excelAction);
        });

        try {
            const rTitle2 = this.translate.instant("reports.reportTitle2");
            const iDate = this.translate.instant("reports.initDate");
            const fDate = this.translate.instant("reports.endDate");
            const aType = this.translate.instant("reports.actionType");
            const appSel = this.translate.instant("reports.reportColumn1");

            const rC2 = this.translate.instant("reports.reportColumn2");
            const rC3 = this.translate.instant("reports.reportColumn3");
            const rC4 = this.translate.instant("reports.reportColumn4");
            const rC5 = this.translate.instant("reports.reportColumn5");
            const rC6 = this.translate.instant("reports.reportColumn6");
            const rC8 = this.translate.instant("reports.reportColumn8");
            const rC12 = this.translate.instant("reports.reportColumn12");
            const rC13 = this.translate.instant("reports.reportColumn13");

            const reportIdentifier = this.translate.instant("reports.reportIdentifier");
            const rId = uuidv4();

            // Summary Table Data
            const summaryTable = [
                [rTitle2, ""],
                [iDate, this.initDate],
                [fDate, this.endDate],
                [aType, this.translate.instant(this.selectedAction)],
                [appSel, this.selectedApp],
                [reportIdentifier, rId],
            ];

            // Main Table Headers and Rows
            const headers = [
                rC2,
                rC3,
                rC4,
                rC5,
                rC6,
                rC8,
                rC12,
                rC13
            ];

            const dataRows = this.csvActionsData.map((row) => [
                row.locationId,
                row.segmentId,
                row.deviceId,
                row.actionName,
                row.executorId,
                row.receiverId,
                row.createdAt,
                row.actionResult,
            ]);

            // Combine all data
            const finalData = [...summaryTable, headers, ...dataRows];

            // Create a worksheet
            const worksheet = XLSX.utils.aoa_to_sheet(finalData);

            // Style the headers
            const range = XLSX.utils.decode_range(worksheet['!ref'] || '');
            for (let C = range.s.c; C <= range.e.c; ++C) {
                const headerCell = XLSX.utils.encode_cell({ r: summaryTable.length, c: C });
                if (!worksheet[headerCell]) continue;
                worksheet[headerCell].s = {
                    fill: { fgColor: { rgb: "4CAF50" } },
                    font: { color: { rgb: "FFFFFF" }, bold: true },
                    alignment: { horizontal: "center" }
                };
            }

            // Create a workbook and add the worksheet
            const workbook = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(workbook, worksheet, "Report");

            // Write the Excel file and trigger download
            const fileName = `${new Date().toISOString()}_audit_trail_report.xlsx`;
            XLSX.writeFile(workbook, fileName);

            this.extraData.push({ name: 'reportIdentifier', value: rId });

            this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, this.AuditTrailActions.EXPORT_REPORT, Date.now() - startRep, 'SUCCESS', '', this.extraData);


            this.loggerService.info("Excel file generated successfully.");
        } catch (error) {
            this.loggerService.error("Error generating Excel file: " + error);
        }
    }



    enableSpinners() {

        this.addingData = true;
        this.showNoData1 = this.showNoData2 = this.showNoData3 = this.showNoData4 = false;

        this.verificationComputedNumber = "";
        this.identificationComputedNumber = "";
        this.deletionComputedNumber = "";
        this.deletionSamplesComputedNumber = "";
        this.newSubjectComputedNumber = "";
        this.newSampleComputedNumber = "";
        this.passwordLoginComputedNumber = "";
        this.noDevComputedNumber = "";
        this.actionsComputedNumber = "";
        this.newPictureComputedNumber = "";
        this.modBioComputedNumber = "";
        this.newPictureComputedNumber = "";

        this.actionsData = [];
        this.virtualActionsData = [];
        this.showSpinners = true;

    }

    hideSpinners() {
        this.addingData = false;
        this.showSpinners = false;
        this.actionsData.length == 0 ? this.showNoData1 = true : this.showNoData1 = false;
        this.showNoData1 = false;
    }

    showDetails(action: ActionEntity) {
        this.detailsDialogAction = action;
        this.detailsDialogText = JSON.stringify(action.data, (key, value) => {
            return value === null ? undefined : value;
          }, 2);
          this.loggerService.debug(this.detailsDialogText);
        this.showDetailsDialog = true;
    }



}
