<p-toast></p-toast>
<p-confirmDialog />
<app-inactivity-monitor
    [inactivityTimeoutLimit]="confirmDialogTimeoutLimit"
    [startChecking]="startCheckingInactivity"
    (expired)="closeConfirmationDialog()"
></app-inactivity-monitor>
<app-inactivity-monitor
    [inactivityTimeoutLimit]="auditTrailService.confirmDialogTimeoutLimit"
    [startChecking]="auditTrailService.startCheckingInactivity"
    (expired)="auditTrailService.closeConfirmationDialog()"
></app-inactivity-monitor>
@if(isLoading){
    <app-loading-spinner [isLoading]="isLoading"></app-loading-spinner>
}@else{
    <app-list-roles
        [readAndWritePermissions]="canReadAndWrite"
        [listRoles]="listRoles"
        [listOfAccesses]="roleAccess"
        (saveRoleOutput)="onSubmitSaveRole($event)"
        (deleteRoleOutput)="confirmDeleteRole($event)"
        (updateRoleOutput)="onSubmitUpdateRole($event)"
    ></app-list-roles>
}