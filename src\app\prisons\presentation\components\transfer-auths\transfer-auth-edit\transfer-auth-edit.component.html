<app-loading-spinner [isLoading]="isLoading"></app-loading-spinner>
<app-inactivity-monitor
    [inactivityTimeoutLimit]="auditTrailService.confirmDialogTimeoutLimit"
    [startChecking]="auditTrailService.startCheckingInactivity"
    (expired)="auditTrailService.closeConfirmationDialog()"
></app-inactivity-monitor>
<div [formGroup]="form" class="dialog-container">
    @if( operationType == opType.INSERT ){
        <p-steps [model]="items" [readonly]="true" [activeIndex]="activeIndex"></p-steps>
    }@else{
        <div class="flex justify-content-center">
            <p-selectButton
                [options]="stepOptions"
                formControlName="stepOptions"
                severity="secondary"
                multiple="false"
                allowEmpty="false"
                optionLabel="key"
                optionValue="value"
                dataKey="value"
                (onChange)="onActiveTabIndexChange($event)">
            </p-selectButton>
        </div>
    }
    <div class="mt-5">
        <p-scrollPanel [style]="{ maxWidth: '75vw', maxHeight: '55vh' }">
            <!-- Dynamic Form -->
            <ng-template #dynamicFormContent></ng-template>
            @switch (activeIndex) {
                <!-- General -->
                @case (0) {
                    <div class="grid">
                        <div class="col-12 flex justify-content-end requiredFieldsLabel">
                            {{ 'content.requiredFields' | translate }} <span class="requiredStar">*</span>
                        </div>
                        <!-- authCode -->
                        <div *ngIf="transferAuth?.id && operationType != opType.INSERT" class="col-4 col-offset-4">
                            <label for="authCode" class="label-form"> {{ 'content.authCode' | translate }}</label>
                            <input
                                type="text"
                                pInputText
                                [(ngModel)]="transferAuth!.id"
                                [ngModelOptions]="{standalone: true}"
                                [disabled]="true"
                            />
                        </div>
                        <!-- originLocationId -->
                        <div class="col-6">
                            <label class="label-form">{{ 'content.originLocation' | translate }} <span *ngIf="isRequiredField('originLocationId')" class="requiredStar">*</span></label>
                            <p-treeSelect appendTo="body"
                                class="md:w-20rem w-full"
                                containerStyleClass="w-full"
                                formControlName="originLocationId"
                                [options]="listLocations"
                                [(ngModel)]="selectedOriginLocationId"
                                [filter]="true"
                                placeholder="{{ 'content.select' | translate }}"
                                (onNodeSelect)="updateListOfPrisoners($event)"
                            />
                            <small *ngIf="!isValid('originLocationId') && form.controls['originLocationId'].touched" class="mb-1" style="color:red">{{ 'content.field_required' | translate }}</small>
                        </div>
                        <!-- destinyLocationId -->
                        <div class="col-6">
                            <label class="label-form">{{ 'content.destinyLocation' | translate }} <span *ngIf="isRequiredField('destinyLocationId')" class="requiredStar">*</span></label>
                            <p-treeSelect appendTo="body"
                                class="md:w-20rem w-full"
                                containerStyleClass="w-full"
                                formControlName="destinyLocationId"
                                [options]="listLocationsDestiny"
                                [(ngModel)]="selectedDestinyLocationId"
                                [filter]="true"
                                placeholder="{{ 'content.select' | translate }}"/>
                            <small *ngIf="!isValid('destinyLocationId') && form.controls['destinyLocationId'].touched" class="mb-1" style="color:red">{{ 'content.field_required' | translate }}</small>
                        </div>
                        <!-- plannedDepartureDateTime -->
                        <div class="col-6">
                            <label class="label-form">{{ 'content.plannedDepartureDateTime' | translate }} <span *ngIf="isRequiredField('plannedDepartureDateTime')" class="requiredStar">*</span></label>
                            <p-calendar
                                inputId="calendar-24h"
                                formControlName="plannedDepartureDateTime"
                                [showTime]="true"
                                dateFormat="{{ 'dateFormat' | translate }}"
                                hourFormat="{{ 'hourFormat' | translate }}"
                                [showButtonBar]="true"
                                appendTo="body"
                            />
                            <small *ngIf="!isValid('plannedDepartureDateTime') && form.controls['plannedDepartureDateTime'].touched" [style]="{'color': 'red'}">{{ 'messages.error_isRequiredField' | translate }}</small>
                        </div>
                        <!-- plannedArrivalDateTime -->
                        <div class="col-6">
                            <label class="label-form">{{ 'content.plannedArrivalDateTime' | translate }} <span *ngIf="isRequiredField('plannedArrivalDateTime')" class="requiredStar">*</span></label>
                            <p-calendar
                                inputId="calendar-24h"
                                formControlName="plannedArrivalDateTime"
                                [showTime]="true"
                                dateFormat="{{ 'dateFormat' | translate }}"
                                hourFormat="{{ 'hourFormat' | translate }}"
                                [showButtonBar]="true"
                                appendTo="body"
                            />
                            <small *ngIf="!isValid('plannedArrivalDateTime') && form.controls['plannedArrivalDateTime'].touched" [style]="{'color': 'red'}">{{ 'messages.error_isRequiredField' | translate }}</small>
                        </div>
                        <!-- authReason -->
                        <div class="col-6">
                            <label class="label-form">{{ 'content.authReason' | translate }} <span *ngIf="isRequiredField('authReason')" class="requiredStar">*</span></label>
                            <p-dropdown formControlName="authReason" [(ngModel)]="selectAuthReason"
                                appendTo="body" [options]="authReasonOptions" optionLabel="value" placeholder="{{ 'content.select' | translate }}" />
                            <small *ngIf="!isValid('authReason') && form.controls['authReason'].touched" [style]="{'color': 'red'}">{{ 'messages.error_isRequiredField' | translate }}</small>
                        </div>
                        <!-- observations -->
                        <div class="col-6">
                            <label for="observations" class="label-form">{{ 'content.observation' | translate }} <span *ngIf="isRequiredField('observations')" class="requiredStar">*</span></label>
                            <textarea id="observations" rows="3" cols="12" pInputTextarea formControlName="observations"></textarea>
                        </div>
                    </div>
                }
                <!-- Details -->
                @case (showTransferAuthDetails ? 1 : -1) {
                    <div class="flex justify-content-center mt-2">
                        <ng-container *ngTemplateOutlet="dynamicFormContent" />
                    </div>
                    <!-- <div class="grid w-full">
                        <div class="col-12 m-3 w-full" style="padding-bottom: 0rem;">
                            <div *ngIf="canReadAndWrite && userIsVerified && editEnabled" class="grid" [formGroup]="detailItemForm">
                                <div class="col-5 width100 p-1">
                                    <label class="label-form" for="key">
                                        {{ 'content.name' | translate }}
                                        <span *ngIf="isRequiredField('key', detailItemForm)" class="requiredStar">*</span></label>
                                    <input type="text" pInputText formControlName="key" />
                                    <small *ngIf="!isValid('key', detailItemForm) && detailItemForm.controls['key'].touched" [style]="{'color': 'red'}">{{ 'messages.error_isRequiredField' | translate }}</small>
                                </div>
                                <div class="col-5 width100 p-1">
                                    <label class="label-form" for="value">
                                        {{ 'content.value' | translate }}
                                        <span *ngIf="isRequiredField('value', detailItemForm)" class="requiredStar">*</span>
                                    </label>
                                    <input type="text" pInputText formControlName="value" />
                                    <small *ngIf="!isValid('value', detailItemForm) && detailItemForm.controls['value'].touched" [style]="{'color': 'red'}">{{ 'messages.error_isRequiredField' | translate }}</small>
                                </div>
                                <div class="col-2 flex justify-content-center align-items-end width100 p-1">
                                    <p-button
                                        [disabled]="detailItemForm.invalid"
                                        [style]="{'pointer-events': 'auto', 'width':'120px','height':'36px', 'color': '#FFFFFF' , 'border': 'none', 'background': '#009BA9', 'font-family': 'Open Sans', 'font-size': '14px'}"
                                        label="{{ 'add'| translate }}"
                                        (onClick)="addDetailItem()"
                                    ></p-button>
                                </div>
                            </div>
                        </div>
                        <div class="col-12 w-full" style="padding-bottom: 0rem;">
                            <p-table
                                [value]="detailsList"
                                dataKey="id"
                                editMode="row"
                                currentPageReportTemplate="{{ 'content.showing' | translate }} {first} {{ 'content.to' | translate }} {last} {{ 'content.of' | translate}} {totalRecords} {{ 'content.records' | translate }}"
                                [rows]="5"
                                [paginator]="true"
                                [styleClass]="'p-datatable-sm p-datatable-striped'"
                                [globalFilterFields]="['key', 'value']">
                            >
                                <ng-template pTemplate="header">
                                    <tr>
                                        <th class="header-small-list">{{ 'content.name' | translate }}</th>
                                        <th class="header-small-list">{{ 'content.value' | translate }}</th>
                                        <th class="header-small-list"></th>
                                    </tr>
                                    <tr>
                                        <th>
                                            <p-columnFilter type="text" field="key" [showMenu]="false" matchMode="contains" />
                                        </th>
                                        <th>
                                            <p-columnFilter type="text" field="value" [showMenu]="false" matchMode="contains" />
                                        </th>
                                        <th></th>
                                    </tr>
                                </ng-template>
                                <ng-template pTemplate="body" let-data let-editing="editing" let-ri="rowIndex">
                                    <tr [pEditableRow]="data" [formGroup]="detailItemRowForm">
                                        <td>
                                            <p-cellEditor>
                                                <ng-template pTemplate="input">
                                                    <input
                                                        pInputText
                                                        type="text"
                                                        formControlName="key"
                                                    />
                                                </ng-template>
                                                <ng-template pTemplate="output">
                                                    {{data.key}}
                                                </ng-template>
                                            </p-cellEditor>
                                        </td>
                                        <td>
                                            <p-cellEditor>
                                                <ng-template pTemplate="input">
                                                    <input
                                                        pInputText
                                                        type="text"
                                                        formControlName="value"
                                                    />
                                                </ng-template>
                                                <ng-template pTemplate="output">
                                                    {{data.value}}
                                                </ng-template>
                                            </p-cellEditor>
                                        </td>
                                        <td>
                                            <div class="flex align-items-center justify-content-center gap-2">
                                                <button
                                                    *ngIf="!editing && canReadAndWrite && userIsVerified && editEnabled"
                                                    pButton
                                                    pRipple
                                                    type="button"
                                                    pInitEditableRow
                                                    icon="pi pi-pencil"
                                                    (click)="onDetailItemRowEditInit(data)"
                                                    class="p-button-rounded p-button-text">
                                                </button>
                                                <button
                                                    *ngIf="!editing && canReadAndWrite && userIsVerified && editEnabled"
                                                    pButton
                                                    pRipple
                                                    type="button"
                                                    icon="pi pi-trash"
                                                    (click)="onDetailItemRowRemoveItem(data)"
                                                    class="p-button-rounded p-button-text p-button-danger">
                                                </button>
                                                <button
                                                    *ngIf="editing && canReadAndWrite && userIsVerified && editEnabled"
                                                    pButton
                                                    pRipple
                                                    type="button"
                                                    pSaveEditableRow
                                                    icon="pi pi-check"
                                                    (click)="onDetailItemRowEditSave(data, ri)"
                                                    class="p-button-rounded p-button-text p-button-success mr-2">
                                                </button>
                                                <button
                                                    *ngIf="editing && canReadAndWrite && userIsVerified && editEnabled"
                                                    pButton pRipple
                                                    type="button"
                                                    pCancelEditableRow
                                                    icon="pi pi-times"
                                                    (click)="onDetailItemRowEditCancel(data, ri)"
                                                    class="p-button-rounded p-button-text p-button-danger">
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                </ng-template>
                                <ng-template pTemplate="emptymessage">
                                    <tr>
                                        <td colspan="5" class="py-4">{{ 'content.noDataAvailable' | translate }}</td>
                                    </tr>
                                </ng-template>
                            </p-table>
                        </div>
                    </div> -->
                }
                <!-- List of Prisoners and List of Responsible Users -->
                @case (showTransferAuthDetails ? (activeIndex == 2 || activeIndex == 3 ? activeIndex : -1) : (activeIndex == 1 || activeIndex == 2 ? activeIndex : -1)) {
                    <app-list-user-subject
                        [isLoading]="isLoading"
                        [type]="subjectType"
                        [showHeader]="false"
                        [showBioSearch]="false"
                        [showTableRecordButton]="false"
                        [showMainButton]="false"
                        [mainActionOnRowClick]="false"
                        [showDeleteButton]="false"
                        [lazyLoad]="useLazyLoad"
                        [readAndWritePermissions]="canReadAndWrite"
                        [readOnly]="true"
                        [listOfUsersSubjects]="(showTransferAuthDetails ? activeIndex == 2 : activeIndex == 1) ? listOfAllPrisoners : listOfAllResponsibleUsers"
                        [selectedUsersSubjects]="getSelection()"
                        [totalRecords]="(showTransferAuthDetails ? activeIndex == 2 : activeIndex == 1) ? listOfAllPrisoners.lenght : listOfAllResponsibleUsers.lenght"
                        [offset]="getSubjectsRequest.offset"
                        [limit]="getSubjectsRequest.limit"
                        [allRoles]="listOfAllRoles"
                        [managerSettings]="managerSettings"
                        [konektorProperties]="konektorProperties"
                        [recordManagementMode]="false"
                        (onSelectionChange)="setSelection($event)"
                    ></app-list-user-subject>
                    <!-- <p-table
                        #dt
                        [value]="(showTransferAuthDetails ? activeIndex == 2 : activeIndex == 1) ? listOfAllPrisoners : listOfAllResponsibleUsers"
                        (onFilter)="onFilter($event, dt)"
                        [selection]="getSelection()"
                        (selectionChange)="setSelection($event)"
                        selectionMode="multiple"
                        dataKey="id"
                        [rowHover]="true"
                        [paginator]="true"
                        [rows]="5"
                        [scrollable]="true"
                        scrollDirection="horizontal"
                        [tableStyle]="{ 'min-width': '75rem' }"
                        styleClass="fixed-table"
                        [showCurrentPageReport]="true"
                        currentPageReportTemplate="{{ 'content.showing' | translate }} {first} {{ 'content.to' | translate }} {last} {{ 'content.of' | translate}} {totalRecords} {{ 'content.records' | translate }}"
                        [globalFilterFields]="['numId', 'names', 'lastNames', 'birthdate', 'gender']"
                        [sortField]="'numId'" [sortOrder]="1">
                        <ng-template pTemplate="header">
                            <tr>
                                <th style="width: 4rem"></th>
                                <th class="fixed-column" pSortableColumn="numId"> {{ 'table.num_id' | translate }} <p-sortIcon field="numId"></p-sortIcon></th>
                                <th class="fixed-column" pSortableColumn="names">{{ 'table.names' | translate }} <p-sortIcon field="name"></p-sortIcon></th>
                                <th class="fixed-column" pSortableColumn="lastNames">{{ 'table.lastNames' | translate }} <p-sortIcon field="lastname"></p-sortIcon></th>
                                <th *ngIf="showTransferAuthDetails ? activeIndex != 2 : activeIndex != 1" class="fixed-column" pSortableColumn="roles">{{ ( 'table.profile') | translate }} <p-sortIcon field="roles"></p-sortIcon></th>
                                <th class="fixed-column" pSortableColumn="birthdate">{{ 'table.birthdate' | translate }} <p-sortIcon field="birthdate"></p-sortIcon></th>
                                <th class="fixed-column" pSortableColumn="gender">{{ 'table.gender' | translate }} <p-sortIcon field="gender"></p-sortIcon></th>
                                <th alignFrozen="right" pFrozenColumn [frozen]="true"></th>
                            </tr>
                            <tr>
                                <th style="width: 4rem">
                                    <p-tableHeaderCheckbox/>
                                </th>
                                <th>
                                    <p-columnFilter type="text" field="numId" [showMenu]="false" matchMode="contains">
                                        <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                            <input  pInputText type="text" (input)="filter($event.target.value)" [value]="value">
                                        </ng-template>
                                    </p-columnFilter>
                                </th>
                                <th>
                                    <p-columnFilter type="text" field="names" [showMenu]="false" matchMode="contains">
                                        <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                            <input pInputText type="text" (input)="filter($event.target.value)" [value]="value">
                                        </ng-template>
                                    </p-columnFilter>
                                </th>
                                <th>
                                    <p-columnFilter type="text" field="lastNames" [showMenu]="false" matchMode="contains">
                                        <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                            <input pInputText type="text" (input)="filter($event.target.value)" [value]="value">
                                        </ng-template>
                                    </p-columnFilter>
                                </th>
                                <th *ngIf="showTransferAuthDetails ? activeIndex != 2 : activeIndex != 1">
                                    <p-columnFilter type="text" field="roles" [showMenu]="false" matchMode="customStringArray">
                                        <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                            <p-dropdown appendTo="body"
                                                [ngModel]="value"
                                                [ngModelOptions]="{standalone: true}"
                                                [options]="allResponsibleUserRoles"
                                                (onChange)="filter($event.value)"
                                                placeholder="{{ 'content.select' | translate }}"
                                                [showClear]="true"
                                                optionLabel="name"
                                                optionValue="name"
                                            >
                                                <ng-template pTemplate="selectedItem">
                                                    {{ value == 'SYSTEM_USER' ? ('role_names.SYSTEM_USER' | translate) : value }}
                                                </ng-template>
                                                <ng-template let-option pTemplate="item">
                                                    {{ option.name == 'SYSTEM_USER' ? ('role_names.SYSTEM_USER' | translate) : option.name }}
                                                </ng-template>
                                            </p-dropdown>
                                        </ng-template>
                                    </p-columnFilter>
                                </th>
                                <th>
                                    <p-columnFilter type="date" field="birthdate" [showMenu]="false" matchMode="customDateRange">
                                        <ng-template pTemplate="filter" let-filter="filterCallback" [formGroup]="dateFormGroup">
                                            <p-calendar
                                                formControlName="date"
                                                selectionMode="range"
                                                (onSelect)="applyDateRangeFilter(dt, 'birthdate')"
                                                (onInput)="applyDateRangeFilter(dt, 'birthdate')"
                                                (onClickOutside)="applyDateRangeFilter(dt, 'birthdate')"
                                                placeholder="{{ 'content.select' | translate }}"
                                                dateFormat="{{ 'dateFormat' | translate }}"
                                                appendTo="body"
                                            ></p-calendar>
                                        </ng-template>
                                    </p-columnFilter>
                                </th>
                                <th>
                                    <p-columnFilter field="gender" [showMenu]="false" matchMode="equals">
                                        <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                            <p-dropdown appendTo="body"
                                                [(ngModel)]="value"
                                                [ngModelOptions]="{standalone: true}"
                                                [options]="[
                                                    {label: 'options.male', value: 'male'},
                                                    {label: 'options.female', value: 'female'}
                                                ]"
                                                (onChange)="filter($event.value)"
                                                placeholder="{{ 'content.select' | translate }}"
                                                [showClear]="true"
                                                optionLabel="label"
                                                optionValue="value"
                                            >
                                                <ng-template pTemplate="selectedItem">
                                                    {{ 'options.' + value | translate }}
                                                </ng-template>
                                                <ng-template let-option pTemplate="item">
                                                    {{ option.label | translate }}
                                                </ng-template>
                                            </p-dropdown>
                                        </ng-template>
                                    </p-columnFilter>
                                </th>
                                <th alignFrozen="right" pFrozenColumn [frozen]="true"></th>
                            </tr>
                        </ng-template>
                        <ng-template pTemplate="body" let-data let-rowIndex="rowIndex">
                            <tr class="p-selectable-row" [pSelectableRow]="data" [pSelectableRowIndex]="rowIndex">
                                <td>
                                    <p-tableCheckbox [value]="data"></p-tableCheckbox>
                                </td>
                                <td showDelay="1000" pTooltip="{{data.numId}}" tooltipPosition="top" class="ellipsis-cell">{{ data.numId }}</td>
                                <td showDelay="1000" pTooltip="{{data.names}}" tooltipPosition="top" class="ellipsis-cell">{{ data.names }}</td>
                                <td showDelay="1000" pTooltip="{{data.lastNames}}" tooltipPosition="top" class="ellipsis-cell">{{ data.lastNames }}</td>
                                <td *ngIf="showTransferAuthDetails ? activeIndex != 2 : activeIndex != 1" showDelay="1000" pTooltip="{{listOfRolesToString(data.roles)}}" tooltipPosition="top" class="ellipsis-cell">{{ listOfRolesToString(data.roles) }}</td>
                                @if(isValidDate(data.birthdate)){
                                    <td showDelay="1000" pTooltip="{{data.birthdate | date:('dateFormatLong' | translate)}}" tooltipPosition="top" class="ellipsis-cell">{{ data.birthdate | date:('dateFormatLong' | translate) }}</td>
                                }@else{
                                    <td showDelay="1000" pTooltip="" tooltipPosition="top" class="ellipsis-cell"></td>
                                }
                                <td showDelay="1000" pTooltip="{{data.gender != '' && data.gender != undefined && data.gender != null ? ('options.' + data.gender | translate) : ''}}" tooltipPosition="top" class="ellipsis-cell">{{ data.gender != '' && data.gender != undefined && data.gender != null ? ('options.' + data.gender | translate) : '' }}</td>
                            </tr>
                        </ng-template>
                        <ng-template pTemplate="emptymessage">
                            <tr>
                                <td colspan="5" class="py-4">{{ 'content.noDataAvailable' | translate }}</td>
                            </tr>
                        </ng-template>
                    </p-table> -->
                }
                <!-- Signatures -->
                @case (showTransferAuthDetails ? 4 : 3) {
                    <div class="flex justify-content-center align-items-center gap-2">
                        <!-- User Authorization Signature -->
                        <div>
                            <app-bio-signatures
                                [showSignatureButton]="(!hasAuthSignature) && (!hasCancelSignature)"
                                [showExtraFormFields]="false"
                                [showSignatureInfo]="hasAuthSignature"
                                [signatureTitle]="'content.authSignature'"
                                [signatureInputLabel]="'content.subjectWhoAuthrizes'"
                                [konektorProperties]="konektorProperties"
                                [managerSettings]="managerSettings"
                                [signatureData]="authSignatureData"
                                [restrictSubjectRoles]="restrictAuthRoles"
                                [subjectRoleSegmentedSearch]="segmentedSearchAuthRole"
                                [userIsVerified]="userIsVerified"
                                (outputResult)="userAuthSignatureResult($event)"
                            ></app-bio-signatures>
                        </div>
                        <!-- User Cancellation Signature -->
                        <div *ngIf="transferAuth?.id != null && transferAuth?.id != undefined && transferAuth?.id != ''">
                            <!-- User Cancellation Signature Button -->
                            <!-- <div *ngIf="!hasCancelSignature && !isExpired && !isInProgress && !transferAuth?.isCompleted!"
                                class="flex flex-column align-items-center gap-2">
                                <label class="signature-title">{{ 'content.cancelSignature' | translate }} </label>
                                <div *ngIf="userIsVerified else mustVerifyUser" class="signature-container flex flex-column gap-2">
                                    <label class="label-form">{{ 'content.subjectWhoCancels' | translate }}</label>
                                    <input
                                        type="text" pInputText
                                        placeholder="{{ 'content.enter_numId_to_verify' | translate }}"
                                        pTooltip="{{ 'content.enter_numId_to_verify' | translate }}" tooltipPosition="top"
                                        [(ngModel)]="userNumId" [ngModelOptions]="{standalone: true}"/>
                                    <label class="label-form">{{ 'content.cancelReason' | translate }} <span class="requiredStar">*</span></label>
                                    <p-dropdown [(ngModel)]="selectCancelReason" [ngModelOptions]="{standalone: true}"
                                        appendTo="body" [options]="cancelReasonOptions" optionLabel="value" placeholder="{{ 'content.select' | translate }}" />
                                    <label class="label-form">{{ 'content.cancelObservation' | translate }}</label>
                                    <input
                                        type="text" pInputText
                                        [(ngModel)]="cancelObservation" [ngModelOptions]="{standalone: true}"/>
                                    <div class="flex flex-column justify-content-center align-items-center">
                                        <app-bio-tech-buttons
                                            [fingerprintEnabled]="managerSettings?.payedTechnology?.dactilar == true && this.konektorProperties?.enabledTech?.dactilar == true && (konektorProperties?.verificationEnabled ? managerSettings?.allowVerify?.dactilar == true : managerSettings?.allowSearch?.dactilar == true)"
                                            [facialEnabled]="managerSettings?.payedTechnology?.facial == true && this.konektorProperties?.enabledTech?.facial == true && (konektorProperties?.verificationEnabled ? managerSettings?.allowVerify?.facial == true : managerSettings?.allowSearch?.facial == true)"
                                            [irisEnabled]="managerSettings?.payedTechnology?.iris == true && this.konektorProperties?.enabledTech?.iris == true && (konektorProperties?.verificationEnabled ? managerSettings?.allowVerify?.iris == true : managerSettings?.allowSearch?.iris == true)"
                                            (tech)="widgetSearch($event, false)"
                                        ></app-bio-tech-buttons>
                                    </div>
                                </div>
                            </div> -->
                            <!-- User Cancellation Signature Information -->
                            <!-- <div *ngIf="hasCancelSignature"
                            class="flex flex-column align-items-center justify-content-center gap-2">
                                <ng-container *ngTemplateOutlet="cancelSignatureInfo" />
                            </div> -->

                            <app-bio-signatures
                                [showSignatureButton]="!hasCancelSignature && !isExpired && !isInProgress && !transferAuth?.isCompleted!"
                                [showExtraFormFields]="true"
                                [reasonOptionsParameter]="cancelReasonParameter"
                                [showSignatureInfo]="hasCancelSignature"
                                [signatureTitle]="'content.cancelSignature'"
                                [signatureInputLabel]="'content.subjectWhoCancels'"
                                [reasonsDropdownLabel]="'content.cancelReason'"
                                [observationInputLabel]="'content.cancelObservation'"
                                [konektorProperties]="konektorProperties"
                                [managerSettings]="managerSettings"
                                [signatureData]="cancelSignatureData"
                                [restrictSubjectRoles]="restrictCancelRoles"
                                [subjectRoleSegmentedSearch]="segmentedSearchCancelRole"
                                [userIsVerified]="userIsVerified"
                                (outputResult)="userCancelSignatureResult($event)"
                            ></app-bio-signatures>
                        </div>
                    </div>
                }
            }
        </p-scrollPanel>
    </div>
</div>
<div>
    <div class="footer-buttons-container">
        @if (activeIndex==0) {
            <p-button label="{{ 'cancel' | translate }}"
                (onClick)="onClose()"
                [style]="{ 'width':'122px', 'height':'36px', 'border':'none', 'color': '#64748B' , 'background': '#FFFFFF', 'font-family': 'Open Sans', 'margin': '4px 0 4px 0' }">
            </p-button>
        }
        @else {
            <p-button label="{{ 'back' | translate }}" icon="pi pi-angle-left"
                (onClick)="onBack()"
                [disabled]="modified"
                [style]="{ 'width':'122px', 'height':'36px', 'border':'none', 'color': '#64748B' , 'background': '#FFFFFF', 'font-family': 'Open Sans', 'margin': '4px 0 4px 0' }">
                </p-button>
        }
        @if (activeIndex!=(items?.length-1)) {
            <p-button label="{{ 'next' | translate }}" icon="pi pi-angle-right" iconPos="right"
                (onClick)="onNext()"
                [disabled]="modified"
                [style]="{ 'width':'122px', 'height':'36px', 'border':'none', 'color': '#FFFFFF' , 'background': '#204887', 'font-family': 'Open Sans', 'margin': '4px 0 4px 0' }">
            </p-button>
        }@else{
            <p-button
                [disabled]="!canReadAndWrite || !userIsVerified || transferAuth?.isCompleted! || transferAuth?.isCancelled || isExpired || isInProgress"
                [label]="createUpdateButtonTitle"  iconPos="right"
                [style]="{ 'width':'122px', 'height':'36px', 'border':'none', 'color': '#FFFFFF' , 'background': '#204887', 'font-family': 'Open Sans', 'margin': '4px 0 4px 0' }"
                (onClick)="saveTransferAuth()">
            </p-button>
        }
    </div>
</div>

<ng-template #mustVerifyUser>
    <div style="width: 200px;" class="signature-container flex flex-column justify-content-center align-items-center">
        {{ 'messages.verification_required_data' | translate }}
    </div>
</ng-template>

<ng-template #authSignatureInfo>
    <div class="flex flex-column align-items-start gap-2">
        <label class="signature-title">{{ 'content.authSignature' | translate }} </label>
        <div class="signature-container flex flex-column gap-2">
            <div class="flex flex-column gap-4">
                <div class="flex justify-content-center align-items-center gap-3" style="padding-bottom: 0rem;">
                    <img class="imgUser" src={{authUserImage}} />
                    <div class="flex flex-column gap-1">
                        <div class="namesLabel mb-2"> {{ authUser.names + ' ' + authUser.lastNames }} </div>
                        <div>
                            <span class="dataKey">{{ 'table.numId' | translate }}: </span>
                            <span class="dataValue">{{ authUser.numId }}</span>
                        </div>
                        <div class="flex align-items-center gap-2">
                            <span class="dataKey">{{ 'content.technology' | translate }}: </span>
                            <img style="width: 20px;" pTooltip="{{ ('content.authUserSignatureTech' | translate) + ': ' + ('content.' + authSignatureData.tech | translate) }}" [src]="getTechIcon(authSignatureData.tech)"/>
                        </div>
                    </div>
                </div>
                <div style="padding-bottom: 0rem;">
                    <div class="signatureDateLabel">{{ ('content.signedOn' | translate) + ' ' + (authSignatureData.date  | date: 'short':'':language) }}</div>
                </div>
            </div>
        </div>
    </div>
</ng-template>

<ng-template #cancelSignatureInfo>
    <div class="flex flex-column align-items-start gap-2">
        <label class="signature-title">{{ 'content.cancelSignature' | translate }} </label>
        <div class="signature-container flex flex-column gap-2">
            <div class="flex flex-column gap-4">
                <div class="flex justify-content-center align-items-center gap-3" style="padding-bottom: 0rem;">
                    <img class="imgUser" src={{cancelUserImage}} />
                    <div class="flex flex-column gap-1">
                        <div class="namesLabel mb-2 ml-3"> {{ cancelUser.names + ' ' + cancelUser.lastNames }} </div>
                        <div>
                            <span class="dataKey">{{ 'table.numId' | translate }}: </span>
                            <span class="dataValue">{{ cancelUser.numId }}</span>
                        </div>
                        <div class="flex align-items-center gap-2">
                            <span class="dataKey">{{ 'content.technology' | translate }}: </span>
                            <img style="width: 20px;" pTooltip="{{ ('content.cancelUserSignatureTech' | translate) + ': ' + ('content.' + cancelSignatureData.tech | translate) }}" [src]="getTechIcon(cancelSignatureData.tech)"/>
                        </div>
                        <div>
                            <span class="dataKey">{{ 'content.cancelReason' | translate }}: </span>
                            <span class="dataValue">{{ cancelSignatureData.reason }}</span>
                        </div>
                        <div *ngIf="cancelSignatureData.observation != null && cancelSignatureData.observation != undefined && cancelSignatureData.observation != ''">
                            <span class="dataKey">{{ 'content.cancelObservation' | translate }}: </span>
                            <span class="dataValue">{{ cancelSignatureData.observation }}</span>
                        </div>
                    </div>
                </div>
                <div style="padding-bottom: 0rem;">
                    <div class="signatureDateLabel">{{ ('content.signedOn' | translate) + ' ' + (cancelSignatureData.date  | date: 'short':'':language) }}</div>
                </div>
            </div>
        </div>
    </div>
</ng-template>

<!-- Widget Verify -->
<app-widget-search
    [numId]="userNumId"
    [widgetUrl]="widgetUrl"
    [managerSettings]="managerSettings"
    [ready]="searchReady"
    [technology]="tech"
    [segmentedSearchAttributes]="segmentedSearchAttributes"
    (result)="onWidgetSearchResult($event)"
></app-widget-search>

<p-dialog [(visible)]="showEnterNumId" [modal]="true" [style]="{ background: '#FFFFFF', 'border-radius': '6px', width: '25rem' }" [draggable]="false" [resizable]="false">
    <ng-template pTemplate="headless">
        <div class="dialogHeader mt-3 ml-3">{{ 'content.verification_1to1_enabled' | translate }}</div>
        <div class="flex flex-column align-items-center justify-content-center gap-3 my-3" [formGroup]="formNumId">
            <p-floatLabel style="width: 90%;" class="mt-6 mb-4">
                <input id="numId" type="text" pInputText formControlName="numId" class="flex-auto" autocomplete="off" style="width: 100%;"/>
                <label for="numId">{{ 'content.id_number' | translate}}</label>
            </p-floatLabel>
            <div class="flex justify-content-center gap-2">
                <button pButton label="{{ 'cancel' | translate }}" class="p-button-text" severity="secondary" (click)="closeNumIdDialog()"></button>
                <p-button label="{{ 'verify' | translate}}" severity="secondary" (onClick)="onNumIdDialogSubmit()" />
            </div>
        </div>
    </ng-template>
</p-dialog>