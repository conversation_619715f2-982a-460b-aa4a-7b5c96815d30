import { Injectable } from "@angular/core";
import * as grpcWeb from 'grpc-web';
import { LocalStorageService } from "../services/local-storage.service";
import { RefreshTokenService } from "../services/refresh-token.service";
import { EncryptionService } from "../services/encryptionService";

@Injectable()
export class GrpcStreamInterceptor implements grpcWeb.StreamInterceptor<any, any> {

    // Static timeout property that can be configured externally
    static TIMEOUT_DURATION = 60000; // Default timeout of 60 seconds, it gets overwritten by manager settings

    // Method to set the timeout externally
    static setTimeoutDuration(duration: number) {
        GrpcStreamInterceptor.TIMEOUT_DURATION = duration;
    }

    intercept(request: grpcWeb.Request<any, any>, invoker: (request: grpcWeb.Request<any, any>) => grpcWeb.ClientReadableStream<any>): grpcWeb.ClientReadableStream<any> {

        class InterceptedStream implements grpcWeb.ClientReadableStream<any> {
            stream: grpcWeb.ClientReadableStream<any>;
            timeoutId: any;
            errorCallback: ((err: any) => void) | null = null;

            constructor(stream: grpcWeb.ClientReadableStream<any>) {
                this.stream = stream;

                if(GrpcStreamInterceptor.TIMEOUT_DURATION >= 1000){
                    this.timeoutId = setTimeout(() => {
                        this.stream.cancel();

                        if (this.errorCallback) {
                            console.error('Stream timed out');
                            this.errorCallback({
                                code: grpcWeb.StatusCode.DEADLINE_EXCEEDED,
                                message: 'Stream timed out',
                            });
                        }
                    }, GrpcStreamInterceptor.TIMEOUT_DURATION);
                }
            };

            on(eventType: string, callback: any) {
                console.log("ON EVENT", eventType)
                console.log("ON CALLBACK", callback)
                this.errorCallback = callback;
                if (eventType === 'data') {
                    const newCallback = (response: any) => {
                        callback(response);
                    };

                    this.stream.on(eventType, newCallback);
                } else if (eventType === 'error') {
                    const newCallback = async (response: any) => {
                        if (response.code === grpcWeb.StatusCode.UNAUTHENTICATED) {
                            console.log("TOKEN EXPIRED")
                            const metadata = request.getMetadata();
                            const encryptionService = new EncryptionService();
                            const localStorage = new LocalStorageService(encryptionService);
                            const refreshToken = new RefreshTokenService();
                            const oldToken = localStorage.getToken();

                            if (oldToken) {
                                try {
                                    console.log("REFRESHING TOKEN")
                                    const newAccessToken: any = await refreshToken.getNewToken(oldToken);
                                    console.log("TOKEN REFRESHED")
                                    metadata['Authorization'] = `Bearer ${newAccessToken.token}`;

                                    localStorage.saveToken(newAccessToken.token);

                                    console.log("INVOKING STREAM")
                                    let newRequest = request;
                                    newRequest.setMetadata(metadata);
                                    const newStream = invoker(request);

                                    newStream.on('data', (data: any) => {
                                        console.log("STREAM INVOKED")
                                        callback(data);
                                    });

                                    newStream.on('error', () => {
                                        console.log("STREAM ERROR")
                                        this.stream = newStream;
                                    });

                                    newStream.on('end', () => {
                                        console.log("STREAM END")
                                        clearTimeout(this.timeoutId);
                                        this.stream.on('end', callback);
                                    });

                                } catch (err) {
                                    console.error('Failed to refresh token', err);
                                }
                            }
                        } else {
                            console.log("STREAM ERROR", response)
                            callback(response);
                        }
                    };

                    this.stream.on('error', newCallback);
                } else if (eventType == 'metadata') {
                    // this.stream.on('metadata', callback);
                } else if (eventType == 'status') {
                    // this.stream.on('status', callback);
                } else if (eventType == 'end') {
                    clearTimeout(this.timeoutId);
                    this.stream.on('end', callback);

                }

                return this;
            };

            removeListener(eventType: string, callback: any) {}

            cancel() {
                clearTimeout(this.timeoutId); // Clear the timeout when the stream is canceled
                this.stream.cancel(); // Cancel the stream
            }
        }

        const metadata = request.getMetadata();
        const encryptionService = new EncryptionService();
        const localStorage = new LocalStorageService(encryptionService);
        metadata['Authorization'] = `Bearer ${localStorage.getToken()}`;

        let response = new InterceptedStream(invoker(request));

        return response;
    };
}