<p-toast></p-toast>
<p-confirmDialog />
<app-inactivity-monitor
    [inactivityTimeoutLimit]="confirmDialogTimeoutLimit"
    [startChecking]="startCheckingInactivity"
    (expired)="closeConfirmationDialog()"
></app-inactivity-monitor>
<app-inactivity-monitor
    [inactivityTimeoutLimit]="auditTrailService.confirmDialogTimeoutLimit"
    [startChecking]="auditTrailService.startCheckingInactivity"
    (expired)="auditTrailService.closeConfirmationDialog()"
></app-inactivity-monitor>
@if(isLoading){
    <app-loading-spinner [isLoading]="isLoading"></app-loading-spinner>
}@else{
    <app-list-tenants
        [listOfTenants]="listOfTenants"
        (tenantDataOutput)="onSaveTenant($event)"
        (addTenantDataOutput)="addTenant($event)"
        (deleteTenant)="confirmDelete($event)"
        [readAndWritePermissions]="canReadAndWrite"
    ></app-list-tenants>
}

<p-dialog [(visible)]="showSavingTenant" [modal]="true" [closable]="false">
    <ng-template pTemplate="content">
        <div class="flex flex-column justify-content-center gap-2 m-5">
            <p-progressBar mode="indeterminate" [style]="{ height: '6px' }" />
            <label [style]="{'color': '#000000'}" for="">{{ 'tenant.wait_tenant_creation' | translate }}</label>
        </div>
    </ng-template>
</p-dialog>