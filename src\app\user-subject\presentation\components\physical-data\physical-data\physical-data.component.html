<p-confirmDialog />
<p-toast/>
<app-inactivity-monitor
    [inactivityTimeoutLimit]="auditTrailService.confirmDialogTimeoutLimit"
    [startChecking]="auditTrailService.startCheckingInactivity"
    (expired)="auditTrailService.closeConfirmationDialog()"
></app-inactivity-monitor>
<!-- Documents -->
<!-- <div class="my-3">
    <p-accordion>
        <p-accordionTab header="{{ 'content.documents' | translate}}">
        </p-accordionTab>
    </p-accordion>
</div> -->
<!-- Profiles -->
<div class="my-3">
    <p-accordion [activeIndex]="0">
        <p-accordionTab header="{{ 'content.profiles' | translate}}">
            <p-carousel
                *ngIf="!loading else loadingSpinner"
                [value]="userSubjectProfiles"
                [numVisible]="4"
                [numScroll]="1"
                [circular]="false"
                [showIndicators]="false"
                [responsiveOptions]="responsiveOptions">
                    <ng-template let-pDataItem pTemplate="item">
                        <div [ngClass]="pDataItem.description === 'uploaded' ? 'note-left' : ''">
                            <div>
                                <img
                                    [ngClass]="pDataItem.description === 'uploaded' ? 'border-3 border-round border-gray-400' : ''"
                                    [src]="pDataItem.content === pDataPlaceholderProfiles ? pDataPlaceholderProfiles : 'data:image/jpeg;base64,' + pDataItem.content"
                                    [alt]="profileName + '-' + pDataItem.number"
                                    class="pDataImage"
                                    (click)="openUserSubjectPhysicalData(pDataItem)">
                            </div>
                        </div>
                    </ng-template>
            </p-carousel>
            <!-- <carousel
                *ngIf="!loading else loadingSpinner"
                [dots]="false"
                [counter]="false"
                [gapBetweenSlides]="5"
                [slideToShow]="4"
                [slideToScroll]="1"
                [slideWidth]="269"
                [maxWidthCarousel]="1200">
                <div *ngFor="let pDataItem of userSubjectProfiles">
                    <img
                        [src]="pDataItem.content === pDataPlaceholder ? pDataPlaceholder : 'data:image/jpeg;base64,' + pDataItem.content"
                        [alt]="profileName + '-' + pDataItem.number"
                        class="carousel-slide carouselImage"
                        (click)="openUserSubjectPhysicalData(pDataItem)">
                </div>
            </carousel> -->
        </p-accordionTab>
    </p-accordion>
</div>
 <!-- Tattoos -->
<div class="my-3">
    <p-accordion>
        <p-accordionTab header="{{ 'content.tattoos' | translate }}">
            <p-carousel
                *ngIf="!loading else loadingSpinner"
                [value]="userSubjectTattoos"
                [numVisible]="4"
                [numScroll]="1"
                [circular]="false"
                [showIndicators]="false"
                [responsiveOptions]="responsiveOptions">
                    <ng-template let-pDataItem pTemplate="item">
                        <div [ngClass]="pDataItem.description === 'uploaded' ? 'note-left' : ''">
                            <div>
                                <img
                                    [ngClass]="pDataItem.description === 'uploaded' ? 'border-3 border-round border-gray-400' : ''"
                                    [src]="pDataItem.content === pDataPlaceholderTattoos ? pDataPlaceholderTattoos : 'data:image/jpeg;base64,' + pDataItem.content"
                                    [alt]="tattooName + '-' + pDataItem.number"
                                    class="pDataImage"
                                    (click)="openUserSubjectPhysicalData(pDataItem)">
                            </div>
                        </div>
                    </ng-template>
            </p-carousel>
            <!-- <carousel
                *ngIf="!loading else loadingSpinner"
                [dots]="false"
                [counter]="false"
                [gapBetweenSlides]="5"
                [slideToShow]="4"
                [slideToScroll]="1"
                [slideWidth]="269"
                [maxWidthCarousel]="1200">
                <div *ngFor="let pDataItem of userSubjectTattoos">
                    <img
                        [src]="pDataItem.content === pDataPlaceholder ? pDataPlaceholder : 'data:image/jpeg;base64,' + pDataItem.content"
                        [alt]="profileName + '-' + pDataItem.number"
                        class="carousel-slide carouselImage"
                        (click)="openUserSubjectPhysicalData(pDataItem)">
                </div>
            </carousel> -->
        </p-accordionTab>
    </p-accordion>
</div>
<!-- Scars -->
<div class="my-3">
    <p-accordion>
        <p-accordionTab header="{{ 'content.scars' | translate }}">
            <p-carousel
                *ngIf="!loading else loadingSpinner"
                [value]="userSubjectScars"
                [numVisible]="4"
                [numScroll]="1"
                [circular]="false"
                [showIndicators]="false"
                [responsiveOptions]="responsiveOptions">
                    <ng-template let-pDataItem pTemplate="item">
                        <div [ngClass]="pDataItem.description === 'uploaded' ? 'note-left' : ''">
                            <div>
                                <img
                                    [ngClass]="pDataItem.description === 'uploaded' ? 'border-3 border-round border-gray-400' : ''"
                                    [src]="pDataItem.content === pDataPlaceholderScars ? pDataPlaceholderScars : 'data:image/jpeg;base64,' + pDataItem.content"
                                    [alt]=" scarName + '-' + pDataItem.number"
                                    class="pDataImage"
                                    (click)="openUserSubjectPhysicalData(pDataItem)">
                            </div>
                        </div>
                    </ng-template>
            </p-carousel>
            <!-- <carousel
                *ngIf="!loading else loadingSpinner"
                [dots]="false"
                [counter]="false"
                [gapBetweenSlides]="5"
                [slideToShow]="4"
                [slideToScroll]="1"
                [slideWidth]="269"
                [maxWidthCarousel]="1200">
                <div *ngFor="let pDataItem of userSubjectScars">
                    <img
                        [src]="pDataItem.content === pDataPlaceholder ? pDataPlaceholder : 'data:image/jpeg;base64,' + pDataItem.content"
                        [alt]="profileName + '-' + pDataItem.number"
                        class="carousel-slide carouselImage"
                        (click)="openUserSubjectPhysicalData(pDataItem)">
                </div>
            </carousel> -->
        </p-accordionTab>
    </p-accordion>
</div>

<ng-template #loadingSpinner>
    <div class="flex justify-content-center">
        <p-progressSpinner styleClass="w-5rem h-5rem" strokeWidth="8" fill="var(--surface-ground)" animationDuration=".5s" ariaLabel="loading" />
    </div>
</ng-template>

<p-dialog header="{{ 'content.selectOption' | translate }}" [modal]="true" [closable]="false" [(visible)]="showSelectOptionDialog">
    <ng-template pTemplate="content">
        <app-new-select-option (onOption1)="openCamera()" (onOption2)="openFileUpload()" (onCancel)="showSelectOptionDialog = false"></app-new-select-option>
    </ng-template>
</p-dialog>

<!-- <app-camera-dialog
    [canReadAndWrite]="canReadAndWrite"
    [loading]="loading"
    [userSubject]="userSubject"
    [selectedCurrentResult]="selectedCurrentResult"
    [showCameraDialog]="showPhysicalDataDialog"
    (result)="onCameraResult($event)"
></app-camera-dialog> -->
<app-camera
    [canReadAndWrite]="canReadAndWrite"
    [selectedCurrentResult]="selectedCurrentResult"
    [showCameraDialog]="showPhysicalDataDialog"
    [aspectRatio]="7/5"
    [showNoDataMessage]="userIsVerified ? (subjectIsVerified ? 'content.noDataAvailable' : 'messages.subject_verification_required_data') : 'messages.verification_required_data'"
    (result)="onCameraResult($event)"
></app-camera>

<!-- Upload New -->
<div class="center-screen">
    <app-upload-files
        [readAndWritePermissions]="canReadAndWrite"
        [showUploadDialog]="showUploadDialog"
        [showForm]="false"
        [acceptedFiles]="acceptedFiles"
        [maxFileSize]="maxFileSize"
        (onCancel)="onCancelUpload()"
        (onUpload)="onUploadResult($event)"
    ></app-upload-files>
</div>