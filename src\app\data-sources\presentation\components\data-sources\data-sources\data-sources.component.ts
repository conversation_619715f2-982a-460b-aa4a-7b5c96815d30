import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Component, EventEmitter, Input, OnChanges, OnDestroy, OnInit, Output, SimpleChanges } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { MessageService, ConfirmationService, Message } from 'primeng/api';
import { ValidatorService } from 'src/verazial-common-frontend/modules/shared/services/validator.service';
import { ExtraData } from 'src/verazial-common-frontend/core/general/actionsV2/domain/entity/extra-data.interface';
import { DataSourceParametersEntity } from 'src/verazial-common-frontend/core/general/data-source/domain/entities/data-source-parameters.entity';
import { DataSourceEntity } from 'src/verazial-common-frontend/core/general/data-source/domain/entities/data-source.entity';
import { AddAppDataSourceParamsUseCase } from 'src/verazial-common-frontend/core/general/data-source/domain/use-cases/add-app-data-source-params.use-case';
import { AddAppDataSourceUseCase } from 'src/verazial-common-frontend/core/general/data-source/domain/use-cases/add-app-data-source.use-case';
import { AttributeData } from 'src/verazial-common-frontend/core/models/attribute-data.model';
import { AuditTrailActions } from 'src/verazial-common-frontend/core/models/audit-trail-actions.enum';
import { AuditTrailFields } from 'src/verazial-common-frontend/core/models/audit-trail-fields.enum';
import { OperationStatus } from 'src/verazial-common-frontend/core/models/operation-status.interface';
import { SourceParamTypes } from 'src/verazial-common-frontend/core/models/source-params-type.enum';
import { SourceType } from 'src/verazial-common-frontend/core/models/source-type.enum';
import { Status } from 'src/verazial-common-frontend/core/models/status.enum';
import { AuditTrailService, ReasonActionTypeEnum, ReasonTypeEnum } from 'src/verazial-common-frontend/core/services/audit-trail.service';
import { ConsoleLoggerService } from 'src/verazial-common-frontend/core/services/console-logger.service';
import { LocalStorageService } from 'src/verazial-common-frontend/core/services/local-storage.service';
import { environment } from 'src/environments/environment';
import {v4 as uuidv4} from 'uuid';


@Component({
  selector: 'app-data-sources',
  templateUrl: './data-sources.component.html',
  styleUrl: './data-sources.component.css',
  providers: [MessageService, ConfirmationService]
})
export class DataSourcesComponent implements OnInit, OnDestroy{

  @Output() cancel = new EventEmitter<boolean>();
  @Output() operationStatus = new EventEmitter<OperationStatus>();

  active: number = 0;

  selectedSourceType: AttributeData | undefined;
  selectedType: AttributeData | undefined;

  showParametersDialog: boolean = false;

  listDataSourceParameters: DataSourceParametersEntity[] = [];

  alertMessage: Message[] | undefined;

  listSourceTypes = [
    // {key: SourceType.WEBSERVICE, value: 'Web Service'},
    {key: SourceType.API_REST, value: 'API Rest'},
    {key: SourceType.API_VERAZIAL, value: 'API Verázial'},
    {key: SourceType.LDAP, value: 'LDAP'},
    {key: SourceType.LDAP_VERAZIAL, value: 'LDAP + Verázial'},
    // {key: SourceType.DB_VIEW, value: 'DB View'},
    // {key: SourceType.DATABASE, value: 'Database'},
    // {key: SourceType.STORE_PROCEDURE, value: 'Store Procedure'}

    ];

  listDataSourceParamTypes = [
    {key: SourceParamTypes.API_TOKEN, value: this.translateService.instant(`content.${SourceParamTypes.API_TOKEN}`)},
    {key: SourceParamTypes.API_ENDPOINT_PARAMETER, value: this.translateService.instant(`content.${SourceParamTypes.API_ENDPOINT_PARAMETER}`)},
    {key: SourceParamTypes.API_USERNAME, value: this.translateService.instant(`content.${SourceParamTypes.API_USERNAME}`)},
    {key: SourceParamTypes.API_PASSWORD, value: this.translateService.instant(`content.${SourceParamTypes.API_PASSWORD}`)},
    {key: SourceParamTypes.API_RESULT_PARAM, value: this.translateService.instant(`content.${SourceParamTypes.API_RESULT_PARAM}`)},
    {key: SourceParamTypes.API_SEARCH_FIELD, value: this.translateService.instant(`content.${SourceParamTypes.API_SEARCH_FIELD}`)},
    {key: SourceParamTypes.LDAP_PASSWORD, value: this.translateService.instant(`content.${SourceParamTypes.LDAP_PASSWORD}`)},
    {key: SourceParamTypes.LDAP_USERNAME, value: this.translateService.instant(`content.${SourceParamTypes.LDAP_USERNAME}`)},
    {key: SourceParamTypes.LDAP_DOMAIN, value: this.translateService.instant(`content.${SourceParamTypes.LDAP_DOMAIN}`)},
    {key: SourceParamTypes.LDAP_BIND_DN, value: this.translateService.instant(`content.${SourceParamTypes.LDAP_BIND_DN}`)},
    {key: SourceParamTypes.LDAP_SEARCH_BASE, value: this.translateService.instant(`content.${SourceParamTypes.LDAP_SEARCH_BASE}`)},
    {key: SourceParamTypes.LDAP_PORT, value: this.translateService.instant(`content.${SourceParamTypes.LDAP_PORT}`)},
    {key: SourceParamTypes.LDAP_SSL, value: this.translateService.instant(`content.${SourceParamTypes.LDAP_SSL}`)},
    {key: SourceParamTypes.LOCAL_METHOD, value: this.translateService.instant(`content.${SourceParamTypes.LOCAL_METHOD}`)},
    {key: SourceParamTypes.LOGIN_USERNAME, value: this.translateService.instant(`content.${SourceParamTypes.LOGIN_USERNAME}`)},
    {key: SourceParamTypes.LOGIN_PASSWORD, value: this.translateService.instant(`content.${SourceParamTypes.LOGIN_PASSWORD}`)},,

  ];

  constructor(
    private fb: FormBuilder,
    private messageService: MessageService,
    private validatorService: ValidatorService,
    private translateService: TranslateService,
    private localStorageService: LocalStorageService,
    public auditTrailService: AuditTrailService,
    private loggerService: ConsoleLoggerService,
    /* Data Sources Use Case */
    private addAppDataSourceUseCase: AddAppDataSourceUseCase,
    /* Parameters Use Cases */
    private addAppDataSourceParamsUseCase: AddAppDataSourceParamsUseCase,
    private http: HttpClient,
  ){}
  ngOnDestroy(): void {
    this.cleanFields();
    this.auditTrailService.resetInactivityMonitor();
  }

  ngOnInit(): void {
    this.loggerService.debug("On init data source")
  }

  public form: FormGroup = this.fb.group({
    dataSourceName: ['', [Validators.required]],
    sourceType: ['', [Validators.required]],
    method: ['', [Validators.required]],
    parameter: ['', [Validators.required]],
    value: ['', [Validators.required]],
    type: ['', [Validators.required]],
  });

  isValid(field: string): boolean {
    return this.validatorService.isValidField(this.form, field);
  }

  cancelEvent(){
    this.cleanFields();
    this.cancel.emit(true);
  }

  showParameters(){
    this.showParametersDialog = true;
  }

  /*loadDataSourceParameters(event: any){

  }*/

  addParameter(parameter: DataSourceParametersEntity){
    // this.listDataSourceParameters.push(parameter);
    this.listDataSourceParameters = [...this.listDataSourceParameters, parameter];
  }

  /*updatedParameters(parameters: DataSourceParametersEntity[]){
    this.listDataSourceParameters = parameters;
  }*/

  deleteParameter(parameter: DataSourceParametersEntity){
    this.listDataSourceParameters = [...this.listDataSourceParameters.filter(param=> param.id != parameter.id)];
  }

  updateParameter(parameter: DataSourceParametersEntity){
    this.listDataSourceParameters = [...this.listDataSourceParameters.filter(param=> param.id != parameter.id)];
    // this.listDataSourceParameters.push(parameter);
    this.listDataSourceParameters = [...this.listDataSourceParameters, parameter];
  }

  async saveDatasource(){
    this.form.get('dataSourceName')?.markAsTouched();
    this.form.get('sourceType')?.markAsTouched();
    this.form.get('method')?.markAsTouched();

    if(!this.isValid('dataSourceName') || !this.isValid('sourceType') || !this.isValid('method')){
      return;
    }

    let datasourceId: string = uuidv4();

    let datasource: DataSourceEntity = {
      id: datasourceId,
      name: this.form.get('dataSourceName')?.value,
      sourceType: this.form.get('sourceType')?.value.key,
      method: this.form.get('method')?.value
    }

    let parameters = this.listDataSourceParameters.map(obj => {
      return { ...obj, dataSourceId: datasourceId };
    });

    this.cleanFields();
    // Check the connection
    if(datasource.sourceType == SourceType.API_VERAZIAL || datasource.sourceType == SourceType.API_REST){
      await this.checkDatasourceConnection(datasource, parameters);
    }else{
      await this.saveDataSourceAndParameters(datasource, parameters);
    }
  }

  async checkDatasourceConnection(datasource: DataSourceEntity, dataSourceParameters: DataSourceParametersEntity[]){

    let apiPassword = "";
    let apiUsername = "";
    let apiToken = "";
    let headers!: HttpHeaders;
    let searchField = "";

    for(const param of dataSourceParameters){
      if(param.type == SourceParamTypes.API_USERNAME){
        apiUsername = param.value!!;
      }
      if(param.type == SourceParamTypes.API_PASSWORD){
        apiPassword = param.value!!;
      }
      if(param.type == SourceParamTypes.API_TOKEN){
        apiToken = param.value!!;
      }
      if(param.type == SourceParamTypes.API_SEARCH_FIELD){
        searchField = param.value!!;
      }
    }

    if (apiToken != ""){
      headers = new HttpHeaders({
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiToken}`
      });
    }

    if (apiUsername != "" && apiPassword != ""){
      headers = new HttpHeaders()
        .set('Authorization', 'Basic ' + btoa(apiUsername + ':' + apiPassword));
    }

    let userData: any;

    let request: string = "";

    if(datasource.sourceType == SourceType.API_VERAZIAL){
      request = `${datasource.method}/abcdf/1234567`
    }else{
      request = datasource.method;
    }

    this.http.head(request, { headers }).subscribe({
      next: (data) => {
        this.saveDataSourceAndParameters(datasource, dataSourceParameters);
      },
      error: (e) => {
        this.loggerService.error(e);
        this.messageService.add({
          severity: 'error',
          summary: this.translateService.instant('content.errorTitle'),
          detail: `${this.translateService.instant('messages.api_connection_error')} ${datasource.method}. ${e.message}`,
          life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
        });
      },
    })
  }

  async saveDataSourceAndParameters(datasource: DataSourceEntity, dataSourceParameters: DataSourceParametersEntity[]){
    let status: OperationStatus;
    const at_attributes: ExtraData[] = [
      { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(datasource) },
    ];
    this.auditTrailService.auditTrailSelectReason(ReasonTypeEnum.CONFIG, AuditTrailActions.ADD_DS, ReasonActionTypeEnum.CREATE, () => {
      this.addAppDataSourceUseCase.execute(datasource).then(
        (data) => {
          this.saveParameters(dataSourceParameters);
          status = {
            status: Status.SUCCESS,
            message: 'content.success'
          }
          this.operationStatus.emit(status);
        },
        (e) => {
          status = {
            status: Status.ERROR,
            message: e.message
          }
          this.loggerService.error(e);
          const at_attributes: ExtraData[] = [
            { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
            { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(datasource) },
          ];
          this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.ADD_DS, 0, 'ERROR', '', at_attributes);
          this.operationStatus.emit(status);
        },
      );
    }, at_attributes);
  }

  saveParameters(parameters: DataSourceParametersEntity[]){
    // Add datasource parameters
    const at_attributes: ExtraData[] = [
      { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(parameters) },
    ];
    this.auditTrailService.auditTrailSelectReason(ReasonTypeEnum.CONFIG, AuditTrailActions.ADD_DS_PARAM, ReasonActionTypeEnum.CREATE, () => {
      this.addAppDataSourceParamsUseCase.execute(parameters).then(
        () => {},
        (e) => {
          this.messageService.add({
            severity: 'error',
            summary: this.translateService.instant('content.errorTitle'),
            detail: `${this.translateService.instant('content.error_save')}: ${e.message}`,
            life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
          });
          this.loggerService.error(e);
          const at_attributes: ExtraData[] = [
            { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
            { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(parameters) },
          ];
          this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.ADD_DS_PARAM, 0, 'ERROR', '', at_attributes);
        }
      );
    }, at_attributes);
  }

  cleanFields(){
    this.form.get('dataSourceName')?.reset();
    this.form.get('sourceType')?.reset();
    this.form.get('method')?.reset();
    this.listDataSourceParameters = [];
  }

}
