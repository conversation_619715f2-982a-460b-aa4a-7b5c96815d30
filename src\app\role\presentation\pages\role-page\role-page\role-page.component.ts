import { Component, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { ConfirmationService, MessageService } from 'primeng/api';
import { AccessEntity } from 'src/verazial-common-frontend/core/general/access/domain/entity/access.entity';
import { GetAllAccessesUseCase } from 'src/verazial-common-frontend/core/general/access/domain/use-case/get-all-accesses.use-case';
import { ExtraData } from 'src/verazial-common-frontend/core/general/actionsV2/domain/entity/extra-data.interface';
import { RoleAccessResponseEntity } from 'src/verazial-common-frontend/core/general/role/domain/entity/role-access-response.entity';
import { RoleAccessEntity } from 'src/verazial-common-frontend/core/general/role/domain/entity/role-access.entity';
import { RoleWithAccessEntity } from 'src/verazial-common-frontend/core/general/role/domain/entity/role-with-access.entity';
import { DeleteRoleAccessByIdUseCase } from 'src/verazial-common-frontend/core/general/role/domain/use-cases/roles-access/delete-role-access-by-id.use-case';
import { AddRoleUseCase } from 'src/verazial-common-frontend/core/general/role/domain/use-cases/roles/add-role.use-case';
import { DeleteRoleByIdUseCase } from 'src/verazial-common-frontend/core/general/role/domain/use-cases/roles/delete-role-by-id.use-case';
import { GetAllRolesUseCase } from 'src/verazial-common-frontend/core/general/role/domain/use-cases/roles/get-all-roles.use-case';
import { GetRoleByIdUseCase } from 'src/verazial-common-frontend/core/general/role/domain/use-cases/roles/get-role-by-id.use-case';
import { UpdateRoleByIdUseCase } from 'src/verazial-common-frontend/core/general/role/domain/use-cases/roles/update-role-by-id.use-case';
import { AccessIdentifier } from 'src/verazial-common-frontend/core/models/access-identifier.enum';
import { AuditTrailActions } from 'src/verazial-common-frontend/core/models/audit-trail-actions.enum';
import { AuditTrailFields } from 'src/verazial-common-frontend/core/models/audit-trail-fields.enum';
import { AuditTrailService, ReasonActionTypeEnum, ReasonTypeEnum } from 'src/verazial-common-frontend/core/services/audit-trail.service';
import { CheckPermissionsService } from 'src/verazial-common-frontend/core/services/check-permissions-service';
import { ConsoleLoggerService } from 'src/verazial-common-frontend/core/services/console-logger.service';
import { LocalStorageService } from 'src/verazial-common-frontend/core/services/local-storage.service';

@Component({
  selector: 'app-role-page',
  templateUrl: './role-page.component.html',
  styleUrl: './role-page.component.css',
  providers: [ConfirmationService, MessageService]
})
export class RolePageComponent implements OnInit, OnDestroy {

  isLoading: boolean = false;
  listRoles: RoleWithAccessEntity[] = [];
  showNewRoleDialog: boolean = false;
  activeIndex: number = 0;
  roleAccess: AccessEntity[] = [];
  listOfAccesses: AccessEntity[] = [];

  // Access code identifier
  access_identifier: string = AccessIdentifier.ROLES;

  canReadAndWrite: boolean = false;

  confirmDialogTimeoutLimit: number = 0;
  startCheckingInactivity: boolean = false;

  constructor(
    private translate: TranslateService,
    private confirmationService: ConfirmationService,
    private messageService: MessageService,
    private checkPermissions: CheckPermissionsService,
    private localStorageService: LocalStorageService,
    private getAllAccessesUseCase: GetAllAccessesUseCase,
    private getAllRolesUseCase: GetAllRolesUseCase,
    private addRoleUseCase: AddRoleUseCase,
    private deleteRoleByIdUseCase: DeleteRoleByIdUseCase,
    private updateRoleByIdUseCase: UpdateRoleByIdUseCase,
    private deleteRoleAccessByIdUseCase: DeleteRoleAccessByIdUseCase,
    private loggerService: ConsoleLoggerService,
    public auditTrailService: AuditTrailService,
    private getRoleByIdUseCase: GetRoleByIdUseCase,
  ) { }

  ngOnInit(): void {
    this.isLoading = true;
    this.setRoleAccesses();
    // this.roleAccess = this.localStorageService.getAccesses() as RoleAccessResponseEntity[];
    this.getAllRoles();
    this.canReadAndWrite = this.checkPermissions.hasReadAndWritePermissions(this.access_identifier);
  }

  setRoleAccesses() {
    this.getAllAccessesUseCase.execute().then(
      (data) => {
        // Filter all access according to the current role level
        this.roleAccess = data;
        this.isLoading = false;
      },
      (e) => {
        this.isLoading = false;
        this.loggerService.error(e);
        const at_attributes: ExtraData[] = [{ name: AuditTrailFields.ERROR, value: JSON.stringify(e) }];
        this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.GET_ALL_ACCESSES, 0, 'ERROR', '', at_attributes);
      }
    )
  }

  ngOnDestroy() {
    // Clean up the timeout if the component is destroyed
    this.closeConfirmationDialog();
    this.auditTrailService.resetInactivityMonitor();
  }

  resetInactivityMonitor() {
    this.startCheckingInactivity = false;
    this.confirmDialogTimeoutLimit = 0;
  }

  getComponentPermisios() {
    let access = this.localStorageService.getAccesses() as RoleAccessResponseEntity[];
  }

  getAllRoles() {
    this.getAllRolesUseCase.execute().then(
      (data) => {
        this.listRoles = data;
        this.isLoading = false;
      },
      (e) => {
        this.isLoading = false;
        this.loggerService.error(e);
        const at_attributes: ExtraData[] = [{ name: AuditTrailFields.ERROR, value: JSON.stringify(e) }];
        this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.GET_ALL_ROLES, 0, 'ERROR', '', at_attributes);
      },
    )
  }

  getAccesses() {
    this.getAllAccessesUseCase.execute().then(
      (data) => {
        // Filter all access according to the current role level
        this.isLoading = false;
      },
      (e) => {
        this.isLoading = false;
        this.loggerService.error(e);
        const at_attributes: ExtraData[] = [{ name: AuditTrailFields.ERROR, value: JSON.stringify(e) }];
        this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.GET_ALL_ACCESSES, 0, 'ERROR', '', at_attributes);
      }
    )
  }

  showRoleDialog(event: any) {
    this.showNewRoleDialog = true;
  }

  onSubmitSaveRole(roleAccess: RoleWithAccessEntity) {
    const at_attributes: ExtraData[] = [
      { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(roleAccess) },
    ];
    this.auditTrailService.auditTrailSelectReason(ReasonTypeEnum.ROLE_MANAGEMENT, AuditTrailActions.ADD_ROL, ReasonActionTypeEnum.CREATE, () => { this.onSaveRole(roleAccess); }, at_attributes, false);
  }

  onSaveRole(roleAccess: RoleWithAccessEntity) {
    console.log(roleAccess);
    this.addRoleUseCase.execute({ role: roleAccess }).then(
      (data) => {
        // this.listRoles.push(data);
        this.listRoles = [...this.listRoles, data];
        this.messageService.add({
          severity: 'success',
          summary: this.translate.instant('content.successTitle'),
          detail: this.translate.instant('messages.success_general'),
          life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
        });
        const at_attributes: ExtraData[] = [
          { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(data) },
          { name: AuditTrailFields.REASON, value: this.auditTrailService.lastReason },
        ];
        this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.ADD_ROL, 0, 'SUCCESS', '', at_attributes);
      },
      (e) => {
        this.messageService.add({
          severity: 'error',
          summary: this.translate.instant('content.errorTitle'),
          detail: `${this.translate.instant('messages.error_creating_role_profile')}: ${e.message}`,
          life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
        });
        this.loggerService.error(e);
        const at_attributes: ExtraData[] = [
          { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
          { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(roleAccess) },
          { name: AuditTrailFields.REASON, value: this.auditTrailService.lastReason },
        ];
        this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.ADD_ROL, 0, 'ERROR', '', at_attributes);
      }
    )
    // this.roleAccess = this.localStorageService.getAccesses() as RoleAccessResponseEntity[];
    this.setRoleAccesses();
  }

  onSubmitUpdateRole(roleAccess: RoleWithAccessEntity) {
    this.getRoleByIdUseCase.execute({ id: roleAccess.id! }).then(
      (data) => {
        const at_attributes: ExtraData[] = [
          { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(data) },
          { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(roleAccess) },
        ];
        this.auditTrailService.auditTrailSelectReason(ReasonTypeEnum.ROLE_MANAGEMENT, AuditTrailActions.MOD_ROL, ReasonActionTypeEnum.UPDATE, () => { this.onUpdateRole(roleAccess, data); }, at_attributes, false);
      },
      (e) => {
        this.messageService.add({
          severity: 'error',
          summary: this.translate.instant('content.errorTitle'),
          detail: `${this.translate.instant('messages.error_retrieving_role_profile')}: ${e.message}`,
          life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
        });
        this.loggerService.error(e);
        const at_attributes: ExtraData[] = [
          { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
          { name: AuditTrailFields.RECORD_ID, value: roleAccess.id!.toString() },
          { name: AuditTrailFields.REASON, value: this.auditTrailService.lastReason },
        ];
        this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.GET_ROLE_BY_ID, 0, 'ERROR', '', at_attributes);
      }
    );
  }

  onUpdateRole(roleAccess: RoleWithAccessEntity, oldValue: RoleWithAccessEntity) {
    let newAccess = roleAccess.roleAccesses ?? [];
    let oldAccess = oldValue.roleAccesses ?? [];
    if ( JSON.stringify(newAccess) != JSON.stringify(oldAccess)) {
      let addedAccesses = newAccess.filter(access => !oldAccess.some(old => old.accessId === access.accessId));
      let removedAccesses = oldAccess.filter(access => !newAccess.some(newAccess => newAccess.accessId === access.accessId));
      if (addedAccesses.length > 0) {
        const at_attributes: ExtraData[] = [
          { name: AuditTrailFields.ROLE_ID, value: roleAccess.id!.toString() },
          { name: AuditTrailFields.ROLE_NAME, value: roleAccess.name!.toString() },
          { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(addedAccesses) },
          { name: AuditTrailFields.REASON, value: this.auditTrailService.lastReason },
        ];
        this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.ADD_ROL_ACC, 0, 'SUCCESS', '', at_attributes);
      }
      if (removedAccesses.length > 0) {
        this.onDeleteAccesses(removedAccesses);
        const at_attributes: ExtraData[] = [
          { name: AuditTrailFields.ROLE_ID, value: roleAccess.id!.toString() },
          { name: AuditTrailFields.ROLE_NAME, value: roleAccess.name!.toString() },
          { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(removedAccesses) },
          { name: AuditTrailFields.REASON, value: this.auditTrailService.lastReason },
        ];
        this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.DEL_ROL_ACC, 0, 'SUCCESS', '', at_attributes);
      }
    }
    this.updateRoleByIdUseCase.execute({ role: roleAccess }).then(
      (data) => {
        this.listRoles = [...this.listRoles.filter(v => v.id != roleAccess.id)];
        this.listRoles = [...this.listRoles, data];
        this.messageService.add({
          severity: 'success',
          summary: this.translate.instant('content.successTitle'),
          detail: this.translate.instant('messages.success_general'),
          life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
        });
      },
      (e) => {
        this.loggerService.error(e);
        const at_attributes: ExtraData[] = [
          { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
          { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(oldValue) },
          { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(roleAccess) },
        ];
        this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.MOD_ROL, 0, 'ERROR', '', at_attributes);
        this.messageService.add({
          severity: 'error',
          summary: this.translate.instant('content.errorTitle'),
          detail: `${this.translate.instant('messages.error_updating_role_profile')}: ${e.message}`,
          life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
        });
      }
    );
  }

  confirmDeleteRole(roleAccess: RoleWithAccessEntity) {
    this.confirmationService.confirm({
      message: `${this.translate.instant('messages.delete_single_record')} <b>${roleAccess.name}</b>?`,
      header: this.translate.instant('messages.delete_confirmation_header'),
      icon: 'pi pi-exclamation-triangle',
      rejectButtonStyleClass:"p-button-text",
      acceptButtonStyleClass:"ng-confirm-button",
      acceptIcon: "none",
      rejectIcon: "none",
      acceptLabel: this.translate.instant("delete"),
      rejectLabel: this.translate.instant("no"),
      accept: () => {
        this.resetInactivityMonitor();
        const at_attributes: ExtraData[] = [
          { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(roleAccess) },
          { name: AuditTrailFields.REASON, value: this.auditTrailService.lastReason },
        ];
        this.auditTrailService.auditTrailSelectReason(ReasonTypeEnum.ROLE_MANAGEMENT, AuditTrailActions.DEL_ROL, ReasonActionTypeEnum.DELETE, () => { this.onDeleteRole(roleAccess); }, at_attributes, false);
      },
      reject: () => {
        this.resetInactivityMonitor();
      }
    });

    this.confirmDialogTimeoutLimit = (this.localStorageService.getSessionSettings()?.timeoutMessages ?? 10) * 1000
    this.startCheckingInactivity = false;
    setTimeout(() => this.startCheckingInactivity = true, 0);
  }

  onDeleteRole(roleAccess: RoleWithAccessEntity) {
    this.deleteRoleByIdUseCase.execute({ id: roleAccess.id! }).then(
      (_) => {
        this.listRoles = [...this.listRoles.filter(v => v.id != roleAccess.id)];
        this.messageService.add({
          severity: 'success',
          summary: this.translate.instant('content.successTitle'),
          detail: this.translate.instant('messages.success_general'),
          life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
        });
        const at_attributes: ExtraData[] = [
          { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(roleAccess) },
          { name: AuditTrailFields.REASON, value: this.auditTrailService.lastReason },
        ];
        this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.DEL_ROL, 0, 'SUCCESS', '', at_attributes);
      },
      (e) => {
        this.messageService.add({
          severity: 'error',
          summary: this.translate.instant('content.errorTitle'),
          detail: `${this.translate.instant('messages.error_deleting_role_profile')}: ${e.message}`,
          life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
        });
        this.loggerService.error(e);
        const at_attributes: ExtraData[] = [
          { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
          { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(roleAccess) },
        ];
        this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.DEL_ROL, 0, 'ERROR', '', at_attributes);
      }
    )
  }

  onSubmitDeleteAccesses(roleAccesses: RoleAccessResponseEntity[]) {
    const at_attributes: ExtraData[] = [
      { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(roleAccesses) },
    ];
    this.auditTrailService.auditTrailSelectReason(ReasonTypeEnum.ROLE_MANAGEMENT, AuditTrailActions.DEL_ROL_ACC, ReasonActionTypeEnum.DELETE, () => { this.onDeleteAccesses(roleAccesses); }, at_attributes, false);
  }

  onDeleteAccesses(roleAccesses: RoleAccessResponseEntity[]) {
    let listOfRoleAccesses: RoleAccessEntity[] = [];
    roleAccesses.forEach(roleAccess => {
      let temp = new RoleAccessEntity();
      temp.id = roleAccess.id;
      temp.roleId = roleAccess.roleId;
      temp.accessId = roleAccess.accessId;
      temp.read = roleAccess.read;
      temp.write = roleAccess.write;

      listOfRoleAccesses.push(temp);
    })

    this.deleteRoleAccessByIdUseCase.execute({ rolesAccesses: listOfRoleAccesses }).then(
      (data) => {
        this.loggerService.debug(data);
        const at_attributes: ExtraData[] = [
          { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(roleAccesses) },
          { name: AuditTrailFields.REASON, value: this.auditTrailService.lastReason },
        ];
        this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.DEL_ROL_ACC, 0, 'SUCCESS', '', at_attributes);
      },
      (e) => {
        this.loggerService.error(e);
        const at_attributes: ExtraData[] = [
          { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
          { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(roleAccesses) },
          { name: AuditTrailFields.REASON, value: this.auditTrailService.lastReason },
        ];
        this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.DEL_ROL_ACC, 0, 'ERROR', '', at_attributes);
      }
    )
  }

  closeConfirmationDialog() {
    this.confirmationService.close();
    this.resetInactivityMonitor();
  }
}