import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DataSourcesComponent } from './data-sources/data-sources.component';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { ButtonModule } from 'primeng/button';
import { DropdownModule } from 'primeng/dropdown';
import { InputTextModule } from 'primeng/inputtext';
import { InputTextareaModule } from 'primeng/inputtextarea';
import { ToastModule } from 'primeng/toast';
import { StepperModule } from 'primeng/stepper';
import { DialogModule } from 'primeng/dialog';
import { TableModule } from 'primeng/table';
import { DataSourceParametersModule } from '../data-source-parameters/data-source-parameters.module';
import { ListDataSourceParametersModule } from '../list-data-source-parameters/list-data-source-parameters.module';
import { StepsModule } from 'primeng/steps';
import { MessagesModule } from 'primeng/messages';
import { InactivityMonitorModule } from 'src/verazial-common-frontend/modules/shared/components/inactivity-monitor/inactivity-monitor.module';


@NgModule({
  declarations: [
    DataSourcesComponent
  ],
  imports: [
    /* Angular */
    CommonModule,
    /* Translate */
    TranslateModule,
    /* Forms */
    FormsModule,
    ReactiveFormsModule,
    /* PrimeNG */
    InputTextModule,
    ButtonModule,
    DropdownModule,
    InputTextareaModule,
    TableModule,
    ToastModule,
    StepperModule,
    StepsModule,
    DialogModule,
    MessagesModule,
    /* Local Apps */
    DataSourceParametersModule,
    ListDataSourceParametersModule,
    InactivityMonitorModule,
  ],
  exports: [
    DataSourcesComponent
  ]
})
export class DataSourcesModule { }
