import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { ConfirmationService, MenuItem, MessageService } from 'primeng/api';
import { TaskFlowEntity } from 'src/verazial-common-frontend/core/general/flow/domain/entity/task-flow.entity';
import { GetAllPublishedTaskFlowsUseCase } from 'src/verazial-common-frontend/core/general/flow/domain/use-cases/task-flow/get-all-published-task-flows.use-case';
import { AssignmentType } from 'src/verazial-common-frontend/core/general/assignment/group/common/enums/assignment-type.enum';
import { AssignmentInfo } from 'src/verazial-common-frontend/core/general/assignment/group/common/interfaces/assignment-info';
import { AssignmentMapper } from 'src/verazial-common-frontend/core/general/assignment/group/data/mapper/assignment.mapper';
import { AssignmentElementEntity } from 'src/verazial-common-frontend/core/general/assignment/group/domain/entity/assignment-elements.entity';
import { AssignmentEntity } from 'src/verazial-common-frontend/core/general/assignment/group/domain/entity/assignment.entity';
import { CreateAssignmentUseCase } from 'src/verazial-common-frontend/core/general/assignment/group/domain/use-cases/create-assignment.use-case';
import { GetAllAssignmentsUseCase } from 'src/verazial-common-frontend/core/general/assignment/group/domain/use-cases/get-all-assignments.use-case';
import { ScheduleTypeEnum } from 'src/verazial-common-frontend/core/general/assignment/categories/common/interfaces/schedule-type.enum';
import { GroupCategoryEntity } from 'src/verazial-common-frontend/core/general/assignment/categories/domain/entity/group-category.entity';
import { GetAllGroupsCategoriesUseCase } from 'src/verazial-common-frontend/core/general/assignment/categories/domain/use-cases/get-all-groups-categories.use-case';
import { ExtraData } from 'src/verazial-common-frontend/core/general/actionsV2/domain/entity/extra-data.interface';
import { AccessIdentifier } from 'src/verazial-common-frontend/core/models/access-identifier.enum';
import { AuditTrailActions } from 'src/verazial-common-frontend/core/models/audit-trail-actions.enum';
import { AuditTrailFields } from 'src/verazial-common-frontend/core/models/audit-trail-fields.enum';
import { AuditTrailService, ReasonActionTypeEnum, ReasonTypeEnum } from 'src/verazial-common-frontend/core/services/audit-trail.service';
import { CheckPermissionsService } from 'src/verazial-common-frontend/core/services/check-permissions-service';
import { ConsoleLoggerService } from 'src/verazial-common-frontend/core/services/console-logger.service';
import { LocalStorageService } from 'src/verazial-common-frontend/core/services/local-storage.service';
import { toEnum } from 'src/verazial-common-frontend/core/util/to-enum';
import { GroupCategoryType } from 'src/verazial-common-frontend/core/general/assignment/categories/common/models/group-category-type.enum';

@Component({
  selector: 'app-admin-group',
  templateUrl: './admin-group.component.html',
  styleUrl: './admin-group.component.css',
  providers: [MessageService, ConfirmationService]
})
export class AdminGroupComponent implements OnInit, OnDestroy {

  listAssignments: AssignmentEntity[] = [];
  listGroupsCategories: GroupCategoryEntity[] = [];
  listTaskFlows: TaskFlowEntity[] = [];

  assignmentMapper = new AssignmentMapper();

  resquestTaskFlowsCompleted: boolean = false;
  resquestCategoriesCompleted: boolean = false;
  resquestGroupsCompleted: boolean = false;

  showNewAdminGroupDialog: boolean = false;
  showFlowCategoryDialog: boolean = false;
  flowCategoryMessage: string = ""
  showCreateFlowButton: boolean = false;
  showCreateCategoryButton: boolean = false;

  isLoading = true;

  items: MenuItem[] = [
    {
      label: "General"
    },
    {
      label: "Flow"
    },
    {
      label: "Category"
    }
  ];

  activeIndex: number = 0;

  disableContinue: boolean = true;

  assignmentInfoData: AssignmentInfo | undefined;
  selectedFlows: AssignmentElementEntity[] = [];
  selectedCategories: AssignmentElementEntity[] = [];

  // Access code identifier
  access_identifier: string = AccessIdentifier.ASSIGNMENTS;
  canReadAndWrite: boolean = false;

  constructor(
    private router: Router,
    private messageService: MessageService,
    private checkPermissions: CheckPermissionsService,
    private translateService: TranslateService,
    private getAllPublishedTaskFlowsUseCase: GetAllPublishedTaskFlowsUseCase,
    private getAllGroupsCategoriesUseCase: GetAllGroupsCategoriesUseCase,
    private getAllAssignmentsUseCase: GetAllAssignmentsUseCase,
    private createAssignmentUseCase: CreateAssignmentUseCase,
    private localStorageService: LocalStorageService,
    private loggerService: ConsoleLoggerService,
    public auditTrailService: AuditTrailService,
  ) {
  }

  ngOnInit(): void {
    this.getTaskFlows();
    this.getGroupCategories();
    this.getAllAssignments();

    this.canReadAndWrite = this.checkPermissions.hasReadAndWritePermissions(this.access_identifier);
  }

  ngOnDestroy() {
    this.auditTrailService.resetInactivityMonitor();
  }

  getTaskFlows() {
    this.getAllPublishedTaskFlowsUseCase.execute().then(
      (data) => {
        this.listTaskFlows = data;
        this.resquestTaskFlowsCompleted = true;
        this.loadingStatus();
      },
      (e) => {
        this.loggerService.error(e);
        const at_attributes: ExtraData[] = [{ name: AuditTrailFields.ERROR, value: JSON.stringify(e) }];
        this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.GET_ALL_PUBLISHED_TASK_FLOWS, 0, 'ERROR', '', at_attributes);
      })
  }

  getGroupCategories() {
    this.getAllGroupsCategoriesUseCase.execute().then(
      (data) => {
        if (data) {
          this.listGroupsCategories = data;
          this.resquestCategoriesCompleted = true;
          this.loadingStatus();
        }
      },
      (e) => {
        this.loggerService.error(e);
        const at_attributes: ExtraData[] = [{ name: AuditTrailFields.ERROR, value: JSON.stringify(e) }];
        this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.GET_ALL_GROUP_CATEGORIES, 0, 'ERROR', '', at_attributes);
      })
  }

  getAllAssignments() {
    this.getAllAssignmentsUseCase.execute().then(
      (data) => {
        this.listAssignments = data;
      },
      (e) => {
        this.loggerService.error(e);
        const at_attributes: ExtraData[] = [{ name: AuditTrailFields.ERROR, value: JSON.stringify(e) }];
        this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.GET_ALL_ASSIGNMENTS, 0, 'ERROR', '', at_attributes);
      }
    )
  }


  loadingStatus() {
    if (this.resquestCategoriesCompleted && this.resquestTaskFlowsCompleted) {
      this.isLoading = false;
    }
  }

  createNewAdminGroup(event: any) {
    if (this.listTaskFlows.length == 0 && this.listGroupsCategories.length == 0) {
      this.flowCategoryMessage = "messages.category_flow_required";
      this.showFlowCategoryDialog = true;
      this.showCreateCategoryButton = true;
      this.showCreateFlowButton = true;
      return
    } else if (this.listTaskFlows.length == 0) {
      this.flowCategoryMessage = "messages.flow_required";
      this.showFlowCategoryDialog = true;
      this.showCreateCategoryButton = false;
      this.showCreateFlowButton = true;
      return
    } else if (this.listGroupsCategories.length == 0) {
      this.flowCategoryMessage = "messages.category_required";
      this.showFlowCategoryDialog = true;
      this.showCreateCategoryButton = true;
      this.showCreateFlowButton = false;
      return
    }
    this.showNewAdminGroupDialog = true;
  }

  onCancel() {
    this.showNewAdminGroupDialog = false;
  }

  onNext() {
    this.activeIndex += 1;
  }

  onBack() {
    this.activeIndex -= 1;
  }

  onSave() {
    let assignment = new AssignmentEntity();

    assignment.name = this.assignmentInfoData?.name!!;
    assignment.description = this.assignmentInfoData?.description;

    assignment.elements = this.selectedFlows.concat(this.selectedCategories);

    const at_attributes: ExtraData[] = [
      { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(assignment) }
    ];
    this.auditTrailService.auditTrailSelectReason(ReasonTypeEnum.CONFIG, AuditTrailActions.ADD_ASG, ReasonActionTypeEnum.CREATE, () => {
      this.createAssignmentUseCase.execute({ assignment: assignment }).then(
        (data) => {
          if (data) {
            this.listAssignments = [...this.listAssignments, data];
            this.messageService.add({
              severity: 'success', summary: this.translateService.instant("titles.success_operation"), detail: this.translateService.instant("messages.success_general"),
              life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
            });
          }
          this.showNewAdminGroupDialog = false;
        },
        (e) => {
          this.loggerService.error(e);
          const at_attributes: ExtraData[] = [
            { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
            { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(assignment) }
          ];
          this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.ADD_ASG, 0, 'ERROR', '', at_attributes);
        }
      );
    }, at_attributes);
  }

  onAddCategory() {
    this.router.navigate(['/groups-categories']);
  }

  onAddFlow() {
    this.router.navigate(['/flows']);
  }

  assignmentInfoDataValid(event: boolean) {
    this.disableContinue = !event;
  }

  getGroupData(event: AssignmentInfo) {
    this.assignmentInfoData = event;
  }

  getSelectedFlows(flows: TaskFlowEntity[]) {
    this.selectedFlows = [];
    flows.forEach(flow => {
      const assigmentElement = new AssignmentElementEntity();
      assigmentElement.type = AssignmentType.FLOW;
      assigmentElement.elementId = flow.id;
      this.selectedFlows.push(assigmentElement);
    })

  }

  getSelectedCategories(categories: GroupCategoryEntity[]) {

    this.selectedCategories = [];
    categories.forEach(category => {
      const assigmentElement = new AssignmentElementEntity();
      assigmentElement.type = AssignmentType.CATEGORY;
      if (category.categorySchedule) {
        assigmentElement.subtype = toEnum(GroupCategoryType, category.attributeType!)
      }
      assigmentElement.elementId = category.id;
      this.selectedCategories.push(assigmentElement);
    })

  }

  resetStates() {
    this.activeIndex = 0;
    this.selectedCategories = [];
    this.selectedFlows = [];
    this.assignmentInfoData = undefined;
  }

}
