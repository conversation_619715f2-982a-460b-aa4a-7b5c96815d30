import { RefreshByUserRequest } from "src/verazial-common-frontend/core/generated/auth/auth_pb";
import { CoreAuthServiceClient } from "src/verazial-common-frontend/core/generated/auth/AuthServiceClientPb";
import { environment } from "src/environments/environment";
import { FailureResponse } from "../classes/failure-response.model";
import { AuthMapper } from "../general/auth/data/mapper/auth.mapper";
import { Injectable } from "@angular/core";
import { LocalStorageService } from "./local-storage.service";
import { EncryptionService } from "./encryptionService";

@Injectable({
    providedIn: 'root',
})
export class RefreshTokenService {

    localStorageService = new LocalStorageService(new EncryptionService());

    constructor() { };

    getNewToken(oldToken: string) {
        const authMapper = new AuthMapper()
        let request = new RefreshByUserRequest();
        request.setOldtoken(oldToken);

        let coreAccessServiceClient = new CoreAuthServiceClient(`${this.localStorageService.getApiGatewayURL() ?? environment.grpcApiGateway}`);

        return new Promise((resolve, reject) => {
            console.log("REFRESHING TOKEN BY USER");
            coreAccessServiceClient.refreshByUser(request, {}, (err, response) => {
                if (err) {
                    let failure: FailureResponse = {
                        code: err.code,
                        message: err.message,
                    };
                    console.log("REFRESHING TOKEN BY USER FAILED");
                    reject(failure);
                } else {
                    console.log("REFRESHING TOKEN BY USER SUCCESS");
                    resolve(authMapper.mapFrom(response));
                }
            });
        });
    }
}