<ng-template #loadingSpinner>
    <div class="flex justify-content-center align-content-center w-full h-full mt-4">
        <p-progressSpinner styleClass="w-5rem h-5rem" strokeWidth="8" fill="var(--surface-ground)" animationDuration=".5s" ariaLabel="loading" [ngClass]="{'spinner': isLoading}"></p-progressSpinner>
    </div>
</ng-template>
<app-inactivity-monitor
    [inactivityTimeoutLimit]="confirmDialogTimeoutLimit"
    [startChecking]="startCheckingInactivity"
    (expired)="closeConfirmationDialog()"
></app-inactivity-monitor>
<app-inactivity-monitor
    [inactivityTimeoutLimit]="auditTrailService.confirmDialogTimeoutLimit"
    [startChecking]="auditTrailService.startCheckingInactivity"
    (expired)="auditTrailService.closeConfirmationDialog()"
></app-inactivity-monitor>
<div *ngIf="!isLoading else loadingSpinner" class="subcontainer">
    <div *ngIf="listOfAuthSchedules.length != 0 else empty" class="subcontainer-list gap-3">
        <div class="flex flex-row justify-content-between flex-wrap gap-2">
            <div class="flex flex-row align-items-center">
                <div class="pr-3">
                    <label class="subcontainer-title">{{ 'titles.authorization_schedules_to_enter_centers' | translate}}</label>
                </div>
                <div class="tableNumSelectedRowsText flex flex-row align-items-center px-3 border-x-1 border-300">
                    @if(selectedAuthSchedules.length > 0){
                        <div>
                            {{ selectedAuthSchedules.length + ' ' + ('content.selected' | translate) }}
                        </div>
                        <button pButton [disabled]="!canReadAndWrite && !readOnly || selectedAuthSchedules.length > 1" icon="pi pi-{{ readOnly || !userIsVerified ? 'eye' : 'pencil' }}" [text]="true" class="ml-3" style="padding: 0; width: 1.5rem;" (click)="onEditAuthSchedule()"></button>
                        <button pButton [disabled]="!canReadAndWrite || !userIsVerified" icon="pi pi-trash" [text]="true" class="ml-2" style="padding: 0; width: 1.5rem;" (click)="deleteMultipleAuthSchedules()"></button>
                    }
                </div>
            </div>
            <div class="flex flex-row flex-wrap justify-content-center gap-4 align-items-center">
                <p-iconField iconPosition="right">
                    <input pInputText type="text"
                        (input)="dt.filterGlobal($event.target.value, 'contains')"
                        placeholder="{{ 'content.search' | translate }}"
                    />
                    <p-inputIcon styleClass="pi pi-search"></p-inputIcon>
                </p-iconField>
                <div class="add-action-main-full">
                    <p-button
                        ngClass="add-action-main-full"
                        [disabled]="!(canReadAndWrite && userIsVerified)"
                        [style]="{'color': '#FFFFFF', 'border': 'none', 'background': '#0AB4BA', 'font-family': 'Open Sans', 'font-size': '14px'}"
                        label="{{ 'content.new_schedule' | translate }}"
                        icon="pi pi-plus" iconPos="right"
                        [rounded]="true"
                        (onClick)="onNewAuthSchedule()"
                    ></p-button>
                </div>
                <div class="add-action-main-small">
                    <p-button
                        [disabled]="!(canReadAndWrite && userIsVerified)"
                        [style]="{'color': '#FFFFFF', 'border': 'none', 'background': '#0AB4BA', 'font-family': 'Open Sans', 'font-size': '14px'}"
                        icon="pi pi-plus"
                        [rounded]="true"
                        (onClick)="onNewAuthSchedule()"
                    ></p-button>
                </div>
            </div>
        </div>
        <div></div>
        <p-table
            #dt
            [value]="listOfAuthSchedules"
            (onFilter)="onFilter($event, dt)"
            [(selection)]="selectedAuthSchedules"
            dataKey="id"
            [rowHover]="true"
            [paginator]="true"
            [rows]="10"
            [rowsPerPageOptions]="[5, 10, 20]"
            [scrollable]="true"
            scrollDirection="horizontal"
            [tableStyle]="{ 'min-width': '75rem' }"
            styleClass="fixed-table"
            [showCurrentPageReport]="true"
            currentPageReportTemplate="{{ 'content.showing' | translate }} {first} {{ 'content.to' | translate }} {last} {{ 'content.of' | translate}} {totalRecords} {{ 'content.records' | translate }}"
            [globalFilterFields]="[
                'id',
                'name',
                'description',
                'type',
                'roleId',
                'locationId',
            ]"
            [sortField]="'id'" [sortOrder]="1">
            <ng-template pTemplate="header">
                <tr>
                    <th style="width: 4rem"></th>
                    <th class="fixed-column-sm" pSortableColumn="id"> {{ 'content.id' | translate }} <p-sortIcon field="id"></p-sortIcon></th>
                    <th class="fixed-column-sm" pSortableColumn="name"> {{ 'content.name' | translate }} <p-sortIcon field="name"></p-sortIcon></th>
                    <!-- <th class="fixed-column-sm" pSortableColumn="description"> {{ 'content.description' | translate }} <p-sortIcon field="description"></p-sortIcon></th> -->
                    <th class="fixed-column-sm" pSortableColumn="type"> {{ 'content.type' | translate }} <p-sortIcon field="type"></p-sortIcon></th>
                    <th class="fixed-column-sm" pSortableColumn="roleId"> {{ 'content.profile' | translate }} <p-sortIcon field="roleId"></p-sortIcon></th>
                    <th class="fixed-column-sm" pSortableColumn="locationId"> {{ 'content.location' | translate }} <p-sortIcon field="locationId"></p-sortIcon></th>
                    <!-- <th class="fixed-column-sm" pSortableColumn="updatedAt"> {{ 'content.updated_at' | translate }} <p-sortIcon field="updatedAt"></p-sortIcon></th> -->
                    <th alignFrozen="right" pFrozenColumn [frozen]="true"></th>
                </tr>
                <tr>
                    <th style="width: 4rem">
                        <p-tableHeaderCheckbox/>
                    </th>
                    <th>
                        <p-columnFilter type="text" field="id" [showMenu]="false" matchMode="contains">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <input pInputText type="text" (input)="filter($event.target.value)" [value]="value">
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th>
                        <p-columnFilter type="text" field="name" [showMenu]="false" matchMode="contains">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <input pInputText type="text" (input)="filter($event.target.value)" [value]="value">
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <!-- <th>
                        <p-columnFilter type="text" field="description" [showMenu]="false" matchMode="contains">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <input pInputText type="text" (input)="filter($event.target.value)" [value]="value">
                            </ng-template>
                        </p-columnFilter>
                    </th> -->
                    <th>
                        <p-columnFilter type="text" field="type" [showMenu]="false" matchMode="contains">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <input pInputText type="text" (input)="filter($event.target.value)" [value]="value">
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th>
                        <!-- <p-columnFilter type="text" field="roleId" [showMenu]="false" matchMode="contains">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <input pInputText type="text" (input)="filter($event.target.value)" [value]="value">
                            </ng-template>
                        </p-columnFilter> -->
                        <p-columnFilter type="text" field="roleId" [showMenu]="false" matchMode="equals">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <p-dropdown appendTo="body"
                                    [ngModel]="value"
                                    [options]="listRoles"
                                    (onChange)="filter($event.value)"
                                    placeholder="{{ 'content.select' | translate }}"
                                    [showClear]="true"
                                    optionLabel="name"
                                    optionValue="id"
                                >
                                    <ng-template pTemplate="selectedItem">
                                        {{ getRoleName(value) }}
                                    </ng-template>
                                    <ng-template let-option pTemplate="item">
                                        {{ option.name }}
                                    </ng-template>
                                </p-dropdown>
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th>
                        <p-columnFilter type="text" field="locationId" [showMenu]="false" matchMode="equals">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <p-treeSelect appendTo="body"
                                    class="md:w-20rem w-full"
                                    containerStyleClass="w-full"
                                    [options]="listLocations"
                                    [ngModel]="value"
                                    placeholder="{{ 'content.select' | translate }}"
                                    [showClear]="true"
                                    [filter]="true"
                                    (onNodeSelect)="filter($event.node.key)"
                                    (onClear)="filter(null)"
                                ></p-treeSelect>
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th>
                        <!-- <p-columnFilter type="date" field="updatedAt" [showMenu]="false" matchMode="customDateRange">
                            <ng-template pTemplate="filter" let-filter="filterCallback" [formGroup]="formDateFilter">
                                <p-calendar
                                    formControlName="date"
                                    selectionMode="range"
                                    (onSelect)="applyDateRangeFilter(dt, 'updatedAt')"
                                    (onInput)="applyDateRangeFilter(dt, 'updatedAt')"
                                    (onClickOutside)="applyDateRangeFilter(dt, 'updatedAt')"
                                    placeholder="{{ 'content.select' | translate }}"
                                    dateFormat="{{ 'dateFormat' | translate }}"
                                    appendTo="body"
                                ></p-calendar>
                            </ng-template>
                        </p-columnFilter> -->
                    </th>
                    <th alignFrozen="right" pFrozenColumn [frozen]="true"></th>
                </tr>
            </ng-template>
            <ng-template pTemplate="body" let-schedule let-rowIndex="rowIndex">
                <tr *ngIf="getCanReadAndWriteSchedule(schedule)" class="p-selectable-row" [pSelectableRow]="schedule" [pSelectableRowIndex]="rowIndex">
                    <td>
                        <p-tableCheckbox [value]="schedule"></p-tableCheckbox>
                    </td>
                    <td (click)="onEditAuthSchedule(schedule)" showDelay="1000" pTooltip="{{schedule.id}}" tooltipPosition="top" class="ellipsis-cell">{{ schedule.id }}</td>
                    <td (click)="onEditAuthSchedule(schedule)" showDelay="1000" pTooltip="{{schedule.name}}" tooltipPosition="top" class="ellipsis-cell">{{ schedule.name }}</td>
                    <!-- <td (click)="onEditAuthSchedule(schedule)" showDelay="1000" pTooltip="{{schedule.description}}" tooltipPosition="top" class="ellipsis-cell">{{ schedule.description }}</td> -->
                    <td (click)="onEditAuthSchedule(schedule)" showDelay="1000" pTooltip="{{getScheduleType(schedule.type) | translate }}" tooltipPosition="top" class="ellipsis-cell">{{ getScheduleType(schedule.type) | translate }}</td>
                    <td (click)="onEditAuthSchedule(schedule)" showDelay="1000" pTooltip="{{getRoleNames(schedule.roleId)}}" tooltipPosition="top" class="ellipsis-cell">{{ getRoleNames(schedule.roleId) }}</td>
                    <td (click)="onEditAuthSchedule(schedule)" showDelay="1000" pTooltip="{{getLocationPathReduce(schedule.locationId)}}" tooltipPosition="top" class="ellipsis-cell">{{ getLocationPathReduce(schedule.locationId) }}</td>
                    <!-- @if(isValidDate(schedule.updatedAt)){
                        <td (click)="onEditAuthSchedule(schedule)" showDelay="1000" pTooltip="{{schedule.updatedAt | date:('dateFormatLong' | translate)}}" tooltipPosition="top" class="ellipsis-cell">{{ schedule.updatedAt | date:('dateFormatLong' | translate) }}</td>
                    }@else{
                        <td (click)="onEditAuthSchedule(schedule)" showDelay="1000" pTooltip="" tooltipPosition="top" class="ellipsis-cell"></td>
                    } -->
                    <!-- <td (click)="onEditAuthSchedule(schedule)" showDelay="1000" pTooltip="{{schedule.updatedAt}}" tooltipPosition="top" class="ellipsis-cell">{{ schedule.updatedAt }}</td> -->
                    <td alignFrozen="right" pFrozenColumn [frozen]="true" class="custom-border">
                        <div class="flex flex-row">
                            <button pButton pRipple [disabled]="!canReadAndWrite && !readOnly" icon="pi pi-{{ readOnly || !userIsVerified ? 'eye' : 'pencil' }}" [text]="true" class="mr-2" style="padding: 0; width: 1.5rem;" (click)="onEditAuthSchedule(schedule)"></button>
                            <button pButton pRipple [disabled]="!canReadAndWrite || !userIsVerified" icon="pi pi-trash" [text]="true" class="mr-2" style="padding: 0; width: 1.5rem;" (click)="deleteAuthSchedule(schedule)"></button>
                        </div>
                    </td>
                </tr>
            </ng-template>
            <ng-template pTemplate="emptymessage">
                <tr>
                    <td colspan="5" class="py-4">{{ 'content.noDataAvailable' | translate }}</td>
                </tr>
            </ng-template>
        </p-table>
    </div>
</div>

<p-dialog [(visible)]="showAuthScheduleDialog" styleClass="p-fluid" [closable]="true" [modal]="true">
    <ng-template pTemplate="header">
        <div></div>
        <div class="dialog-title">
            {{ (operationType == opType.INSERT ? 'content.new_schedule' : 'content.edit_schedule') | translate }}
        </div>
    </ng-template>
    <ng-template pTemplate="content">
        <app-auth-schedule-edit
            [canReadAndWrite]="canReadAndWriteSchedule"
            [userIsVerified]="userIsVerified"
            [operationType]="operationType"
            [authSchedule]="authSchedule"
            [listRoles]="listRoles"
            [listLocations]="listLocations"
            [createUpdateButtonTitle]="createUpdateButtonTitle"
            (operationStatus)="operationStatus($event)"
        ></app-auth-schedule-edit>
    </ng-template>
</p-dialog>

<ng-template #empty>
    <div class="subcontainer-list gap-3">
        <div class="flex flex-row justify-content-between flex-wrap gap-2">
            <div class="flex flex-row align-items-center">
                <div class="pr-3">
                    <label class="subcontainer-title">{{ 'titles.authorization_schedules_to_enter_centers' | translate}}</label>
                </div>
            </div>
        </div>
    </div>
    <div style="height: 65vh;" class="flex justify-content-center align-items-center">
        <app-empty
            [readAndWritePermissions]="canReadAndWrite && userIsVerified"
            buttonLabel="content.new_schedule"
            titleLabel="titles.no_auth_schedules_available"
            contentHeight="300px"
            (clicked)="onNewAuthSchedule()">
        </app-empty>
    </div>
</ng-template>