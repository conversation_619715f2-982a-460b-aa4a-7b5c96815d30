import { DOCUMENT } from '@angular/common';
import { Component, EventEmitter, Inject, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { ValidatorService } from 'src/verazial-common-frontend/modules/shared/services/validator.service';
import { UserEntity } from 'src/verazial-common-frontend/core/general/user/domain/entity/user.entity';
import { UpdateUserUseCase } from 'src/verazial-common-frontend/core/general/user/domain/use-cases/update-user.use-case';
import { AuditTrailActions } from 'src/verazial-common-frontend/core/models/audit-trail-actions.enum';
import { AuditTrailFields } from 'src/verazial-common-frontend/core/models/audit-trail-fields.enum';
import { AuditTrailService, ReasonActionTypeEnum, ReasonTypeEnum } from 'src/verazial-common-frontend/core/services/audit-trail.service';
import { ConsoleLoggerService } from 'src/verazial-common-frontend/core/services/console-logger.service';
import { LanguageRecordModel, TranslationGroup } from 'src/verazial-common-frontend/core/general/manager/common/models/translation.model';
import { GeneralSettings } from 'src/verazial-common-frontend/core/general/manager/common/models/general-settings.model';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'app-update-password',
  templateUrl: './update-password.component.html',
  styleUrl: './update-password.component.css'
})
export class UpdatePasswordComponent implements OnInit, OnDestroy {
  @Input() userData: UserEntity | undefined;
  @Input() validatorPattern: string | RegExp = /^(?=.*?[A-Z])(?=.*?[a-z])(?=.*?[0-9])(?=.*?[#?!@$%^&*-]).{8,}$/;
  @Input() generalSettings: GeneralSettings | undefined;
  @Output() successUpdate = new EventEmitter<boolean>();

  isLoading: boolean = false;
  passwordNotMatch: boolean = false;

  errorMessage: string = "messages.invalidPasswordComplexity";

  public dataForm: FormGroup = this.fb.group({
    password: ['', [Validators.required, Validators.pattern(this.validatorPattern)]],
    repeatPassword: ['', [Validators.required, Validators.pattern(this.validatorPattern)]],
  });

  constructor(
    @Inject(DOCUMENT) private _document: Document,
    private fb: FormBuilder,
    private validatorService: ValidatorService,
    private translate: TranslateService,
    private loggerService: ConsoleLoggerService,
    private updateUserUseCase: UpdateUserUseCase,
    public auditTrailService: AuditTrailService,
  ) { }

  ngOnInit(): void {
    this.loggerService.debug(this.userData);
    this.translate.onLangChange.subscribe(event => {
      const currentLang = event.lang;
      console.log('Current language:', currentLang);
    });
    var lang = this.translate.currentLang;
    if (!lang) {
      lang = this.translate.getDefaultLang();
    }
    var translation = this.generalSettings?.continued1?.passwordComplexity?.errorMessage?.translations?.find((t: LanguageRecordModel) => t.languageCode == lang);
    if (translation)
      this.errorMessage = translation.value!;
    else
      this.errorMessage = this.translate.instant(this.errorMessage);

  }

  ngOnDestroy() {
    // Clean up the timeout if the component is destroyed
    this.auditTrailService.resetInactivityMonitor();
  }

  isValid(field: string): boolean {
    return this.validatorService.isValidField(this.dataForm, field);
  }

  checkSpecificError(field: string, error: string): boolean {
    if (error == 'minLength') {
      let result = true;
      let control = this.dataForm.controls[field]
      if (control.errors) {
        const object = control.errors['minlength'];
        if (object) {
          result = object.requiredLength > object.actualLength;
        }
        else {
          result = false;
        }
      }
      return result;
    }
    return this.validatorService.checkSpecificError(this.dataForm, field, error);
  }

  updatePassword() {
    this.dataForm.markAllAsTouched();
    if (!this.passwordNotMatch && this.dataForm.valid) {
      let userWithNewPassword = {
        ...this.userData,
        password: this.dataForm.get('password')?.value,
        mustUpdatePassword: false
      } as UserEntity
      const reasons = this.generalSettings?.continued1?.passwordManagementReasons;
      const translations = this.generalSettings?.continued1?.translations?.find((t: TranslationGroup) => t.id === 'passwordManagementReasons')?.translations;
      const at_attributes = [
        { name: AuditTrailFields.USER_NUM_ID, value: this.userData?.numId },
        { name: AuditTrailFields.USER_ID, value: this.userData?.id },
        { name: AuditTrailFields.USER_ROLES, value: JSON.stringify(this.userData?.roles) },
        { name: AuditTrailFields.APPLICATION_ID, value: environment.application},
      ];
      this.auditTrailService.auditTrailSelectReason(ReasonTypeEnum.OTHER, AuditTrailActions.MOD_PASS, ReasonActionTypeEnum.UPDATE, () => {
        this.updateUserUseCase.execute({ user: userWithNewPassword }).then(
          (data) => {
            // this.auditTrailService.registerAuditTrailAction(this.userData?.numId!, AuditTrailActions.MOD_PASS, 0, 'SUCCESS', '', at_attributes);
            this.successUpdate.emit(true);
          },
          (e) => {
            this.loggerService.error("Users MS")
            this.loggerService.error(e);
            const at_attributes = [
              { name: AuditTrailFields.USER_NUM_ID, value: this.userData?.numId },
              { name: AuditTrailFields.USER_ID, value: this.userData?.id },
              { name: AuditTrailFields.USER_ROLES, value: JSON.stringify(this.userData?.roles) },
              { name: AuditTrailFields.ERROR, value: JSON.stringify(e) }
            ];
            this.auditTrailService.registerAuditTrailAction(this.userData?.numId!, AuditTrailActions.MOD_PASS, 0, 'ERROR', '', at_attributes);
            this.successUpdate.emit(false)
          }
        )
      }, at_attributes, true, reasons, translations, userWithNewPassword.numId!);
    }
  }

  trackDataChange() {
    if (this.dataForm.get('password')?.value != null && this.dataForm.get('repeatPassword')?.value != null &&
      this.dataForm.get('password')?.value != this.dataForm.get('repeatPassword')?.value) {
      this.passwordNotMatch = true;
    } else {
      this.passwordNotMatch = false;
    }
  }

  disableCopyPaste(event: ClipboardEvent): void {
    if (!this.generalSettings?.continued1?.passwordComplexity?.isEnabled) {
      event.preventDefault();
    }
  }
}
