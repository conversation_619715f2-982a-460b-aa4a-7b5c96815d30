
<app-inactivity-monitor
    [inactivityTimeoutLimit]="confirmDialogTimeoutLimit"
    [startChecking]="startCheckingInactivity"
    (expired)="closeConfirmationDialog()"
></app-inactivity-monitor>
<app-inactivity-monitor
    [inactivityTimeoutLimit]="auditTrailService.confirmDialogTimeoutLimit"
    [startChecking]="auditTrailService.startCheckingInactivity"
    (expired)="auditTrailService.closeConfirmationDialog()"
></app-inactivity-monitor>
<div [formGroup]="form">
    <div class="main">
        <div class="edit-header">
            <div class="content">
                <p-button icon="pi pi-angle-left" [text]="true" (onClick)="onReturn()"></p-button>
                <input id="disabled-input" type="text" pInputText [disabled]="true" formControlName="applicationTitle" class="p-inputtext-sm"/>
                <div class="last-update-div" *ngIf="showLastUpdate">
                    <div class="vertical-line"></div>
                    <label class="text">{{ "flow.last_update" | translate}}: {{ lastUpdate }}</label>
                </div>
            </div>
            <div class="content">
                <div class="vertical-line"></div>
                <p-button label="{{ 'save' | translate }}" icon="pi pi-save"
                (onClick)="saveApplication()"
                [style]="{'width':'110px','height':'36px', 'color': '#FFFFFF' , 'border': 'none', 'background': '#0AB4BA', 'font-family': 'Open Sans', 'font-size': '14px'}" ></p-button>
                <p-button label="{{ publishText | translate}}" icon="pi pi-file-{{ publishIcon }}"
                [disabled]="isPublishDisabled"
                (onClick)="changeAppStatus()"
                [style]="isFlowPublished? {'height':'36px', 'color': '#FFFFFF' , 'border': 'none', 'background': '#64748B', 'font-family': 'Open Sans', 'font-size': '14px'}:
                    {'height':'36px', 'color': '#FFFFFF' , 'border': 'none', 'background': '#F59E0B', 'font-family': 'Open Sans', 'font-size': '14px'}"></p-button>
            </div>
        </div>
        <div class="flex flex-row" [style]="{'border': 'solid 1px #DADEE3', 'height': '-webkit-fill-available',}">
            <div class="menu-actions">
                <div class="actions p-3">

                    <label [style]="{'color': '#556376', 'font-size':'14px', 'font-weight': 600}">
                        {{ "pass_application.add_window" | translate}}
                    </label>
                    <div class="dashed-horizontal-line"></div>
                    @if (uniqueWindows.length==0) {
                        <label [style]="{'color': '#6C757D', 'font-size':'12px', 'font-weight': 400}">{{ "pass_application.no_windows" | translate }}</label>
                    }@else {
                        @for (window of uniqueWindows; track window.id) {
                            <p-splitButton
                                #button
                                label="{{ window.windowName }}"
                                draggable="true"
                                (dragstart)="onDragStart($event, window)"
                                [model]="options"
                                (onDropdownClick)="onRightClick(window)"
                                styleClass="p-splitButton-custom">
                            </p-splitButton>
                        }
                    }

                </div>
                <div class="flex justify-content-center bg-white pt-3 pb-3">
                    <p-button label="{{ 'pass_application.new_window' | translate }}" icon="pi pi-plus" iconPos="right" (onClick)="addWindow()"
                    [style]="{'width':'188px','height':'36px', 'color': '#FFFFFF' , 'border': 'none', 'background': '#15294A', 'font-family': 'Open Sans', 'font-size': '14px'}"></p-button>
                </div>
            </div>
            <div id="drawflow"
                class="drawflow-class flex-grow-1 flex m-2 px-5 py-3 border-round "
                (drop)="onDrop($event)"
                (dragover)="onDragOver($event)">

                <div class="zoom">
                    <div class="pi pi-search-minus" (click)="editor.zoom_out()"></div>
                    <div class="pi pi-search-plus" (click)="editor.zoom_in()"></div>
                </div>
                <div *ngIf="listNodesDrawFlow.length == 0" class="empty-flow">
                    <div class="content">
                        <div class="pi pi-plus py-3" [style]="{'color': '#009BA9', 'font-size': '1.7rem'}"></div>
                        <label> {{ "flow.drag_drop" | translate}} <b>{{ "pass_application.the_window" | translate}}</b>. </label>
                    </div>
                </div>
            </div>
            <div class="components" [style]="hideStyle">
                <div class="field">
                    <label class="label-text" for="windowNamePanel">{{ 'pass_application.window' | translate }}</label>
                    <input id="windowNamePanel" formControlName="windowNamePanel" type="text" pInputText/>
                </div>
                <div class="field">
                    <label class="label-text" for="windowTargetPanel">{{ 'pass_application.target' | translate }}</label>
                    <input id="windowTargetPanel" formControlName="windowTargetPanel" type="text" pInputText/>
                </div>

                <!--h5><span>{{ 'headers.window_components' | translate }}</span></h5-->

                <div class="divider">
                    <p><label class="label-div">{{ 'pass_application.window_components' | translate }}</label></p>
                </div>

                <div class="button-new-component">
                    <p-button label="{{ 'pass_application.new_component' | translate }}"
                    icon="pi pi-plus-circle" iconPos="right" (onClick)="addNewComponent()"
                    [style]="{'width':'100%', 'height':'36px', 'color': '#FFFFFF' , 'border': 'none', 'background': '#15294A', 'font-family': 'Open Sans', 'font-size': '14px'}">
                </p-button>
                </div>

                <div *ngIf="dataComponentLoading">
                    <div *ngFor="let item of listComponents">
                        <p-skeleton height="2rem" styleClass="mb-2"></p-skeleton>
                    </div>
                </div>

                <div [style]="attributeStyle">
                    <p-accordion [multiple]="true">
                        <div class="flex flex-column gap-1 mt-2">
                            @for (item of listComponents; track item.id) {
                            <p-accordionTab class="custom-accordion-style" header="{{item.displayName}}">
                                <div class="field">
                                    <label class="label-text" for="inputText">{{ 'content.display_name' | translate }}</label>
                                    <input id="{{item.id}}-{{item.windowOrder}}-window-display-name"
                                        [formControlName]="item.id + '-' + item.windowOrder + '-window-display-name'"
                                        type="text"
                                        pInputText
                                        placeholder="{{ 'content.display_name' | translate }}"
                                        (input)="onChangeName(item)"/>
                                </div>

                                <div class="field">
                                    <label class="label-text" for="inputText">{{ 'content.datasource_link_field' | translate }}</label>
                                    <input id="{{item.id}}-{{item.windowOrder}}-window-name"
                                        [formControlName]="item.id + '-' + item.windowOrder + '-window-name'"
                                        type="text"
                                        pInputText
                                        placeholder="{{ 'content.datasource_link_field' | translate }}"
                                    />
                                </div>

                                <div class="field">
                                    <label class="label-text" for="inputText">{{ 'pass_application.attribute_name' | translate }}</label>
                                    <input
                                        id="{{item.id}}-{{item.windowOrder}}-attribute-name"
                                        [formControlName]="item.id + '-' + item.windowOrder + '-attribute-name'"
                                        type="text"
                                        pInputText placeholder="{{ 'pass_application.attribute_name' | translate }}"/>
                                </div>

                                <div class="field">
                                    <label class="label-text" for="{{item.id}}-{{item.windowOrder}}-component-type">{{ 'pass_application.component_type' | translate }}</label>
                                    <p-dropdown
                                        appendTo="body"
                                        [options]="listComponentTypes"
                                        placeholder="{{ 'content.select' | translate}}"
                                        optionLabel="value"
                                        [formControlName]="item.id!!+ '-' + item.windowOrder + '-component-type'"
                                        (onChange)="onComboboxChange($event)"
                                        id="{{item.id}}-{{item.windowOrder}}-component-type"
                                        ></p-dropdown>
                                    <div *ngIf="optionNotValid">
                                        <small style="color: red;">{{ 'messages.error_isRequiredField' | translate }}</small>
                                    </div>
                                </div>

                                <div class="field">
                                    <label class="label-text" for="{{item.id}}-{{item.windowOrder}}-trigger-order">{{ 'pass_application.trigger_order' | translate }}</label>
                                    <input
                                        id="{{item.id}}-{{item.windowOrder}}-trigger-order"
                                        [formControlName]="item.id+ '-' + item.windowOrder + '-trigger-order'"
                                        type="number" pInputText/>
                                </div>

                                <div class="field">
                                    <label class="label-text" for="{{item.id}}-{{item.windowOrder}}-position">{{ 'pass_application.position' | translate }}</label>
                                    <input
                                        id="{{item.id}}-{{item.windowOrder}}-position"
                                        [formControlName]="item.id + '-' + item.windowOrder + '-position'"
                                        type="number" pInputText/>
                                </div>

                                <div class="field">
                                    <label class="label-text" for="{{item.id}}-{{item.windowOrder}}-event">{{ 'pass_application.event' | translate }}</label>
                                    <p-dropdown
                                        appendTo="body"
                                        [options]="listEvents"
                                        placeholder="{{ 'content.select' | translate}}"
                                        optionLabel="value"
                                        [formControlName]="item.id!! + '-' + item.windowOrder + '-event'"
                                        (onChange)="onComboboxChange($event)"
                                        id="{{item.id}}-{{item.windowOrder}}-event"
                                        ></p-dropdown>
                                    <div *ngIf="optionNotValid">
                                        <small style="color: red;">{{ 'messages.error_isRequiredField' | translate }}</small>
                                    </div>
                                </div>

                                <div class="field">
                                    <label class="container-checkbox">{{ 'pass_application.display_component_user' | translate}}
                                        <input type="checkbox"
                                        [formControlName]="item.id!! + '-' + item.windowOrder + '-display-component-user'"
                                        id="{{item.id}}-{{item.windowOrder}}-display-component-user">
                                        <span class="checkmark"></span>
                                    </label>
                                </div>
                                <div class="horizontal-line"></div>
                                <div class="div-trash">
                                    <div class="pi pi-trash" (click)="deleteElement(item)"></div>
                                </div>
                            </p-accordionTab>
                        }
                        </div>

                    </p-accordion>
                </div>
            </div>
        </div>
    </div>

    <!-- p-confirmDialog [style]="{width: '395px'}"></p-confirmDialog-->


    <p-dialog [(visible)]="showWindowDialog" [header]="headerWindowDialog" [modal]="true" styleClass="p-fluid">
        <ng-template pTemplate="header">
            <div></div>
            <div class="flex justify-content-center" [style]="{'color': '#495057', 'font-weight':'600', 'font-size':'14px'}">
                {{ headerWindowDialog }}
            </div>
        </ng-template>
        <div class="edit dialog-content flex">
            <div class="field">
                <label for="windowName">{{ "content.name" | translate}}</label>
                <input type="text" pInputText id="windowName" formControlName="windowName"  required autofocus
                [ngClass]="!isValid('windowName') && form.controls['windowName'].touched? 'ng-invalid ng-dirty':'' "/>
                <div *ngIf="!isValid('windowName') && form.controls['windowName'].touched">
                    <small style="color: red;">{{ 'messages.error_isRequiredField' | translate }}</small>
                </div>
            </div>
            <div class="field">
                <label for="windowTarget">{{ 'pass_application.target' | translate }}</label>
                <p-dropdown
                    appendTo="body"
                    [options]="listTargets"
                    placeholder="{{ 'content.select' | translate}}"
                    optionLabel="value"
                    formControlName="windowTarget"
                    [ngClass]="!isValid('windowTarget') && form.controls['windowTarget'].touched? 'ng-invalid ng-dirty':'' "
                    id="windowTarget"
                    ></p-dropdown>
                <div *ngIf="!isValid('windowTarget') && form.controls['windowTarget'].touched">
                    <small style="color: red;">{{ 'messages.error_isRequiredField' | translate }}</small>
                </div>
            </div>
        </div>
        <div class="flex flex-row justify-content-center gap-2 mt-3 mb-5">
            <p-button label="{{ 'cancel' | translate }}" class="p-button-text" (click)="closeActionDialog()"
            [style]="{'color': '#000000' , 'background': '#FFFFFF', 'border':'none' }"></p-button>
            <p-button label="{{ 'save' | translate }}" class="p-button-text" (click)="saveWindow()"
            [style]="{'color': '#FFFFFF' , 'background': '#204887' }"
            ></p-button>
            <!--p-button [label]="'buttons.save' | translate" (click)="saveModification()" [text]="true" styleClass="w-full text-primary-50 border-1 border-white-alpha-30 hover:bg-white-alpha-10" class="w-full" ></p-button>
            <p-button [label]="'buttons.cancel'| translate" (click)="closeDialogReasonMondification()" [text]="true"  styleClass="w-full text-primary-50 border-1 border-white-alpha-30 hover:bg-white-alpha-10" class="w-full"></p-button-->
        </div>
    </p-dialog>

    <p-dialog [(visible)]="showSaveApplicationDialog" [modal]="true" styleClass="p-fluid">
        <ng-template pTemplate="header">
            <div></div>
            <div class="flex justify-content-center" [style]="{'color': '#495057', 'font-weight':'600', 'font-size':'14px'}">
                {{ applicationDialogHeader }}
            </div>
        </ng-template>
        <ng-template pTemplate="content">
            <div class="dialog-content flex" >
                <div class="field">
                    <label class="label-text" for="applicationName">{{ "content.name" | translate}}</label>
                    <input type="text" pInputText formControlName="applicationName" id="applicationName" required autofocus />
                </div>

                <!-- AppRegistry -->
                <div class="field">
                    <label for="appRegistryId">{{ 'pass_application.appRegistry' | translate }}</label>
                    <p-dropdown
                        appendTo="body"
                        [options]="listAppRegistry"
                        placeholder="{{'content.select' | translate}}"
                        optionLabel="value"
                        [(ngModel)]="selectedAppRegistry"
                        formControlName="appRegistryId"
                        dataKey="key"
                        id="technology"
                        >
                        <ng-template pTemplate="selectedItem">
                            <div class="flex align-items-center gap-2" *ngIf="selectedAppRegistry">
                                <div>{{ selectedAppRegistry.name }}</div>
                            </div>
                        </ng-template>
                        <ng-template let-appRegistry pTemplate="item">
                            <div class="flex align-items-center gap-2">
                                <div>{{ appRegistry.name }}</div>
                            </div>
                        </ng-template>
                    </p-dropdown>
                    <div *ngIf="!isValid('appRegistryId') && form.controls['appRegistryId'].touched">
                        <small style="color: red;">{{ 'messages.error_isRequiredField' | translate }}</small>
                    </div>
                </div>

                <div class="field">
                    <label class="label-text" for="applicationPath">{{ "pass_application.full_path" | translate}}</label>
                    <input type="text" pInputText formControlName="applicationPath" id="applicationPath" required autofocus />
                </div>
                <!-- Flow type -->
                <div class="field">
                    <label class="label-text" for="flowType">{{ 'pass_application.flow_type' | translate }}</label>
                    <p-dropdown
                        appendTo="body"
                        [options]="listOfApplicationFlowTypes"
                        placeholder="{{'content.select' | translate}}"
                        optionLabel="value"
                        [(ngModel)]="selectedFlowType"
                        formControlName="flowType"
                        dataKey="key"
                        id="flowType"
                        >
                        <ng-template pTemplate="selectedItem">
                            <div class="flex align-items-center gap-2" *ngIf="selectedFlowType">
                                <div>{{ selectedFlowType.value }}</div>
                            </div>
                        </ng-template>
                        <ng-template let-flowtype pTemplate="item">
                            <div class="flex align-items-center gap-2">
                                <div>{{ flowtype.value }}</div>
                            </div>
                        </ng-template>
                    </p-dropdown>
                    <div *ngIf="!isValid('flowType') && form.controls['flowType'].touched">
                        <small style="color: red;">{{ 'messages.error_isRequiredField' | translate }}</small>
                    </div>
                </div>

                <!-- Technology -->
                <div class="field">
                    <label class="label-text" for="technology">{{ 'pass_application.technology' | translate }}</label>
                    <p-dropdown
                        appendTo="body"
                        [options]="listTechnologies"
                        placeholder="{{'content.select' | translate}}"
                        optionLabel="value"
                        [(ngModel)]="selectedTechnology"
                        formControlName="technology"
                        dataKey="key"
                        id="technology"
                        >
                        <ng-template pTemplate="selectedItem">
                            <div class="flex align-items-center gap-2" *ngIf="selectedTechnology">
                                <div>{{ selectedTechnology.value }}</div>
                            </div>
                        </ng-template>
                        <ng-template let-technology pTemplate="item">
                            <div class="flex align-items-center gap-2">
                                <div>{{ technology.value }}</div>
                            </div>
                        </ng-template>
                    </p-dropdown>
                    <div *ngIf="!isValid('technology') && form.controls['technology'].touched">
                        <small style="color: red;">{{ 'messages.error_isRequiredField' | translate }}</small>
                    </div>
                </div>

                <!-- Application type -->
                <div class="field">
                    <label class="label-text" for="applicationType">{{ 'pass_application.application_type' | translate }}</label>
                    <p-dropdown
                        appendTo="body"
                        [options]="listApplicationTypes"
                        placeholder="{{'content.select' | translate}}"
                        optionLabel="value"
                        [(ngModel)]="selectedApplicationType"
                        formControlName="applicationType"
                        id="applicationType"
                        >
                        <ng-template pTemplate="selectedItem">
                            <div class="flex align-items-center gap-2" *ngIf="selectedApplicationType">
                                <div>{{ selectedApplicationType.value }}</div>
                            </div>
                        </ng-template>
                        <ng-template let-appType pTemplate="item">
                            <div class="flex align-items-center gap-2">
                                <div>{{ appType.value }}</div>
                            </div>
                        </ng-template>
                    </p-dropdown>
                    <div *ngIf="!isValid('applicationType') && form.controls['applicationType'].touched">
                        <small style="color: red;">{{ 'messages.error_isRequiredField' | translate }}</small>
                    </div>
                </div>

                <!-- Data source -->

                <div class="field">
                    <label class="label-text" for="dataSource">{{ 'pass_application.data_source' | translate }}</label>
                    <p-dropdown
                        appendTo="body"
                        [options]="listDataSources"
                        placeholder="{{'content.select' | translate}}"
                        optionLabel="name"
                        [(ngModel)]="selectedDataSource"
                        formControlName="dataSource"
                        id="dataSource"
                        >
                        <ng-template pTemplate="selectedItem">
                            <div class="flex align-items-center gap-2" *ngIf="selectedDataSource">
                                <div>{{ selectedDataSource.name }}</div>
                            </div>
                        </ng-template>
                        <ng-template let-ds pTemplate="item">
                            <div class="flex align-items-center gap-2">
                                <div>{{ ds.name }}</div>
                            </div>
                        </ng-template>
                    </p-dropdown>
                    <div *ngIf="!isValid('dataSource') && form.controls['dataSource'].touched">
                        <small style="color: red;">{{ 'messages.error_isRequiredField' | translate }}</small>
                    </div>
                </div>

                <div class="field">
                    <table>
                        <tr>
                            <td>{{ 'flow.publish' | translate }}</td>
                            <td class="right"><p-inputSwitch id="isPublished" formControlName="isPublished"></p-inputSwitch></td>
                        </tr>
                    </table>
                </div>
            </div>
         </ng-template>

         <ng-template pTemplate="footer">
            <div class="dialog-footer">
                <div class="flex justify-content-center flex-wrap gap-2">
                    <button pButton pRipple label="{{ 'cancel' | translate }}" class="p-button-text" (click)="closeSaveApplicationDialog()"></button>
                    <p-button label="{{ 'save' | translate }}" class="p-button-text" (click)="acceptSaveApplication()"
                    [style]="{'color': '#FFFFFF' , 'background': '#204887' }"
                    ></p-button>
                </div>
            </div>

         </ng-template>
    </p-dialog>
</div>
