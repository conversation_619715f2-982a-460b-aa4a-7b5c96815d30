import { HttpClient } from '@angular/common/http';
import { FormGroup, FormBuilder, Validators, FormControl } from '@angular/forms';
import { ValidatorService } from 'src/verazial-common-frontend/modules/shared/services/validator.service';
import { WindowParametersModel } from 'src/verazial-common-frontend/core/general/application/data/model/window-parameters.model';
import { ApplicationEntity } from 'src/verazial-common-frontend/core/general/application/domain/entities/application.entity';
import { GetApplicationByIdUseCase } from 'src/verazial-common-frontend/core/general/application/domain/use-cases/get-application-by-id.use-case';
import { SubjectAppCredentialsEntity } from 'src/verazial-common-frontend/core/general/subject-application/domain/entities/subject-app-credentials.entity';
import { SubjectApplicationEntity } from 'src/verazial-common-frontend/core/general/subject-application/domain/entities/subject-application.entity';
import { AddSubjectsAppCredentialsUseCase } from 'src/verazial-common-frontend/core/general/subject-application/domain/use-cases/add-subjects-app-credentials.use-case';
import { AddSubjectsAppUseCase } from 'src/verazial-common-frontend/core/general/subject-application/domain/use-cases/add-subjects-app.use-case';
import { DeleteSubjectAppByIdUseCase } from 'src/verazial-common-frontend/core/general/subject-application/domain/use-cases/delete-subject-app-by-id.use-case';
import { GetAllSubjectsUseCase } from 'src/verazial-common-frontend/core/general/subject/domain/use-cases/get-all-subjects.use-case';
// import { SubjectApplication} from 'src/verazial-common-frontend/core/models/subject-app';
import {v4 as uuidv4} from 'uuid';
import { GetWindowParamsByAppIdUseCase } from 'src/verazial-common-frontend/core/general/application/domain/use-cases/get-window-params-by-app-id.use-case';
import { TranslateService } from '@ngx-translate/core';
import { Component, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { GetAllCredentialsBySubjectAppIdUseCase } from 'src/verazial-common-frontend/core/general/subject-application/domain/use-cases/get-all-credentials-by-subject-app-id.use-case';
import { ConfirmationService, FilterService, MenuItem, MessageService } from 'primeng/api';
import { UpdateSubjectAppByIdUseCase } from 'src/verazial-common-frontend/core/general/subject-application/domain/use-cases/update-subject-app-by-id.use-case';
import { UpdateSubjectAppCredentialsByIdUseCase } from 'src/verazial-common-frontend/core/general/subject-application/domain/use-cases/update-subject-app-credentials-by-id.use-case';
import { GetAppWindowByAppIdUseCase } from 'src/verazial-common-frontend/core/general/application/domain/use-cases/get-app-window-by-app-id.use-case';
import { AddCredentialsUseCase } from 'src/verazial-common-frontend/core/general/credential/domain/use-cases/add-credentials.use-case';
import { GetWindowParamsByWindowIdUseCase } from 'src/verazial-common-frontend/core/general/application/domain/use-cases/get-window-params-by-window-id.use-case';
import { CredentialParameters } from 'src/verazial-common-frontend/core/general/credential/common/credential-parameters.interface';
import { CredentialEntity } from 'src/verazial-common-frontend/core/general/credential/domain/entity/credential.entity';
import { ApplicationWindowEntity } from 'src/verazial-common-frontend/core/general/application/domain/entities/application-window.entity';
import { GetAppDataSourceByIdUseCase } from 'src/verazial-common-frontend/core/general/data-source/domain/use-cases/get-app-data-source-by-id.use-case';
import { SourceType } from 'src/verazial-common-frontend/core/models/source-type.enum';
import { DeleteCredentialByNumIdAndSubjectAppIdUseCase } from 'src/verazial-common-frontend/core/general/credential/domain/use-cases/delete-credential-by-num-id-and-subject-app-id.use-case';
import { GetAllActiveApplicationsUseCase } from 'src/verazial-common-frontend/core/general/application/domain/use-cases/get-all-active-applications.use-case';
import { ContextMenu } from 'primeng/contextmenu';
import { SubjectEntity } from 'src/verazial-common-frontend/core/general/subject/domain/entity/subject.entity';
import { GetAllSubjectAppsByNumIdUseCase } from 'src/verazial-common-frontend/core/general/subject-application/domain/use-cases/get-all-subjec-apps-by-num-id.use-case';
import { FieldType } from 'src/verazial-common-frontend/core/general/credential/common/field-type.enum';
import { RoleEntity } from 'src/verazial-common-frontend/core/general/common/entity/role.entity';
import { AccessIdentifier } from 'src/verazial-common-frontend/core/models/access-identifier.enum';
import { CheckPermissionsService } from 'src/verazial-common-frontend/core/services/check-permissions-service';
import { ConsoleLoggerService } from 'src/verazial-common-frontend/core/services/console-logger.service';
import { GetAllRolesUseCase } from 'src/verazial-common-frontend/core/general/role/domain/use-cases/roles/get-all-roles.use-case';
import { RoleType } from 'src/verazial-common-frontend/core/general/role/common/enum/role-type.enum';
import { Table } from 'primeng/table';
import { LocalStorageService } from 'src/verazial-common-frontend/core/services/local-storage.service';
import { ExtraData } from 'src/verazial-common-frontend/core/general/actionsV2/domain/entity/extra-data.interface';
import { AuditTrailFields } from 'src/verazial-common-frontend/core/models/audit-trail-fields.enum';
import { AuditTrailService, ReasonActionTypeEnum, ReasonTypeEnum } from 'src/verazial-common-frontend/core/services/audit-trail.service';
import { AuditTrailActions } from 'src/verazial-common-frontend/core/models/audit-trail-actions.enum';
import { GetSubjectAppByIdUseCase } from 'src/verazial-common-frontend/core/general/subject-application/domain/use-cases/get-subject-app-by-id.use-case';
import { GetSubjectAppCredentialsByIdUseCase } from 'src/verazial-common-frontend/core/general/subject-application/domain/use-cases/get-subject-app-credentials-by-id.use-case';
import { WindowTarget } from 'src/verazial-common-frontend/core/general/application-flow/common/interfaces/windows-target.enum';
import { UserSubjectEnum } from 'src/verazial-common-frontend/core/models/user-subject.enum';
import { GetSubjectsRequestEntity } from 'src/verazial-common-frontend/core/general/subject/domain/entity/get-subjects-request.entity';
import { GetKonektorPropertiesUseCase } from 'src/verazial-common-frontend/core/general/konektor/domain/use-cases/get-konektor-properties.use-case';
import { GetSettingsByApplicationUseCase } from 'src/verazial-common-frontend/core/general/manager/domain/use-cases/get-settings-by-application.use-case';
import { environment } from 'src/environments/environment';
import { OperationStatus } from 'src/verazial-common-frontend/core/models/operation-status.interface';
import { Status } from 'src/verazial-common-frontend/core/models/status.enum';
import { GeneralSettings } from 'src/verazial-common-frontend/core/general/manager/common/models/general-settings.model';
import { KonektorPropertiesEntity } from 'src/verazial-common-frontend/core/general/konektor/domain/entity/konektor-properties.entity';
import { GetAppRegistriesUseCase } from 'src/verazial-common-frontend/core/general/app-registry/domain/use-cases/get-app-registries.use-case';
import { AppRegistryEntity } from 'src/verazial-common-frontend/core/general/app-registry/domain/entities/app-registry.entity';

@Component({
  selector: 'app-user-applications-page',
  templateUrl: './user-applications-page.component.html',
  styleUrls: ['./user-applications-page.component.css'],
})
export class UserApplicationsPageComponent implements OnInit, OnDestroy {

  items: MenuItem[] | undefined;

  listConnectionTypes = [
    {value: 'IPV4', label: 'IPV4'},
    {value: 'IPV6', label: 'IPV6'},
    {value: 'HOSTNAME', label: 'HOSTNAME'}
  ];

  showDesktopOptions: boolean = false;
  listApplicationWebType = 'WEB';

  error: boolean = false;
  success: boolean = false;
  title: string = "";
  message: string = "";

  sidebarVisible: boolean = false;

  loading: boolean = false;
  loadingCredentials: boolean = false;

  selectedSubjects: SubjectEntity[] = [];
  selectedAppDropDown!: ApplicationEntity;
  selectAll: boolean = false;

  isRunningLocally: boolean = false;
  hasDataSource: boolean = false;

  trueValue: boolean = true;
  falseValue: boolean = false;

  newSubjectApp!: SubjectApplicationEntity;
  listSubjectApplications: SubjectApplicationEntity[] = [];
  updatedListUserApplications: SubjectApplicationEntity[] = [];

  subjectType = UserSubjectEnum.SUBJECT;
  useLazyLoad: boolean = false;
  getSubjectsRequest = new GetSubjectsRequestEntity();
  listSubjects: SubjectEntity[] = [];
  listSubjectsResponse!: SubjectEntity[];
  allRoles: RoleEntity[] = [];
  subjectApplication!: SubjectApplicationEntity;
  application!: ApplicationEntity;
  applicationParameters: WindowParametersModel[] = [];
  listWindows: ApplicationWindowEntity[] = [];

  applicationDetailsReady: boolean = false;
  subjectName: string = "";
  numberSubjectApps: number = 0;
  numberAppCredentials: number = 0;

  subjects!: any[];

  listApplications!: ApplicationEntity[];
  listAllApplications: ApplicationEntity[] = [];
  listAppRegistries!: AppRegistryEntity[];
  listSubjectCredentials: SubjectAppCredentialsEntity[] = [];
  passwordSubjectCredentials: boolean[] = [];
  listTempSubjectCredentials: SubjectAppCredentialsEntity[] = [];
  deleteApplicationDialog: boolean = false;

  selectedApplication!: ApplicationEntity;

  savedSubjectApp: SubjectApplicationEntity[] = [];

  showApplicationDetails: boolean = false;
  showUserCredentials: boolean = false;

  subject: string = "";

  subjectNumId: string = "";

  isLoading = false;

  confirmDialogTimeoutLimit: number = 0;
  startCheckingInactivity: boolean = false;

  // Date Range Filter
  formGroup: FormGroup = new FormGroup({
    date: new FormControl<Date[] | null>(null)
  });
  dateFilterValues = {
    startDate: null,
    endDate: null
  };
  rangeDates: Date[] | null = null;

  @ViewChild('cm') cm: ContextMenu | undefined;

  // Access code identifier
  access_identifier: string = AccessIdentifier.PASS_ASSIGN_APP;
  canReadAndWrite: boolean = false;
  showDetailsAccess_access_identifier: string = AccessIdentifier.PASS_APP_ASSIGNMENT_DETAILS;
  showDetailsAccess = false;

  /** Settings */
  managerSettings?: GeneralSettings;
  konektorProperties?: KonektorPropertiesEntity;

  /** Form */
  dataForm: FormGroup = this.fb.group({
    firstName: [],
    secondName: [],
    lastName: [],
    email: [],
    subjectAppName: ['',[Validators.minLength(2), Validators.required]],
    hostName: [],
    hostName2: ['', [Validators.required, Validators.minLength(10)]],
    username: [],
    password: [],
    runingLocally: [],
    updateAll: [],
    selectAllUsers: [],
    applications:[],
    connectionType: [],
    connectionType2: [],
    appRegistry: ['', [Validators.required]],
    appSelectBox:['', [Validators.required]],
    value:[],
    canUserUpdateCred: [],
    canUserUpdate:[],
    canUserUpdate2: [],
    mustUserUpdateDedentials: [],
    mustUserUpdateDedentials2: [],
    mustUserUpdate: []
  });

  constructor(
    private fb: FormBuilder,
    private loggerService: ConsoleLoggerService,
    private localStorageService: LocalStorageService,
    private messageService: MessageService,
    private confirmationService: ConfirmationService,
    private checkPermissions: CheckPermissionsService,
    private filterService: FilterService,
    /* Audit Trail */
    public auditTrailService: AuditTrailService,
    /* Subjects */
    private getAllSubjectsUseCase: GetAllSubjectsUseCase,
    private getAllRolesUseCase: GetAllRolesUseCase,
    /* App Registry */
    private getAppRegistriesUseCase: GetAppRegistriesUseCase,
    /* Applications */
    private getAllApplicationsUseCase: GetAllActiveApplicationsUseCase,
    private getAppWindowByAppIdUseCase: GetAppWindowByAppIdUseCase,
    private getWindowParamsByAppIdUseCase: GetWindowParamsByAppIdUseCase,
    private getApplicationByIdUseCase: GetApplicationByIdUseCase,
    /* Subject Applications */
    private addSubjectsAppUseCase: AddSubjectsAppUseCase,
    private getAllSubjectAppsByNumIdUseCase: GetAllSubjectAppsByNumIdUseCase,
    private getSubjectAppByIdUseCase: GetSubjectAppByIdUseCase,
    private deleteSubjectAppByIdUseCase: DeleteSubjectAppByIdUseCase,
    private updateSubjectAppByIdUseCase: UpdateSubjectAppByIdUseCase,
    /** Subject App Credentials */
    private addSubjectsAppCredentialsUseCase: AddSubjectsAppCredentialsUseCase,
    private getSubjectCredentialBySubjectAppId: GetAllCredentialsBySubjectAppIdUseCase,
    private updateSubjectAppCredentialsByIdUseCase: UpdateSubjectAppCredentialsByIdUseCase,
    private getSubjectAppCredentialsByIdUseCase: GetSubjectAppCredentialsByIdUseCase,
    /** Window Parameters */
    private getWindowParamsByWindowIdUseCase: GetWindowParamsByWindowIdUseCase,
    /** Datasource */
    private getAppDataSourceByIdUseCase: GetAppDataSourceByIdUseCase,
    /** External Credentials */
    private addCredentialUseCase: AddCredentialsUseCase,
    private deleteCredentialByNumIdAndSubjectAppIdUseCase: DeleteCredentialByNumIdAndSubjectAppIdUseCase,
    /** Http Client */
    private http: HttpClient,
    /** Translate */
    private translate: TranslateService,
    /** Manager */
    private getSettingsByApplicationUseCase: GetSettingsByApplicationUseCase,
    /** Konektor */
    private getKonektorPropertiesUseCase: GetKonektorPropertiesUseCase,
  ) {
    this.filterService.register('customStringArray', (value: any, filter: any): boolean => {
      if (!filter) return true; // If no filter provided, show all
      else if (typeof value === 'object' && typeof filter === 'string') {
        return value.map((role: RoleEntity) => role.name).join(', ').toLowerCase().includes(filter.toLowerCase());
      }
      return false;
    });
    this.filterService.register('customDateRange', (value: any, filter: any): boolean => {
      if (!filter || (!filter.startDate && !filter.endDate)) {
        return true; // If no filter, show all
      }
      const dateValue = new Date(value).getTime();
      const startDate = filter.startDate ? new Date(filter.startDate).getTime() : null;
      const endDate = filter.endDate ? new Date(filter.endDate).getTime() : null;
      if (startDate && endDate) {
        return dateValue >= startDate && dateValue <= endDate;
      } else if (startDate) {
        return dateValue >= startDate;
      } else if (endDate) {
        return dateValue <= endDate;
      }
      return false;
    });
  }

  ngOnInit(): void {
    this.isLoading = true;
    this.getAllRoles();
    this.getAllSubjects();
    this.getAllApplications();
    this.getSettings();

    this.canReadAndWrite = this.checkPermissions.hasReadAndWritePermissions(this.access_identifier);
    this.showDetailsAccess = this.checkPermissions.hasReadAndWritePermissions(this.showDetailsAccess_access_identifier);

    this.items = [
      {
          label: 'Add Application',
          icon: 'pi pi-check-square',
          command: () => { this.onShowAddApplication();}
      },
      {
          label: 'View',
          icon: 'pi pi-eye',
          command: () => { this.onShowViewDetails();}
      }
    ];

    this.getSubjectsRequest.offset = 0;
    this.getSubjectsRequest.limit = 10;
  }

  ngOnDestroy() {
    // Clean up the timeout if the component is destroyed
    this.closeConfirmationDialog();
    this.auditTrailService.resetInactivityMonitor();
  }

  resetInactivityMonitor() {
    this.startCheckingInactivity = false;
    this.confirmDialogTimeoutLimit = 0;
  }

  getAllRoles() {
    this.getAllRolesUseCase.execute().then(
      (data) => {
        data.forEach(role => {
          if (role.type == RoleType.SUBJECT) {
            this.allRoles.push({
              id: role.id,
              name: role.name,
              level: role.level,
              type: role.type,
              description: role.description,
              showInMenu: role.showInMenu,
              createdAt: undefined,
              updatedAt: undefined,
            });
          }
        });
      },
      (e) => {
        // this.loggerService.error(e);
        const at_attributes: ExtraData[] = [{ name: AuditTrailFields.ERROR, value: JSON.stringify(e) }];
        this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.GET_ALL_ROLES, 0, 'ERROR', '', at_attributes);
      },
    );
  }

  /** Getting all subjects from Verázial ID Tasks */
  async getAllSubjects(){
    this.getAllSubjectsUseCase.execute({offset:0, limit:10000}).then(
      (subjects) => {
        this.listSubjectsResponse = subjects;
        this.listSubjects = subjects;
        this.isLoading = false;
      },
      (e: any) => {
        // this.loggerService.error(e);
        this.isLoading = false;
      }
    );
  }

  /** Loading all applications available in the system */
  async getAllApplications(){
    try{
      this.listAllApplications = await this.getAllApplicationsUseCase.execute();
      this.listAppRegistries = await this.getAppRegistriesUseCase.execute();
    }catch(e: any){
      // this.loggerService.error(e);
    }
  }

  getSettings() {
    this.getSettingsByApplicationUseCase.execute({ applicationName: environment.application }).then(
      (data) => {
        this.managerSettings = data.settings;
      },
      (e) => {
        // this.loggerService.error(e);
        let status: OperationStatus = {
          status: Status.ERROR,
          message: `Getting Settings - ${e.message}`
        }
        // this.loggerService.error('Error Retrieving Manager Settings:');
        // this.loggerService.error(e);
        const at_attributes: ExtraData[] = [
          { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
          { name: AuditTrailFields.APPLICATION_ID, value: environment.application },
        ];
        this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.GET_SETTINGS_BY_APPLICATION, 0, 'ERROR', '', at_attributes);
      }
    ).finally(() => {
      this.getKonektorPropertiesUseCase.execute().subscribe({
        next: (data) => {
          this.konektorProperties = data;
          if (data.apiGatewayGrpc) {
            this.localStorageService.setApiGatewayURL(data.apiGatewayGrpc);
          }
          else {
            this.localStorageService.destroyApiGatewayURL();
          }
        },
        error: (e) => {
          // this.loggerService.error(e);
          let status: OperationStatus = {
            status: Status.ERROR,
            message: `Getting Konektor ProPerties - ${e.message}`
          }
          // this.loggerService.error('Error Retrieving Manager Settings:');
          // this.loggerService.error(e);
          const at_attributes: ExtraData[] = [
            { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
            { name: AuditTrailFields.APPLICATION_ID, value: environment.application },
          ];
          this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.GET_SETTINGS_BY_APPLICATION, 0, 'ERROR', '', at_attributes);
        },
      });
    });
  }

  setSelection(event: any): void {
    this.selectedSubjects = [... event];
  }

  /** Getting all the Subject's Applications */
  async getSubjectAppsBySubjectId(subjectId: string){
    this.loading = true;
    this.listSubjectApplications = [];
    this.updatedListUserApplications = [];
    try{
      this.loading = true;
      this.listSubjectApplications = await this.getAllSubjectAppsByNumIdUseCase.execute({numId: subjectId});
      if(this.listSubjectApplications){
        this.loading = false;
      }

    }catch(e: any){
      // this.loggerService.error(e);
    }
  }

  getAppNameByAppId(appId: string){
    let app = this.listAllApplications.filter(app => app.id == appId);
    return app[0].applicationName;
  }

  getAppRegistryNameByAppId(appId: string){
    let id = this.listAllApplications.filter(app => app.id == appId)[0].appRegistryId;
    let app = this.listAppRegistries.filter(app => app.id == id);
    return app[0].name;
  }

  cleanMessage(){
    this.error = false;
    this.success = false;
    this.message = "";
    this.title = "";
  }

  hiddeMessages(): void {
    setTimeout(() => {
      this.error = false;
      this.success = false;
      this.message = "";
      this.title = "";
    }, 5000);
  }

  /** On select App Registry filter applications for that Registry*/
  async onAppRegistrySelect(event: any){
    try{
      if(event){
        let appRegistry: AppRegistryEntity = event.value;
        this.listApplications = this.listAllApplications.filter(app => app.appRegistryId == appRegistry.id);
      }
    }catch(e: any){
      // this.loggerService.error(e);
    }
  }

  /** On select an application loads all the UI components */
  async onApplicationSelect(event: any){
    try{
      this.applicationDetailsReady = false;
      if(event){
        let application: ApplicationEntity = event.value;
        this.selectedApplication = application;
        this.showDesktopOptions = this.selectedApplication.applicationType != this.listApplicationWebType;
        if (this.showDesktopOptions) {
          this.dataForm.get('connectionType')?.setValidators([Validators.required]);
          this.dataForm.get('hostName')?.setValidators([Validators.required, Validators.minLength(10)]);
        }
        else {
          this.dataForm.get('connectionType')?.clearValidators();
          this.dataForm.get('hostName')?.clearValidators();
        }
        let windowParameters = await this.getWindowParamsByAppIdUseCase.execute({appId: application.id!!});

        let tmp = await this.getAppWindowByAppIdUseCase.execute({appId: application.id!!});

        this.listWindows = Array.from(
          new Map(tmp.map(item => [item.id, item])).values()
        );

        let tempListWindows: ApplicationWindowEntity[] = [];
        for(let i=0; i< this.listWindows.length; i++){
          let parameters = await this.getWindowParamsByWindowIdUseCase.execute({windowId: this.listWindows[i].id!!});

          let matchParams = parameters.find((_v, k) => _v.uiComponentType == 'TEXTBOX' || _v.uiComponentType == 'COMBOBOX' ||
          _v.uiComponentType == 'CHECKBOX' || _v.uiComponentType == 'LIST-CHECKBOX' || _v.uiComponentType == 'DATAGRID' ||
          _v.uiComponentType == FieldType.PASSWORD)
          if(matchParams){
            this.listWindows[i].windowParameters = parameters;
            tempListWindows = [...tempListWindows, this.listWindows[i]]
          }

        }
        this.newUiComponents(windowParameters);
      }
    }catch(e: any){
      // this.loggerService.error(e);
    }
  }


  /** List of UI components to be displayed in the front end */
  newUiComponents(applicationDetails?: WindowParametersModel[]){
    this.applicationParameters = [];
    if(applicationDetails){
      for(const app of applicationDetails){
        if(app.uiComponentType == 'TEXTBOX' || app.uiComponentType == 'COMBOBOX' ||
        app.uiComponentType == 'CHECKBOX' || app.uiComponentType == 'LIST-CHECKBOX' ||
        app.uiComponentType == 'DATAGRID' || app.uiComponentType == FieldType.PASSWORD){
          this.applicationParameters = [...this.applicationParameters, app];
        }
      }
    }
    this.applicationDetailsReady = true;
  }

  onChangeRunLocally(event: any){
    this.isRunningLocally = !this.isRunningLocally;
  }

  onChangeUpdateAll(event: any){

  }

  async saveUserAppData(){
    // this.cleanMessage();
    let newListSubjectApps: SubjectApplicationEntity[] = [];
    let newListCredentials: SubjectAppCredentialsEntity[] = [];

    this.dataForm.get('subjectAppName')?.markAsTouched();
    this.dataForm.get('hostName')?.markAsTouched();
    this.dataForm.get('appRegistry')?.markAsTouched();
    this.dataForm.get('appSelectBox')?.markAsTouched();
    this.dataForm.get('connectionType')?.markAsTouched();

    if(this.dataForm.get('appRegistry')?.value == null
    || this.dataForm.get('appRegistry')?.value == ""){
      this.messageService.add({
        severity: 'error',
        summary: this.translate.instant('content.errorTitle'),
        detail: this.translate.instant('pass_assigment.select_apps'),
        life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
      });
      return;
    }
    if(this.dataForm.get('appSelectBox')?.value == null
    || this.dataForm.get('appSelectBox')?.value == ""){
      this.messageService.add({
        severity: 'error',
        summary: this.translate.instant('content.errorTitle'),
        detail: this.translate.instant('pass_assigment.select_apps_flow'),
        life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
      });
      return;
    }

    if(this.selectedSubjects.length > 0){
      if(this.isValid('subjectAppName')){
        if (this.showDesktopOptions && !this.isValid('hostName')) {
          this.messageService.add({
            severity: 'error',
            summary: this.translate.instant('content.errorTitle'),
            detail: this.translate.instant('pass_assigment.host_name_required'),
            life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
          });
          return;
        }

        for(const subject of this.selectedSubjects){
          // Adding users
          let subjectAppId = uuidv4();

          let newSubjectApplication: SubjectApplicationEntity = {
            id: subjectAppId,
            numId: subject.numId,
            applicationId: this.selectedApplication.id,
            appRegistryId: this.selectedApplication.appRegistryId,
            subjectAppName: this.dataForm.controls['subjectAppName'].value,
            connectionMethod: this.dataForm.controls['connectionType'].value!=null?this.dataForm.controls['connectionType'].value:'',
            host: this.dataForm.controls['hostName'].value!=null?this.dataForm.controls['hostName'].value:'',
            canUserUpdateCredentials: this.dataForm.controls['canUserUpdateCred'].value!=null?this.dataForm.controls['canUserUpdateCred'].value:false,
            credentialsInitialised: this.dataForm.controls['mustUserUpdate'].value!=null?!this.dataForm.controls['mustUserUpdate'].value:true,
          };

          newListSubjectApps = [...newListSubjectApps, newSubjectApplication];
          // Adding credentials
          for(const parameter of this.applicationParameters){
            let newcredentialId = uuidv4();
            let paramValue: any;

            if(parameter.uiComponentType == 'CHECKBOX'){
              paramValue = (<HTMLInputElement>document.getElementById(parameter.id!!)).checked;
            }else{
              if((<HTMLInputElement>document.getElementById(parameter.id!!)).value!=null && (<HTMLInputElement>document.getElementById(parameter.id!!)).value!=undefined){
                paramValue = (<HTMLInputElement>document.getElementById(parameter.id!!)).value;
              }else{
                paramValue = '';
              }
            }

            let newCredentials: SubjectAppCredentialsEntity = {
              id: newcredentialId,
              subjectAppId: subjectAppId,
              appParamId: parameter.id,
              value: paramValue!=null?String(paramValue):undefined
            };

            newListCredentials = [...newListCredentials, newCredentials];
          }

          this.saveSubjectApp(newListSubjectApps, newListCredentials);
          if(this.selectedApplication.dataSourceId){
            let dataSource = await this.getAppDataSourceByIdUseCase.execute({id: this.selectedApplication.dataSourceId});
            if(dataSource.sourceType == SourceType.API_VERAZIAL || dataSource.sourceType == SourceType.LDAP_VERAZIAL){
              this.saveCredentialExternalDataSource(newListSubjectApps, newListCredentials);
            }
          }

          newListSubjectApps = [];
          newListCredentials = [];
        }
        this.selectedSubjects = [];
      }
    }else{
      this.messageService.add({
        severity: 'error',
        summary: this.translate.instant('content.errorTitle'),
        detail: this.translate.instant('content.select_user'),
        life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
      });
    }
  }

  // Saving subject application
  async saveSubjectApp(subjectApps: SubjectApplicationEntity[], credentials: SubjectAppCredentialsEntity[]){
    this.cleanMessage();
    try{
      const at_attributes: ExtraData[] = [
        { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(subjectApps) },
      ];
      this.auditTrailService.auditTrailSelectReason(ReasonTypeEnum.PASS_APP_MANAGEMENT, AuditTrailActions.ADD_PSS, ReasonActionTypeEnum.CREATE, async () => {
        this.savedSubjectApp = await this.addSubjectsAppUseCase.execute(subjectApps);
        if (this.savedSubjectApp){
          this.saveSubjectAppCredentials(credentials);
        }
      }, at_attributes);
    }catch(e:any){
      this.messageService.add({
        severity: 'error',
        summary: this.translate.instant('content.errorTitle'),
        detail: String(e.message),
        life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
      });
    }
  }


  // Saving subject credentials.
  async saveSubjectAppCredentials(subjectAppCredentials: SubjectApplicationEntity[]){
    this.cleanMessage();
    try {
      const at_attributes: ExtraData[] = [
        { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(subjectAppCredentials) },
      ];
      this.auditTrailService.auditTrailSelectReason(ReasonTypeEnum.PASS_APP_MANAGEMENT, AuditTrailActions.ADD_PSS_CRED, ReasonActionTypeEnum.CREATE, async () => {
        let response = await this.addSubjectsAppCredentialsUseCase.execute(subjectAppCredentials);

        if(response){
          this.messageService.add({
            severity: 'success',
            summary: this.translate.instant('content.successTitle'),
            detail: this.translate.instant('messages.saved_successfully'),
            life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
          });
        }
      }, at_attributes);
    } catch (e: any) {
      this.messageService.add({
        severity: 'error',
        summary: this.translate.instant('content.errorTitle'),
        detail: String(e.message),
        life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
      });
    }
  }

  // Save credentials in Verázial Centralised credentials
  async saveCredentialExternalDataSource(subjectApplications: SubjectApplicationEntity[], credentials: SubjectAppCredentialsEntity[]){

    let credentialParameters: CredentialParameters[] = [];

    for(const window of this.listWindows){
      if (window.target == WindowTarget.AUTHENTICATION){
        if(window.windowParameters){
          for(const param of window.windowParameters){
            let matchCredential = credentials.find((_v,k)=> _v.appParamId == param.id);
            let type: FieldType;
            if(matchCredential){
              if(param.uiComponentType == FieldType.PASSWORD){
                type = FieldType.PASSWORD;
              }else{
                type = FieldType.TEXT;
              }
              let newCredentialParameter: CredentialParameters = {
                key: param.name!!,
                value: matchCredential.value!!,
                type: type
              }
              credentialParameters = [...credentialParameters, newCredentialParameter];
            }
          }
        }
      }
    }
    let newCredentials: CredentialEntity = {
      numId: subjectApplications[0].numId!!,
      subjectApplicationId: subjectApplications[0].id!!,
      parameters: credentialParameters
    }

    if(credentialParameters.length>0){
      const at_attributes: ExtraData[] = [
        { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(newCredentials) },
      ];
      this.auditTrailService.auditTrailSelectReason(ReasonTypeEnum.PASS_APP_MANAGEMENT, AuditTrailActions.ADD_APP_CRED, ReasonActionTypeEnum.CREATE, () => {
        this.addCredentialUseCase.execute(newCredentials).subscribe({
          next: (data) => {
            // // this.loggerService.debug(data);
          },
          error: (e) =>{
            // this.loggerService.error(e);
            const at_attributes: ExtraData[] = [
              { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
              { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(newCredentials) },
            ];
            this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.ADD_APP_CRED, 0, 'ERROR', '', at_attributes);
          },
          complete: ()=>{
            // // this.loggerService.debug("completed");
          }
        });
      }, at_attributes);
    }
  }

  onSubjectSelectionChange(value = []) {
    this.selectedSubjects = value;
  }

  /** On change the subject selected */
  handleSelectSubject(event: any){
    this.updatedListUserApplications = [];
    this.listSubjectCredentials = [];
    this.passwordSubjectCredentials = [];
    this.getSubjectAppsBySubjectId(event.numId);
    this.loading = false;

    this.subject = `${event.names} ${event.lastNames}`
    this.subjectNumId = event.numId!!;
    // this.showApplicationDetails = true;
  }

  onShowAddApplication(subjects?: SubjectEntity[]){
    if (subjects && subjects.length == 1) {
      this.onShowAddApplicationSingleUser(subjects[0]);
    }
    else {
      if (subjects && subjects.length > 0) {
        this.selectedSubjects = subjects;
      }
      this.sidebarVisible = true;
    }
  }

  onShowAddApplicationSingleUser(user: SubjectEntity){
    this.sidebarVisible = true;
    this.selectedSubjects = [];
    this.selectedSubjects.push(user);
  }

  onShowViewDetails(data?: any){

    this.listSubjectCredentials = [];
    this.passwordSubjectCredentials = [];
    let selectedSubject = new SubjectEntity();
    if(data){
      selectedSubject = data;
    }else{
      selectedSubject = this.selectedSubjects[0];
    }
    this.subject = `${selectedSubject.names} ${selectedSubject.lastNames}`;
    this.subjectNumId = selectedSubject.numId!!;

    this.getSubjectAppsBySubjectId(selectedSubject.numId!);
    this.showApplicationDetails = true;
  }

  /** On change the application selected */
  async handleSelectApplication(event: any){
    this.listSubjectCredentials = [];
    this.passwordSubjectCredentials = [];
    try {
      this.loadingCredentials = true;
      this.listSubjectCredentials = await this.getSubjectCredentialBySubjectAppId.execute({subjectAppId: event.id});
      this.passwordSubjectCredentials = this.listSubjectCredentials.map(() => false);
      // this.numberAppCredentials = this.listSubjectCredentials.length;
      this.listTempSubjectCredentials = {...this.listSubjectCredentials};
      this.getWindowParametersByAppId(event.applicationId, this.listSubjectCredentials);
    } catch (e: any) {
      // this.loggerService.error(e);
    }
  }

  // Getting all Application's window parameters
  async getWindowParametersByAppId(appId: string, credentials: SubjectAppCredentialsEntity[]){
    try {
      let windowsParameters = await this.getWindowParamsByAppIdUseCase.execute({appId: appId});
      credentials.forEach((credential, index) => {
        let matchCredential = windowsParameters.find((v,k) => v.id === credential.appParamId)
        if(matchCredential)
        {
          credential.parameterName = matchCredential.name;
          if (matchCredential?.uiComponentType === 'PASSWORD') {
            this.passwordSubjectCredentials[index] = true;
          }
        }
      });
      this.loadingCredentials = false;
    } catch (e: any) {
      // this.loggerService.error(e);
    }
  }

  // On edit subject's application
  onApplicationRowEditInit(app: SubjectApplicationEntity){
    let selectedApplication = this.listAllApplications.find((_v,k) => _v.id == app.applicationId);
    this.showDesktopOptions = selectedApplication?.applicationType != this.listApplicationWebType;
    if (this.showDesktopOptions) {
      this.dataForm.controls['connectionType'].setValidators([Validators.required]);
      this.dataForm.controls['hostName'].setValidators([Validators.required, Validators.minLength(10)]);
    }
    else {
      this.dataForm.controls['connectionType'].clearValidators();
      this.dataForm.controls['hostName'].clearValidators();
    }
    this.dataForm.controls['connectionType2'].setValue(app.connectionMethod)
  }

  // Update subject's application information
  async onApplicationRowEditSave(application: SubjectApplicationEntity){

    if(this.showDesktopOptions ? application.host!="" && application.connectionMethod!="" : true){
      try {
        this.getSubjectAppByIdUseCase.execute({id: application.id!!}).then(
          (data) => {
            const at_attributes: ExtraData[] = [
              { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(data) },
              { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(application) },
            ];
            this.auditTrailService.auditTrailSelectReason(ReasonTypeEnum.PASS_APP_MANAGEMENT, AuditTrailActions.MOD_PSS, ReasonActionTypeEnum.UPDATE, async () => {
              let response = await this.updateSubjectAppByIdUseCase.execute(application);

              if (response) {
                this.messageService.add({
                  severity: 'success',
                  summary: this.translate.instant('content.successTitle'),
                  detail: this.translate.instant('messages.updated_successfully'),
                  life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
                });
              }
            }, at_attributes);
          },
          (e) => {
            // this.loggerService.error(e);
          }
        );
      } catch (e: any) {
        if(e.message!=""){
          this.messageService.add({
            severity: 'error',
            summary: this.translate.instant('content.errorTitle'),
            detail: String(e.message),
            life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
          });
        }
      }
    }else{
      this.messageService.add({
        severity: 'error',
        summary: this.translate.instant('content.errorTitle'),
        detail: this.translate.instant('content.fields_not_valid'),
        life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
      });
    }
  }

  getInitialisationStatus(status: boolean){

  }

  onApplicationRowEditCancel(app: any, index: number){

  }

  onCredentialRowEditInit(app: any){

  }

  /** Update credential */
  async onCredentialRowEditSave(app: SubjectAppCredentialsEntity){
    // this.dataForm.controls['value'].markAllAsTouched();
    if(this.isValid('value')){
      // app.value = this.dataForm.controls['value'].value;
      this.getSubjectAppCredentialsByIdUseCase.execute({id: app.id!!}).then(
        (data) => {
          const at_attributes: ExtraData[] = [
            { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(data) },
            { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(app) },
          ];
          this.auditTrailService.auditTrailSelectReason(ReasonTypeEnum.PASS_APP_MANAGEMENT, AuditTrailActions.MOD_PSS_CRED, ReasonActionTypeEnum.UPDATE, async () => {
            let listSubjectCredentials: SubjectAppCredentialsEntity[] = [];
            listSubjectCredentials.push(app);
            let response = await this.updateSubjectAppCredentialsByIdUseCase.execute(listSubjectCredentials);
            if (response) {
              this.messageService.add({
                severity: 'success',
                summary: this.translate.instant('content.successTitle'),
                detail: this.translate.instant('messages.updated_successfully'),
                life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
              });
            }
          }, at_attributes);
        },
        (e) => {
          // this.loggerService.error(e);
        }
      );
    }else{
      this.messageService.add({
        severity: 'error',
        summary: this.translate.instant('content.errorTitle'),
        detail: this.translate.instant('content.fields_not_valid'),
        life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
      });
    }

  }

  onCredentialRowEditCancel(app: any, index: number){

  }

  // Delete a subject application
  deleteApplication(application: SubjectApplicationEntity){
    this.confirmationService.confirm({
      message: this.translate.instant('messages.message_remove_subject_app') + ' <b>' + application.subjectAppName + '</b>?',
      header: this.translate.instant('confirmation'),
      icon: 'pi pi-exclamation-triangle',
      rejectButtonStyleClass:"p-button-text",
      acceptButtonStyleClass:"ng-confirm-button",
      acceptLabel: this.translate.instant('yes'),
      rejectLabel: this.translate.instant('no'),
      accept: () => {
        this.resetInactivityMonitor();
          this.confirmDeleteApplication(application);
      },
      reject: () => {
        this.resetInactivityMonitor();
      }
    });

    this.confirmDialogTimeoutLimit = (this.localStorageService.getSessionSettings()?.timeoutMessages ?? 10) * 1000
    this.startCheckingInactivity = false;
    setTimeout(() => this.startCheckingInactivity = true, 0);
  }

  // Confirm delete application
  async confirmDeleteApplication(app: SubjectApplicationEntity){

    this.cleanMessage();
    try {
      const at_attributes: ExtraData[] = [
        { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(app) },
      ];
      this.auditTrailService.auditTrailSelectReason(ReasonTypeEnum.PASS_APP_MANAGEMENT, AuditTrailActions.DEL_PSS, ReasonActionTypeEnum.DELETE, async () => {
        let response = await this.deleteSubjectAppByIdUseCase.execute({ id: app.id!! });
        if (response.success) {
          this.listSubjectApplications = [...this.listSubjectApplications.filter((_v, k) => _v.id !== app.id)];
          this.listSubjectCredentials = [];
          this.deleteCredentialsExternalDataSource(app.numId!!, app.id!!);
          this.messageService.add({
            severity: 'success',
            summary: this.translate.instant('content.successTitle'),
            life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
          });
        }
      }, at_attributes);
    } catch (e: any) {
      this.messageService.add({
        severity: 'error',
        summary: this.translate.instant('content.errorTitle'),
        life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
      });
    }

    this.deleteApplicationDialog = false;
  }

  deleteCredentialsExternalDataSource(numId: string, subjectAppId: string){
    this.getSubjectCredentialBySubjectAppId.execute({subjectAppId: subjectAppId}).then(
      (data) => {
        const at_attributes: ExtraData[] = [
          { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(data) },
        ];
        this.auditTrailService.auditTrailSelectReason(ReasonTypeEnum.PASS_APP_MANAGEMENT, AuditTrailActions.DEL_PSS_CRED, ReasonActionTypeEnum.DELETE, () => {
          this.deleteCredentialByNumIdAndSubjectAppIdUseCase.execute({numId: numId, subjectAppId: subjectAppId}).subscribe({
            next: (data) => {
              // // this.loggerService.debug(data);
            },
            error: (e) => {
              // this.loggerService.error(e);
              const at_attributes: ExtraData[] = [
                { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
                { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(data) },
              ];
              this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.DEL_PSS_CRED, 0, 'ERROR', '', at_attributes);
            },
            complete: () => {
              // // this.loggerService.debug("Completed");
            }
          });
        }, at_attributes);
      },
      (e) => {
        // this.loggerService.error(e);
      }
    );
  }

  isValid(field: string) {
    var validatorService = new ValidatorService();
    return validatorService.isValidField(this.dataForm, field);
  }

  onMustUpdateChange(event: any)
  {
    if(event.checked){
      this.dataForm.controls['canUserUpdateCred'].setValue(true);
    }
  }

  onCanUserUpdateChange(event: any){
    if(!event.checked){
      this.dataForm.controls['mustUserUpdate'].setValue(false);
    }
  }

  onHide() {
    this.subject = "";
    this.subjectNumId = "";
  }

  onContextMenu(event: any) {
    if(this.selectedSubjects.length > 0){
      if(this.selectedSubjects.length == 1){
        this.items = [
          {
              label: 'Add Application',
              icon: 'pi pi-check-square',
              command: () => { this.onShowAddApplication();}
          },
          {
              label: 'View',
              icon: 'pi pi-eye',
              command: () => { this.onShowViewDetails();}
          }
        ];
      }else if(this.selectedSubjects.length > 1){
        this.items = [
          {
              label: 'Add Application',
              icon: 'pi pi-check-square',
              command: () => { this.onShowAddApplication();}
          }
        ];
      }
      this.cm!.target = event.currentTarget;
      this.cm!.show(event);
    }

  }

  // Convert the list of roles to string
  listOfRolesToString(roles?: RoleEntity[]): string {
    let stringOfRoles: string = "";

    if (roles && roles?.length > 0) {
      stringOfRoles = roles?.map(role => role.name == 'SYSTEM_USER' ? this.translate.instant('role_names.SYSTEM_USER') : role.name).join(', ');
    }

    return stringOfRoles
  }

  /* Search */
  onFilter(event: any, dt: Table) {
    if(!event.filters['birthdate'].value){
      this.rangeDates = null;
      this.formGroup.reset();
    }
  }

  /* Date Range Filter */
  applyDateRangeFilter(dt: Table, field: string) {
    this.rangeDates = this.formGroup.get('date')?.value;
    dt.filter({
      startDate: this.rangeDates ? this.rangeDates[0] : null,
      endDate: this.rangeDates ? this.rangeDates[1] : null
    }, field, 'customDateRange');
  }

  closeConfirmationDialog() {
    this.confirmationService.close();
    this.resetInactivityMonitor();
  }

  isValidDate(dateString: string): boolean {
    const date = new Date(dateString);
    return !isNaN(date.getTime()) && date.toISOString().split('T')[0] != (new Date(0)).toISOString().split('T')[0];
  }

  onHideSidebar() {
    if (this.selectedSubjects.length > 0) {
      this.selectedSubjects = [];
    }
    this.sidebarVisible = false;
    this.listApplications = [];
    this.listSubjectApplications = [];
    this.listSubjectCredentials = [];
    this.listWindows = [];
    this.applicationDetailsReady = false;
    this.dataForm.reset();
  }
}
