import { Component, Input, <PERSON><PERSON><PERSON><PERSON>, OnInit } from "@angular/core";
import { FormControl, FormGroup } from "@angular/forms";
import { TranslateService } from "@ngx-translate/core";
import { ConfirmationService, FilterService, MessageService, TreeNode } from "primeng/api";
import { Table } from "primeng/table";
import { environment } from "src/environments/environment";
import { ExtraData } from "src/verazial-common-frontend/core/general/actionsV2/domain/entity/extra-data.interface";
import { OperationType } from "src/verazial-common-frontend/core/general/assignment/categories/common/enum/operation-type.enum";
import { GeneralSettings } from "src/verazial-common-frontend/core/general/manager/common/models/general-settings.model";
import { NewLocationModel } from "src/verazial-common-frontend/core/general/manager/common/models/new-location.model";
import { GetSettingsByApplicationUseCase } from "src/verazial-common-frontend/core/general/manager/domain/use-cases/get-settings-by-application.use-case";
import { TransferAuthEntity } from "src/verazial-common-frontend/core/general/prisons/domain/entity/transfer-auth/transfer-auth.entity";
import { DeleteTransferAuthByIdUseCase } from "src/verazial-common-frontend/core/general/prisons/domain/use-cases/transfer-auth/delete-transfer-auth-by-id.use-case";
import { GetAllTransferAuthsUseCase } from "src/verazial-common-frontend/core/general/prisons/domain/use-cases/transfer-auth/get-all-transfer-auths.use-case";
import { AccessIdentifier } from "src/verazial-common-frontend/core/models/access-identifier.enum";
import { AuditTrailActions } from "src/verazial-common-frontend/core/models/audit-trail-actions.enum";
import { AuditTrailFields } from "src/verazial-common-frontend/core/models/audit-trail-fields.enum";
import { AuditTrailService, ReasonActionTypeEnum, ReasonTypeEnum } from "src/verazial-common-frontend/core/services/audit-trail.service";
import { CheckPermissionsService } from "src/verazial-common-frontend/core/services/check-permissions-service";
import { ConsoleLoggerService } from "src/verazial-common-frontend/core/services/console-logger.service";
import { LocalStorageService } from "src/verazial-common-frontend/core/services/local-storage.service";
import { OperationStatus } from "src/verazial-common-frontend/core/models/operation-status.interface";
import { Status } from "src/verazial-common-frontend/core/models/status.enum";
import { GetAllSubjectsUseCase } from "src/verazial-common-frontend/core/general/subject/domain/use-cases/get-all-subjects.use-case";
import { SubjectEntity } from "src/verazial-common-frontend/core/general/subject/domain/entity/subject.entity";
import { GetAllUsersUseCase } from "src/verazial-common-frontend/core/general/user/domain/use-cases/get-all-users.use-case";
import { UserEntity } from "src/verazial-common-frontend/core/general/user/domain/entity/user.entity";
import { RoleEntity } from "src/verazial-common-frontend/core/general/common/entity/role.entity";
import { GetAllRolesUseCase } from "src/verazial-common-frontend/core/general/role/domain/use-cases/roles/get-all-roles.use-case";
import { NewLocationsService } from "src/verazial-common-frontend/core/services/new-locations.service";
import { GetKonektorPropertiesUseCase } from "src/verazial-common-frontend/core/general/konektor/domain/use-cases/get-konektor-properties.use-case";
import { AuthStatus } from "src/verazial-common-frontend/core/general/prisons/common/enums/transfer-auth-status.enum";

@Component({
    selector: 'app-transfer-auths-list',
    templateUrl: './transfer-auths-list.component.html',
    styleUrl: './transfer-auths-list.component.css',
    providers: [MessageService, ConfirmationService]
})
export class TransferAuthsListComponent implements OnInit, OnDestroy {

    // Inputs
    @Input() userIsVerified: boolean = false;

    isLoading: boolean = false;

    confirmDialogTimeoutLimit: number = 0;
    startCheckingInactivity: boolean = false;

    canReadAndWrite: boolean = false;
    canReadAndWriteAuth: boolean = false;
    canSeeAllLocations: boolean = false;
    readOnly: boolean = false;
    access_identifier: string = AccessIdentifier.PRISONS_TRANSFER_AUTHORIZATIONS;
    all_locations_idenfitier = AccessIdentifier.GET_ALL_LOCATIONS;
    isDisabledSaveButton: boolean = true;

    listOfTransferAuth: TransferAuthEntity[] = [];
    selectedTransferAuths: TransferAuthEntity[] = [];

    // Create/Update Dialog
    showTransferAuthDialog: boolean = false;
    transferAuth?: TransferAuthEntity;
    operationType!: OperationType;
    opType = OperationType;
    createUpdateButtonTitle: string = this.translateService.instant('save');

    // Execute Dialog
    showExecuteTransferAuthDialog: boolean = false;

    // Options
    listLocations: TreeNode[] = [];
    listLocationsDestiny: TreeNode[] = [];
    selectedLocation?: TreeNode;


    // Table Filters Filter
    filteredValues: any[] = [];
    formDateFilter: FormGroup = new FormGroup({
        date: new FormControl<Date[] | null>(null)
    });
    dateFilterValues = {
        startDate: null,
        endDate: null
    };
    rangeDates: Date[] | null = null;

    // Settings
    managerSettings?: GeneralSettings;

    // Subjects
    listOfAllSubjects: SubjectEntity[] = [];
    // Users
    listOfAllUsers: UserEntity[] = [];
    // Roles
    listOfAllRoles: RoleEntity[] = [];

    AuthStatus = AuthStatus;

    constructor(
        private confirmationService: ConfirmationService,
        private filterService: FilterService,
        private localStorageService: LocalStorageService,
        private translateService: TranslateService,
        private loggerService: ConsoleLoggerService,
        private messageService: MessageService,
        public auditTrailService: AuditTrailService,
        private checkPermissions: CheckPermissionsService,
        private newLocationsService: NewLocationsService,
        private getSettingsByApplicationUseCase: GetSettingsByApplicationUseCase,
        private getAllTransferAuthsUseCase: GetAllTransferAuthsUseCase,
        private deleteTransferAuthByIdUseCase: DeleteTransferAuthByIdUseCase,
        private getAllSubjectsUseCase: GetAllSubjectsUseCase,
        private getAllUsersUseCase: GetAllUsersUseCase,
        private getAllRolesUseCase: GetAllRolesUseCase,
        private getKonektorPropertiesUseCase: GetKonektorPropertiesUseCase,
    ) {
        this.filterService.register('customDateRange', (value: any, filter: any): boolean => {
            if (!filter || (!filter.startDate && !filter.endDate)) {
                return true; // If no filter, show all
            }
            const dateValue = new Date(value).getTime();
            const startDate = filter.startDate ? new Date(filter.startDate).getTime() : null;
            const endDate = filter.endDate ? new Date(filter.endDate).getTime() : null;
            if (startDate && endDate) {
                return dateValue >= startDate && dateValue <= endDate;
            } else if (startDate) {
                return dateValue >= startDate;
            } else if (endDate) {
                return dateValue <= endDate;
            }
            return false;
        });
        this.filterService.register('subjectNames', (value: any, filter: any): boolean => {
            if (!filter) return true; // If no filter provided, show all
            else if (typeof filter === 'string') {
                return this.listOfAllSubjects
                    .filter(subject => (subject.names + ' ' + subject.lastNames).toLowerCase().includes(filter.toLowerCase()))
                    .map(subject => subject.id).includes(value);
            }
            return false;
        });
        this.filterService.register('userNames', (value: any, filter: any): boolean => {
            if (!filter) return true; // If no filter provided, show all
            else if (typeof filter === 'string') {
                return this.listOfAllUsers
                    .filter(subject => (subject.names + ' ' + subject.lastNames).toLowerCase().includes(filter.toLowerCase()))
                    .map(subject => subject.id).includes(value);
            }
            return false;
        });
    }

    ngOnInit(): void {
        this.isLoading = true;
        this.canReadAndWrite = this.checkPermissions.hasReadAndWritePermissions(this.access_identifier);
        this.canSeeAllLocations = this.checkPermissions.hasReadAndWritePermissions(this.all_locations_idenfitier);
        this.getManagerSettings(() => {
            this.getSubjects(() => {
                this.getData();
            });
        });
    }

    ngOnDestroy() {
        // Clean up the timeout if the component is destroyed
        this.closeConfirmationDialog();
        this.auditTrailService.resetInactivityMonitor();
    }

    resetInactivityMonitor() {
        this.startCheckingInactivity = false;
        this.confirmDialogTimeoutLimit = 0;
    }

    getManagerSettings(_callback: Function) {
        this.getSettingsByApplicationUseCase.execute({ applicationName: environment.application }).then(
            (data) => {
                this.managerSettings = data.settings!;
            },
            (e) => {
                this.loggerService.error(e);
                this.managerSettings = this.localStorageService.getSessionSettings()!;
            }
        )
            .finally(() => {
                // this.loggerService.debug(this.managerSettings?.continued1?.newLocations);
                // this.listLocations = this.mapLocations(this.managerSettings?.continued1?.newLocations) || [];
                if (this.managerSettings) {
                    let options = this.managerSettings.continued1?.newLocations!;
                    if (this.canSeeAllLocations)
                        this.listLocations = this.newLocationsService.locationsToTreeSelectOptions(options, false);
                    else {
                        this.getKonektorPropertiesUseCase.execute().subscribe({
                            next: (data) => {
                                const konektorProperties = data;
                                if (data.apiGatewayGrpc) {
                                    this.localStorageService.setApiGatewayURL(data.apiGatewayGrpc);
                                }
                                else {
                                    this.localStorageService.destroyApiGatewayURL();
                                }
                                var location = konektorProperties.locationId;
                                this.listLocations = this.newLocationsService.locationsToTreeSelectOptionsForLocation(options, location!, false);
                            },
                            error: (e) => {
                                this.loggerService.error('Error Getting Konektor Properties:');
                                this.loggerService.error(e);
                            }
                        });
                    }
                    this.listLocationsDestiny = this.newLocationsService.locationsToTreeSelectOptions(options, false);
                }
                _callback();
            });
    }

    getSubjects(_callback: Function) {
        this.getAllUsersUseCase.execute({ offset: 0, limit: (this.managerSettings?.continued1?.inputTextAreaThreshold ?? 10000) }).then(
            (users) => {
                this.listOfAllUsers = users;
            },
            (e) => {
                if (e.message != "") {
                    this.loggerService.error(e.message);
                }
            }
        );
        this.getAllRolesUseCase.execute().then(
            (roles) => {
                this.listOfAllRoles = roles;
            },
            (e) => {
                if (e.message != "") {
                    this.loggerService.error(e.message);
                }
            }
        );
        this.getAllSubjectsUseCase.execute({ offset: 0, limit: (this.managerSettings?.continued1?.inputTextAreaThreshold ?? 10000) }).then(
            (subjects) => {
                this.listOfAllSubjects = subjects;
            },
            (e) => {
                if (e.message != "") {
                    this.loggerService.error(e.message);
                }
            }
        )
            .finally(() => {
                _callback();
            });
    }

    getData() {
        this.getAllTransferAuthsUseCase.execute({}).then(
            (data) => {
                this.loggerService.debug(data)
                this.listOfTransferAuth = data;
            },
            (e) => {
                this.loggerService.error(e);
                const at_attributes: ExtraData[] = [{ name: AuditTrailFields.ERROR, value: JSON.stringify(e) }];
                this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.GET_ALL_TRANSFER_AUTHORIZATIONS, 0, 'ERROR', '', at_attributes);
            }
        )
            .finally(() => {
                this.isLoading = false;
            });
    }

    onNewTransferAuth() {
        this.operationType = OperationType.INSERT;
        this.createUpdateButtonTitle = this.translateService.instant('save');
        this.canReadAndWriteAuth = this.canReadAndWrite;
        this.showTransferAuthDialog = true;
    }

    onEditTransferAuth(transferAuth: TransferAuthEntity) {
        if (!transferAuth) {
            if (this.selectedTransferAuths.length === 1) {
                transferAuth = this.selectedTransferAuths[0];
            }
            else {
                return;
            }
        }
        this.transferAuth = {...transferAuth};
        this.operationType = OperationType.UPDATE;
        this.createUpdateButtonTitle = this.translateService.instant('update');
        this.canReadAndWriteAuth = this.canReadAndWrite && this.getCanReadAndWriteAuth(transferAuth);
        this.showTransferAuthDialog = true;
    }

    onExecuteTransferAuth(transferAuth: TransferAuthEntity) {
        if(!transferAuth) {
            if(this.selectedTransferAuths.length === 1) {
                transferAuth = this.selectedTransferAuths[0];
            } else {
                return;
            }
        }

        this.transferAuth = {...transferAuth};
        this.canReadAndWriteAuth = this.canReadAndWrite && this.getCanReadAndWriteAuth(transferAuth);
        this.showExecuteTransferAuthDialog = true;
    }

    deleteTransferAuth(data: TransferAuthEntity) {
        this.loggerService.debug(data);
        this.confirmationService.confirm({
            message: this.translateService.instant('messages.message_remove') + " <b>" + data.id + `</b>?`,
            header: this.translateService.instant('messages.delete_single_record'),
            icon: 'pi pi-exclamation-triangle',
            acceptIcon: "none",
            rejectIcon: "none",
            rejectButtonStyleClass: "p-button-text",
            acceptButtonStyleClass: "ng-confirm-button",
            acceptLabel: this.translateService.instant('delete'),
            rejectLabel: this.translateService.instant('no'),
            accept: () => {
                this.resetInactivityMonitor();
                const at_attributes: ExtraData[] = [
                    { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(data) },
                ];
                this.auditTrailService.auditTrailSelectReason(ReasonTypeEnum.CONFIG, AuditTrailActions.DEL_TRANSFER_AUTHORIZATION, ReasonActionTypeEnum.DELETE,
                    async () => {
                        await this.deleteTransferAuthById(data);
                        this.messageService.add({
                            severity: 'success',
                            summary: this.translateService.instant('content.successTitle'),
                            detail: this.translateService.instant('messages.success_general'),
                            life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
                        });
                    }, at_attributes);
            },
            reject: () => {
                this.resetInactivityMonitor();
            }
        });

        this.confirmDialogTimeoutLimit = (this.localStorageService.getSessionSettings()?.timeoutMessages ?? 10) * 1000
        this.startCheckingInactivity = false;
        setTimeout(() => this.startCheckingInactivity = true, 0);
    }

    async deleteMultipleTransferAuths() {
        if (this.selectedTransferAuths.length > 0) {
            this.confirmationService.confirm({
                message: `${this.translateService.instant('messages.message_remove')} <b>${this.selectedTransferAuths.length}</b>?`,
                header: this.translateService.instant('messages.delete_multiple_records'),
                icon: 'pi pi-exclamation-triangle',
                acceptIcon: "none",
                rejectIcon: "none",
                rejectButtonStyleClass: "p-button-text",
                acceptButtonStyleClass: "ng-confirm-button",
                acceptLabel: this.translateService.instant('delete'),
                rejectLabel: this.translateService.instant('no'),
                accept: async () => {
                    this.resetInactivityMonitor();
                    const at_attributes: ExtraData[] = [
                        { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(this.selectedTransferAuths) },
                    ];

                    this.auditTrailService.auditTrailSelectReason(
                        ReasonTypeEnum.CONFIG,
                        AuditTrailActions.DEL_TRANSFER_AUTHORIZATION,
                        ReasonActionTypeEnum.DELETE,
                        async () => {
                            this.isLoading = true;
                            try {
                                // Execute all deletions in parallel
                                await Promise.all(this.selectedTransferAuths.map(auth => this.deleteTransferAuthById(auth)));

                                // Success message
                                this.messageService.add({
                                    severity: 'success',
                                    summary: this.translateService.instant('content.successTitle'),
                                    detail: this.translateService.instant('messages.success_general'),
                                    life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
                                });

                                // Clear selected items
                                this.selectedTransferAuths = [];
                            } catch (error: any) {
                                this.messageService.add({
                                    severity: 'error',
                                    summary: this.translateService.instant('content.errorTitle'),
                                    detail: `${this.translateService.instant('messages.error_deleting_transfer_auth')}: ${error.message}`,
                                    life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
                                });

                                this.loggerService.error('Error deleting transfer authorizations:');
                                this.loggerService.error(error);
                            } finally {
                                this.isLoading = false;
                            }
                        },
                        at_attributes
                    );
                },
                reject: () => {
                    this.resetInactivityMonitor();
                }
            });

            this.confirmDialogTimeoutLimit = (this.localStorageService.getSessionSettings()?.timeoutMessages ?? 10) * 1000
            this.startCheckingInactivity = false;
            setTimeout(() => this.startCheckingInactivity = true, 0);
        }
    }

    deleteTransferAuthById(data: TransferAuthEntity): Promise<void> {
        return new Promise(async (resolve, reject) => {
            const id = data.id!;
            try {
                await this.deleteTransferAuthByIdUseCase.execute({ id });

                // Remove deleted item from the list
                this.listOfTransferAuth = this.listOfTransferAuth.filter(item => item.id !== id);

                resolve();
            } catch (e: any) {
                this.messageService.add({
                    severity: 'error',
                    summary: `${this.translateService.instant("titles.error_operation")}: ${id}`,
                    detail: `${this.translateService.instant("messages.error_deleting_transfer_auth")}: ${e.message}`,
                    life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
                });

                this.loggerService.error(e);

                const at_attributes: ExtraData[] = [
                    { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
                    { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(data) },
                ];
                this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.DEL_TRANSFER_AUTHORIZATION, 0, 'ERROR', '', at_attributes);

                reject(e);
            }
        });
    }

    operationStatus(event: OperationStatus) {
        if (event.status == Status.SUCCESS) {
            this.showTransferAuthDialog = false;
            if (event.message != 'CLOSE') {
                this.messageService.add({
                    severity: 'success', summary: this.translateService.instant("titles.success_operation"), detail: this.translateService.instant("messages.success_general"),
                    life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
                });
                this.getData();
            }
        } else {
            this.messageService.add({
                severity: 'error', summary: this.translateService.instant("titles.error_operation"), detail: `${event.message}`,
                life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
            });
        }
    }

    operationStatusExecute(event: OperationStatus) {
        if (event.status == Status.SUCCESS) {
            this.showExecuteTransferAuthDialog = false;
            if (event.message != 'CLOSE') {
                this.messageService.add({
                    severity: 'success', summary: this.translateService.instant("titles.success_operation"), detail: this.translateService.instant("messages.success_general"),
                    life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
                });
                this.getData();
            }
        } else {
            this.messageService.add({
                severity: 'error', summary: this.translateService.instant("titles.error_operation"), detail: `${event.message}`,
                life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
            });
        }

    }

    /* Supporting Functions */

    getSubjectNamesById(id: string): string {
        let subject = this.listOfAllSubjects.find((subject) => subject.id === id);
        return subject ? subject.names + ' ' + subject.lastNames : '';
    }

    getUserNamesById(id: string): string {
        let user = this.listOfAllUsers.find((user) => user.id === id);
        return user ? user.names + ' ' + user.lastNames : '';
    }

    mapLocations(nodes?: NewLocationModel[]): TreeNode[] {
        return nodes?.map((node) => ({
            key: node.id,
            label: node.name,
            children: this.mapLocations(node.children) // Recursive call
        })) || [];
    }

    getLocationPath(locationId: string): string {
        return this.newLocationsService.getNameAndTree(locationId, this.listLocationsDestiny)!;
    }

    findLocationPath(nodes?: TreeNode[], targetId?: string, path: string[] = []): string[] | null {
        for (let node of nodes || []) {
            const currentPath: string[] = [...path, node.label!]; // Add current node to path
            if (node.key === targetId) {
                return currentPath; // Found the target node
            }
            const childPath = this.findLocationPath(node.children, targetId, currentPath);
            if (childPath) {
                return childPath; // Found in children
            }
        }
        return null; // Not found
    }

    getCanReadAndWriteAuth(transferAuth: TransferAuthEntity): boolean {
        return this.newLocationsService.containsKey(transferAuth.originLocationId!, this.listLocations)
    }

    onFilter(event: any, dt: Table) {
        this.filteredValues = event.filteredValue;
        if (
            !event.filters['authRegistrationDate'].value &&
            !event.filters['authUserSignatureDate'].value &&
            !event.filters['authExpirationDate'].value &&
            !event.filters['plannedDepartureDateTime'].value &&
            !event.filters['plannedArrivalDateTime'].value &&
            !event.filters['cancelUserSignatureDate'].value &&
            !event.filters['createdAt'].value &&
            !event.filters['updatedAt'].value
        ) {
            this.rangeDates = null;
            this.formDateFilter.reset();
        }
    }

    applyDateRangeFilter(dt: Table, field: string) {
        this.rangeDates = this.formDateFilter.get('date')?.value;
        dt.filter({
            startDate: this.rangeDates ? this.rangeDates[0] : null,
            endDate: this.rangeDates ? this.rangeDates[1] : null
        }, field, 'customDateRange');
    }

    isValidDate(dateString: string): boolean {
        const date = new Date(dateString);
        return !isNaN(date.getTime()) && date.toISOString().split('T')[0] != (new Date(0)).toISOString().split('T')[0];
    }

    closeConfirmationDialog() {
        this.confirmationService.close();
        this.resetInactivityMonitor();
    }

    enableEdit(transferAuth: TransferAuthEntity): boolean {
        let isInProgress = transferAuth?.status == AuthStatus.IN_PROGRESS;
        let hasAuthSignature = transferAuth?.authUserNumId != '' && transferAuth?.authUserNumId != null && transferAuth?.authUserNumId != undefined;
        let hasCancelSignature = transferAuth?.cancelUserNumId != '' && transferAuth?.cancelUserNumId != null && transferAuth?.cancelUserNumId != undefined;
        let isCompleted = transferAuth?.isCompleted;
        return !isInProgress && !hasAuthSignature && !hasCancelSignature && !isCompleted;
    }
}