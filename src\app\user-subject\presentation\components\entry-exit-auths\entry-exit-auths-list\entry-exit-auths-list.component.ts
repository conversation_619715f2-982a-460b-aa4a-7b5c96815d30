import { Component, Input, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>nit, SimpleChang<PERSON> } from "@angular/core";
import { FormControl, FormGroup } from "@angular/forms";
import { TranslateService } from "@ngx-translate/core";
import { ConfirmationService, FilterService, MessageService } from "primeng/api";
import { Table } from "primeng/table";
import { environment } from "src/environments/environment";
import { ExtraData } from "src/verazial-common-frontend/core/general/actionsV2/domain/entity/extra-data.interface";
import { OperationType } from "src/verazial-common-frontend/core/general/assignment/categories/common/enum/operation-type.enum";
import { RoleEntity } from "src/verazial-common-frontend/core/general/common/entity/role.entity";
import { GeneralSettings } from "src/verazial-common-frontend/core/general/manager/common/models/general-settings.model";
import { GetSettingsByApplicationUseCase } from "src/verazial-common-frontend/core/general/manager/domain/use-cases/get-settings-by-application.use-case";
import { EntryExitAuthType } from "src/verazial-common-frontend/core/general/prisons/common/enums/entry-exit-auth-type.enum";
import { AuthStatus } from "src/verazial-common-frontend/core/general/prisons/common/enums/transfer-auth-status.enum";
import { EntryExitAuthEntity } from "src/verazial-common-frontend/core/general/prisons/domain/entity/entry-exit-auth/entry-exit-auth.entity";
import { DeleteEntryExitAuthByIdUseCase } from "src/verazial-common-frontend/core/general/prisons/domain/use-cases/entry-exit-auth/delete-entry-exit-auth-by-id.use-case";
import { GetEntryExitAuthsBySubjectIdUseCase } from "src/verazial-common-frontend/core/general/prisons/domain/use-cases/entry-exit-auth/get-entry-exit-auths-by-subject-id.use-case";
import { GetAllRolesUseCase } from "src/verazial-common-frontend/core/general/role/domain/use-cases/roles/get-all-roles.use-case";
import { SubjectEntity } from "src/verazial-common-frontend/core/general/subject/domain/entity/subject.entity";
import { GetAllSubjectsUseCase } from "src/verazial-common-frontend/core/general/subject/domain/use-cases/get-all-subjects.use-case";
import { UserEntity } from "src/verazial-common-frontend/core/general/user/domain/entity/user.entity";
import { GetAllUsersUseCase } from "src/verazial-common-frontend/core/general/user/domain/use-cases/get-all-users.use-case";
import { AccessIdentifier } from "src/verazial-common-frontend/core/models/access-identifier.enum";
import { AuditTrailActions } from "src/verazial-common-frontend/core/models/audit-trail-actions.enum";
import { AuditTrailFields } from "src/verazial-common-frontend/core/models/audit-trail-fields.enum";
import { OperationStatus } from "src/verazial-common-frontend/core/models/operation-status.interface";
import { Status } from "src/verazial-common-frontend/core/models/status.enum";
import { AuditTrailService, ReasonActionTypeEnum, ReasonTypeEnum } from "src/verazial-common-frontend/core/services/audit-trail.service";
import { CheckPermissionsService } from "src/verazial-common-frontend/core/services/check-permissions-service";
import { ConsoleLoggerService } from "src/verazial-common-frontend/core/services/console-logger.service";
import { EntryExitService } from "src/verazial-common-frontend/core/services/entry-exit.service";
import { LocalStorageService } from "src/verazial-common-frontend/core/services/local-storage.service";

@Component({
    selector: 'app-entry-exit-auths-list',
    templateUrl: './entry-exit-auths-list.component.html',
    styleUrl: './entry-exit-auths-list.component.css',
    providers: [MessageService, ConfirmationService]
})
export class EntryExitAuthsListComponent implements OnInit, OnChanges, OnDestroy {
    // Inputs
    @Input() readAndWritePermissions: boolean = false;
    @Input() userSubject?: SubjectEntity;
    @Input() userIsVerified: boolean = false;
    @Input() isPrisoner: boolean = false;

    isLoading: boolean = false;

    confirmDialogTimeoutLimit: number = 0;
    startCheckingInactivity: boolean = false;

    canReadAndWrite: boolean = false;
    readOnly: boolean = false;
    access_identifier: string = AccessIdentifier.PRISONS_ENTRY_EXIT_AUTHORIZATIONS;
    isDisabledSaveButton: boolean = true;

    listOfEntryExitAuths: EntryExitAuthEntity[] = [];
    selectedEntryExitAuths: EntryExitAuthEntity[] = [];

    // Create/Update Dialog
    showEntryExitAuthDialog: boolean = false;
    entryExitAuth?: EntryExitAuthEntity;
    operationType!: OperationType;
    opType = OperationType;
    createUpdateButtonTitle: string = this.translateService.instant('save');

    // Options
    authReasonOptions: string[] = [];
    authReasonsParameter: string = 'entry-exit-auth-reasons';
    authTypeOptions = [
        { label: 'content.entry', value: EntryExitAuthType.ENTRY },
        { label: 'content.exit', value: EntryExitAuthType.EXIT },
        { label: 'prisons_tab.definitive_exit', value: EntryExitAuthType.DFN_EXIT },
    ]

    // Table Filters Filter
    filteredValues: any[] = [];
    formDateFilter: FormGroup = new FormGroup({
        date: new FormControl<Date[] | null>(null)
    });
    dateFilterValues = {
        startDate: null,
        endDate: null
    };
    rangeDates: Date[] | null = null;

    // Settings
    managerSettings?: GeneralSettings;

    // Subjects
    listOfAllSubjects: SubjectEntity[] = [];
    // Users
    listOfAllUsers: UserEntity[] = [];
    // Roles
    listOfAllRoles: RoleEntity[] = [];

    constructor(
        private confirmationService: ConfirmationService,
        private filterService: FilterService,
        private localStorageService: LocalStorageService,
        private translateService: TranslateService,
        private loggerService: ConsoleLoggerService,
        private messageService: MessageService,
        public auditTrailService: AuditTrailService,
        private checkPermissions: CheckPermissionsService,
        private getSettingsByApplicationUseCase: GetSettingsByApplicationUseCase,
        private getAllSubjectsUseCase: GetAllSubjectsUseCase,
        private getAllUsersUseCase: GetAllUsersUseCase,
        private getAllRolesUseCase: GetAllRolesUseCase,
        private getEntryExitAuthsBySubjectIdUseCase: GetEntryExitAuthsBySubjectIdUseCase,
        private deleteEntryExitAuthByIdUseCase: DeleteEntryExitAuthByIdUseCase,
        private entryExitService: EntryExitService
    ) {
        this.filterService.register('customDateRange', (value: any, filter: any): boolean => {
            if (!filter || (!filter.startDate && !filter.endDate)) {
                return true; // If no filter, show all
            }
            const dateValue = new Date(value).getTime();
            const startDate = filter.startDate ? new Date(filter.startDate).getTime() : null;
            const endDate = filter.endDate ? new Date(filter.endDate).getTime() : null;
            if (startDate && endDate) {
                return dateValue >= startDate && dateValue <= endDate;
            } else if (startDate) {
                return dateValue >= startDate;
            } else if (endDate) {
                return dateValue <= endDate;
            }
            return false;
        });
        this.filterService.register('subjectNames', (value: any, filter: any): boolean => {
            if (!filter) return true; // If no filter provided, show all
            else if (typeof filter === 'string') {
                return this.listOfAllSubjects
                    .filter(subject => (subject.names + ' ' + subject.lastNames).toLowerCase().includes(filter.toLowerCase()))
                    .map(subject => subject.id).includes(value);
            }
            return false;
        });
        this.filterService.register('userNames', (value: any, filter: any): boolean => {
            if (!filter) return true; // If no filter provided, show all
            else if (typeof filter === 'string') {
                return this.listOfAllUsers
                    .filter(subject => (subject.names + ' ' + subject.lastNames).toLowerCase().includes(filter.toLowerCase()))
                    .map(subject => subject.id).includes(value);
            }
            return false;
        });
    }

    ngOnInit() {
        this.isLoading = true;
        this.setTypeOptions();
        this.canReadAndWrite = this.checkPermissions.hasReadAndWritePermissions(this.access_identifier) && this.readAndWritePermissions;
        this.getManagerSettings(() => {
            this.getSubjects(() => {
                this.getData();
            });
        });
    }

    ngOnDestroy() {
        // Clean up the timeout if the component is destroyed
        this.closeConfirmationDialog();
        this.auditTrailService.resetInactivityMonitor();
    }

    resetInactivityMonitor() {
        this.startCheckingInactivity = false;
        this.confirmDialogTimeoutLimit = 0;
    }

    getData() {
        this.getEntryExitAuthsBySubjectIdUseCase.execute({ subjectId: this.userSubject?.id! }).then(
            (data) => {
                this.listOfEntryExitAuths = data;
                this.entryExitService.setSubjectEntryExitAuths(data);
            },
            (e) => {
                this.loggerService.error(e);
                const at_attributes: ExtraData[] = [{ name: AuditTrailFields.ERROR, value: JSON.stringify(e) }];
                this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.GET_ENTRY_EXIT_AUTHORIZATION_BY_SUBJECT_ID, 0, 'ERROR', '', at_attributes);
            }
        )
        .finally(() => {
            this.isLoading = false;
        });
    }

    getManagerSettings(_callback: Function) {
        this.getSettingsByApplicationUseCase.execute({ applicationName: environment.application }).then(
            (data) => {
                this.managerSettings = data.settings!;
            },
            (e) => {
                this.loggerService.error(e);
                this.managerSettings = this.localStorageService.getSessionSettings()!;
            }
        )
        .finally(() => {
            _callback();
        });
    }

    getSubjects(_callback: Function) {
        this.getAllSubjectsUseCase.execute({ offset: 0, limit: 10000 }).then(
            (subjects) => {
                this.listOfAllSubjects = subjects;
            },
            (e) => {
                this.loggerService.error(e);
            }
        )
        .finally(() => {
            this.getAllUsersUseCase.execute({ offset: 0, limit: 10000 }).then(
                (users) => {
                    this.listOfAllUsers = users;
                },
                (e) => {
                    this.loggerService.error(e);
                }
            )
            .finally(() => {
                this.getAllRolesUseCase.execute().then(
                    (roles) => {
                        this.listOfAllRoles = roles;
                    },
                    (e) => {
                        this.loggerService.error(e);
                    }
                )
                .finally(() => {
                    _callback();
                });
            });
        });
    }

    ngOnChanges(changes: SimpleChanges): void {
        if (changes['userSubject'] && changes['userSubject'].currentValue) {
            this.ngOnInit();
        }
        if (changes['readAndWritePermissions'] && changes['readAndWritePermissions'].currentValue) {
            this.canReadAndWrite = this.checkPermissions.hasReadAndWritePermissions(this.access_identifier) && this.readAndWritePermissions;
        }
        this.setTypeOptions();
    }

    setTypeOptions() {
        if (this.isPrisoner) {
            this.authTypeOptions = [
                // { label: 'content.entry', value: EntryExitAuthType.ENTRY },
                { label: 'content.exit', value: EntryExitAuthType.EXIT },
                { label: 'prisons_tab.definitive_exit', value: EntryExitAuthType.DFN_EXIT },
            ]
        }
        else {
            this.authTypeOptions = [
                { label: 'content.entry', value: EntryExitAuthType.ENTRY },
                { label: 'content.exit', value: EntryExitAuthType.EXIT },
                // { label: 'prisons_tab.definitive_exit', value: EntryExitAuthType.DFN_EXIT },
            ]
        }
    }

    onNewEntryExitAuth() {
        this.entryExitAuth = new EntryExitAuthEntity();
        this.operationType = OperationType.INSERT;
        this.createUpdateButtonTitle = this.translateService.instant('save');
        this.showEntryExitAuthDialog = true;
    }

    onEditEntryExitAuth(entryExitAuth: EntryExitAuthEntity) {
        if (!entryExitAuth) {
            if (this.selectedEntryExitAuths.length === 1) {
                entryExitAuth = this.selectedEntryExitAuths[0];
            }
            else {
                return;
            }
        }
        this.entryExitAuth = {...entryExitAuth};
        this.operationType = OperationType.UPDATE;
        this.createUpdateButtonTitle = this.translateService.instant('update');
        this.showEntryExitAuthDialog = true;
    }

    deleteEntryExitAuth(data: EntryExitAuthEntity) {
        this.loggerService.debug(data);
        this.confirmationService.confirm({
            message: this.translateService.instant('messages.message_remove') + " <b>" + data.id + `</b>?`,
            header: this.translateService.instant('messages.delete_single_record'),
            icon: 'pi pi-exclamation-triangle',
            acceptIcon: "none",
            rejectIcon: "none",
            rejectButtonStyleClass: "p-button-text",
            acceptButtonStyleClass: "ng-confirm-button",
            acceptLabel: this.translateService.instant('delete'),
            rejectLabel: this.translateService.instant('no'),
            accept: () => {
                this.resetInactivityMonitor();
                const at_attributes: ExtraData[] = [
                    { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(data) },
                ];
                this.auditTrailService.auditTrailSelectReason(ReasonTypeEnum.CONFIG, AuditTrailActions.DEL_ENTRY_EXIT_AUTHORIZATION, ReasonActionTypeEnum.DELETE,
                    async () => {
                        await this.deleteEntryExitAuthById(data);
                        this.messageService.add({
                            severity: 'success',
                            summary: this.translateService.instant('content.successTitle'),
                            detail: this.translateService.instant('messages.success_general'),
                            life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
                        });
                    }, at_attributes);
            },
            reject: () => {
                this.resetInactivityMonitor();
            }
        });

        this.confirmDialogTimeoutLimit = (this.localStorageService.getSessionSettings()?.timeoutMessages ?? 10) * 1000
        this.startCheckingInactivity = false;
        setTimeout(() => this.startCheckingInactivity = true, 0);
    }

    async deleteMultipleEntryExitAuths() {
        if (this.selectedEntryExitAuths.length > 0) {
            this.confirmationService.confirm({
                message: `${this.translateService.instant('messages.message_remove')} <b>${this.selectedEntryExitAuths.length}</b>?`,
                header: this.translateService.instant('messages.delete_multiple_records'),
                icon: 'pi pi-exclamation-triangle',
                acceptIcon: "none",
                rejectIcon: "none",
                rejectButtonStyleClass: "p-button-text",
                acceptButtonStyleClass: "ng-confirm-button",
                acceptLabel: this.translateService.instant('delete'),
                rejectLabel: this.translateService.instant('no'),
                accept: async () => {
                    this.resetInactivityMonitor();
                    const at_attributes: ExtraData[] = [
                        { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(this.selectedEntryExitAuths) },
                    ];

                    this.auditTrailService.auditTrailSelectReason(
                        ReasonTypeEnum.CONFIG,
                        AuditTrailActions.DEL_ENTRY_EXIT_AUTHORIZATION,
                        ReasonActionTypeEnum.DELETE,
                        async () => {
                            this.isLoading = true;
                            try {
                                // Run all deletions in parallel
                                await Promise.all(this.selectedEntryExitAuths.map(auth => this.deleteEntryExitAuthById(auth)));

                                // Success notification
                                this.messageService.add({
                                    severity: 'success',
                                    summary: this.translateService.instant('content.successTitle'),
                                    detail: this.translateService.instant('messages.success_general'),
                                    life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
                                });

                                // Clear selection
                                this.selectedEntryExitAuths = [];
                            } catch (error: any) {
                                this.messageService.add({
                                    severity: 'error',
                                    summary: this.translateService.instant('content.errorTitle'),
                                    detail: `${this.translateService.instant('messages.error_deleting_entry_exit_auth')}: ${error.message}`,
                                    life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
                                });

                                this.loggerService.error('Error deleting entry/exit authorizations:');
                                this.loggerService.error(error);
                            } finally {
                                this.isLoading = false;
                            }
                        },
                        at_attributes
                    );
                },
                reject: () => {
                    this.resetInactivityMonitor();
                }
            });

            this.confirmDialogTimeoutLimit = (this.localStorageService.getSessionSettings()?.timeoutMessages ?? 10) * 1000
            this.startCheckingInactivity = false;
            setTimeout(() => this.startCheckingInactivity = true, 0);
        }
    }

    deleteEntryExitAuthById(data: EntryExitAuthEntity): Promise<void> {
        return new Promise(async (resolve, reject) => {
            const id = data.id!;
            try {
                await this.deleteEntryExitAuthByIdUseCase.execute({ id });

                // Remove from list
                this.listOfEntryExitAuths = this.listOfEntryExitAuths.filter(item => item.id !== id);
                this.entryExitService.setSubjectEntryExitAuths(this.listOfEntryExitAuths);

                resolve();
            } catch (e: any) {
                this.messageService.add({
                    severity: 'error',
                    summary: `${this.translateService.instant("titles.error_operation")}: ${id}`,
                    detail: `${this.translateService.instant("messages.error_deleting_entry_exit_auth")}: ${e.message}`,
                    life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
                });

                this.loggerService.error(e);

                const at_attributes: ExtraData[] = [
                    { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
                    { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(data) },
                ];
                this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.DEL_ENTRY_EXIT_AUTHORIZATION, 0, 'ERROR', '', at_attributes);

                reject(e);
            }
        });
    }

    operationStatus(event: OperationStatus) {
        if (event.status == Status.SUCCESS) {
            this.showEntryExitAuthDialog = false;
            if (event.message != 'CLOSE') {
                this.messageService.add({
                    severity: 'success', summary: this.translateService.instant("titles.success_operation"), detail: this.translateService.instant("messages.success_general"),
                    life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
                });
                this.getData();
            }
        } else {
            this.messageService.add({
                severity: 'error', summary: this.translateService.instant("titles.error_operation"), detail: `${event.message}`,
                life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
            });
        }
    }

    /* Supporting Functions */

    getSubjectNamesById(id: string): string {
        let subject = this.listOfAllSubjects.find((subject) => subject.id === id);
        return subject ? subject.names + ' ' + subject.lastNames : '';
    }

    getUserNamesById(id: string): string {
        let user = this.listOfAllUsers.find((user) => user.id === id);
        return user ? user.names + ' ' + user.lastNames : '';
    }

    getAuthType(authType: EntryExitAuthType): string {
        return this.authTypeOptions.find((option) => option.value === authType)?.label ?? '';
    }

    onFilter(event: any, dt: Table) {
        this.filteredValues = event.filteredValue;
        if (
            !event.filters['authRegistrationDate'].value &&
            !event.filters['authUserSignatureDate'].value &&
            !event.filters['authStartDateTime'].value &&
            !event.filters['authEndDateTime'].value &&
            // !event.filters['actualStartDateTime'].value &&
            // !event.filters['actualEndDateTime'].value &&
            !event.filters['createdAt'].value &&
            !event.filters['updatedAt'].value
        ) {
            this.rangeDates = null;
            this.formDateFilter.reset();
        }
    }

    applyDateRangeFilter(dt: Table, field: string) {
        this.rangeDates = this.formDateFilter.get('date')?.value;
        dt.filter({
            startDate: this.rangeDates ? this.rangeDates[0] : null,
            endDate: this.rangeDates ? this.rangeDates[1] : null
        }, field, 'customDateRange');
    }

    isValidDate(dateString: string): boolean {
        const date = new Date(dateString);
        return !isNaN(date.getTime()) && date.toISOString().split('T')[0] != (new Date(0)).toISOString().split('T')[0];
    }

    closeConfirmationDialog() {
        this.confirmationService.close();
        this.resetInactivityMonitor();
    }

    enableEdit(auth: EntryExitAuthEntity): boolean {
        let isInProgress = auth?.status == AuthStatus.IN_PROGRESS;
        let hasAuthSignature = auth?.authUserNumId != '' && auth?.authUserNumId != null && auth?.authUserNumId != undefined;
        let isCompleted = auth?.isCompleted;
        return !isInProgress && !hasAuthSignature && !isCompleted;
    }
}