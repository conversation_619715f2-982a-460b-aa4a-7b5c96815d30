<p-toast />
<p-confirmDialog />
<app-loading-spinner [isLoading]="isLoading"></app-loading-spinner>
<app-inactivity-monitor
    [inactivityTimeoutLimit]="confirmDialogTimeoutLimit"
    [startChecking]="startCheckingInactivity"
    (expired)="closeConfirmationDialog()"
></app-inactivity-monitor>
<app-inactivity-monitor
    [inactivityTimeoutLimit]="auditTrailService.confirmDialogTimeoutLimit"
    [startChecking]="auditTrailService.startCheckingInactivity"
    (expired)="auditTrailService.closeConfirmationDialog()"
></app-inactivity-monitor>
<app-applications-list
    [canReadAndWrite]="canReadAndWrite"
    [listAppRegistries]="listAppRegistries"
    (onAdd)="onAdd($event)"
    (onEdit)="onEdit($event)"
    (onDelete)="onDelete($event)"
></app-applications-list>