import { Component, Input, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, On<PERSON>nit, SimpleChanges } from "@angular/core";
import { FormBuilder, FormControl, FormGroup, Validators } from "@angular/forms";
import { TranslateService } from "@ngx-translate/core";
import { Table } from 'primeng/table';
import { ConfirmationService, FilterService, MenuItem, MessageService } from "primeng/api";
import { BelongingEntity, BelongingItemEntity } from "src/verazial-common-frontend/core/general/prisons/domain/entity/belongings.entity";
import { GenericKeyValue } from "src/verazial-common-frontend/core/models/key-value.interface";
import { AuditTrailService, ReasonActionTypeEnum, ReasonTypeEnum } from "src/verazial-common-frontend/core/services/audit-trail.service";
import { CheckPermissionsService } from "src/verazial-common-frontend/core/services/check-permissions-service";
import { ConsoleLoggerService } from "src/verazial-common-frontend/core/services/console-logger.service";
import { LocalStorageService } from "src/verazial-common-frontend/core/services/local-storage.service";
import { ValidatorService } from "src/verazial-common-frontend/modules/shared/services/validator.service";
import { AccessIdentifier } from "src/verazial-common-frontend/core/models/access-identifier.enum";
import { SubjectEntity } from "src/verazial-common-frontend/core/general/subject/domain/entity/subject.entity";
import { SaveBelongingUseCase } from "src/verazial-common-frontend/core/general/prisons/domain/use-cases/belongings/save-belonging.use-case";
import { ExtraData } from "src/verazial-common-frontend/core/general/actionsV2/domain/entity/extra-data.interface";
import { AuditTrailFields } from "src/verazial-common-frontend/core/models/audit-trail-fields.enum";
import { AuditTrailActions } from "src/verazial-common-frontend/core/models/audit-trail-actions.enum";
import { UpdateBelongingUseCase } from "src/verazial-common-frontend/core/general/prisons/domain/use-cases/belongings/update-belonging.use-case";
import { GetBelongingsBySubjectIdUseCase } from "src/verazial-common-frontend/core/general/prisons/domain/use-cases/belongings/get-belongings-by-subject-id.use-case";
import { DeleteBelongingByIdUseCase } from "src/verazial-common-frontend/core/general/prisons/domain/use-cases/belongings/delete-belonging-by-id.use-case";
import { StaticResourceEntity } from "src/verazial-common-frontend/core/general/storage/domain/entity/static-resource.entity";
import { DeleteStaticResourcesBySubjectIdAndNameAndNumberUseCase } from "src/verazial-common-frontend/core/general/storage/domain/use-cases/delete-static-resource-by-subject-id-and-name-and-number.use-case";
import { GetStaticResourcesBySubjectIdAndNameUseCase } from "src/verazial-common-frontend/core/general/storage/domain/use-cases/get-static-resources-by-subject-id-and-name.use-case";
import { CreateStaticResourceEntity } from "src/verazial-common-frontend/core/general/storage/domain/entity/create-static-resource.entity";
import { CreateStaticResourceUseCase } from "src/verazial-common-frontend/core/general/storage/domain/use-cases/create-static-resource.use-case";
import { GeneralSettings } from "src/verazial-common-frontend/core/general/manager/common/models/general-settings.model";
import { KonektorPropertiesEntity } from "src/verazial-common-frontend/core/general/konektor/domain/entity/konektor-properties.entity";
import { GetKonektorPropertiesUseCase } from "src/verazial-common-frontend/core/general/konektor/domain/use-cases/get-konektor-properties.use-case";
import { WidgetResult } from "src/verazial-common-frontend/modules/shared/models/widget-response.model";
import { environment } from "src/environments/environment";
import { OperationType } from "src/verazial-common-frontend/core/general/assignment/categories/common/enum/operation-type.enum";
import { AttributeData } from "src/verazial-common-frontend/core/general/flow/common/models/attribute-data.model";
import { GetSubjectByNumIdUseCase } from "src/verazial-common-frontend/core/general/subject/domain/use-cases/get-subject-by-num-id.use-case";
import { BioSignatureResult } from "src/verazial-common-frontend/modules/shared/components/bio-signatures/bio-signatures/bio-signatures.component";
import { LanguageRecordModel, TranslationGroup, TranslationModel } from "src/verazial-common-frontend/core/general/manager/common/models/translation.model";

@Component({
    selector: 'app-belongings-list',
    templateUrl: './belongings-list.component.html',
    styleUrl: './belongings-list.component.css'
})
export class BelongingsListComponent implements OnInit, OnChanges, OnDestroy {

    // Inputs
    @Input() userSubject: SubjectEntity | undefined;
    @Input() userIsVerified: boolean = false;

    // Subject Info
    image: string = '';
    imagePlaceholder: string = "verazial-common-frontend/assets/images/all/UserPic.svg";
    userReceptionImage: string = '';
    userReturnImage: string = '';

    // Users Info

    // Flags
    isLoading: boolean = false;
    editEnabled: boolean = true;

    // Page State
    confirmDialogTimeoutLimit: number = 0;
    startCheckingInactivity: boolean = false;
    language: string = this.localStorageService.getLanguage() || environment.defaultLanguage;

    // Access
    canReadAndWrite: boolean = false;
    readOnly: boolean = false;
    access_identifier = AccessIdentifier.PRISONS_BELONGINGS;
    isDisableSaveButton: boolean = true;

    // Data
    listOfBelongings: BelongingEntity[] = [];
    selectedBelongings: BelongingEntity[] = [];

    // Table Filters
    filteredValues: any[] = [];
    // Date Range Filter
    formGroup: FormGroup = new FormGroup({
        date: new FormControl<Date[] | null>(null)
    });
    dateFilterValues = {
        startDate: null,
        endDate: null
    };
    rangeDates: Date[] | null = null;

    // CRUD
    showBelongingDialog: boolean = false;
    isNew: boolean = true;
    belonging: BelongingEntity | undefined;
    belongingsList: BelongingItemEntity[] = [];
    belongingTypeSelected: GenericKeyValue | undefined;
    belongingTypeOptions: GenericKeyValue[] = [];
    belongingTypeParameter = 'belongings';
    items?: MenuItem[];
    activeIndex: number = 0;
    operationType: OperationType = OperationType.INSERT;
    opType = OperationType;
    minDate: Date | undefined;
    // New Belonging
    public form: FormGroup = this.fb.group({
        comments: [''],
        stepOptions: ['0'],
    });
    // New Belonging Item
    public belongingItemForm: FormGroup = this.fb.group({
        belongingType: ['', [Validators.required]],
        belongingDescription: ['', [Validators.required]],
        comments: [''],
    });
    public rowForm: FormGroup = this.fb.group({
        type: ['', [Validators.required]],
        description: ['', [Validators.required]],
        comments: [''],
    });
    // Edit Belonging
    clonedBelongingItem: { [s: string]: BelongingItemEntity } = {};
    // Belonging Phtos
    showBelongingPhotosDialog: boolean = false;
    showNewBelongingPhotoDialog: boolean = false;
    belongingsPhotoList: StaticResourceEntity[] = [];
    belongingsPhotoName: string = 'belongings-photo-';
    assetRoute: string = 'verazial-common-frontend/assets/images/';
    selectedImage: StaticResourceEntity | undefined;
    openFullImageDialog: boolean = false;
    responsiveOptions: any[] | undefined;
    selectedCurrentResult: StaticResourceEntity | undefined;
    // Belonging Signatures
    showReceptionButton: boolean = false;
    showReceptionInfo: boolean = false;
    userReception?: SubjectEntity;
    restrictReceptionRoles: string[] = [];
    segmentedSearchReceptionRole: string = '';
    receptionSignatureData?: BioSignatureResult;
    showReturnButton: boolean = false;
    showReturnInfo: boolean = false;
    userReturn?: SubjectEntity;
    restrictReturnRoles: string[] = [];
    segmentedSearchReturnRole: string = '';
    returnSignatureData?: BioSignatureResult;

    // Widget Functions
    widgetUrl: string = '';
    managerSettings?: GeneralSettings;
    konektorProperties?: KonektorPropertiesEntity;
    segmentedSearchAttributes: { name: string, value: string, secondSearch?: string }[] = [];
    userNumId: string = '';
    subjectNumId: string = '';
    verifyReady: boolean = false;
    searchReady: boolean = false;
    tech: string = '';
    // Enter NumId (1:1 Verification)
    showEnterNumId: boolean = false;
    formNumId: FormGroup = new FormGroup({
      numId: new FormControl('', Validators.required)
    });
    verificationSubjectId: string = "";

    stepOptions: AttributeData[] = [
        { key: this.translateService.instant('category.category_type'), value: "0" },
        { key: this.translateService.instant('category.configuration'), value: "1" },
    ];

    constructor(
        private checkPermissions: CheckPermissionsService,
        private fb: FormBuilder,
        private validatorService: ValidatorService,
        private localStorageService: LocalStorageService,
        private loggerService: ConsoleLoggerService,
        private translateService: TranslateService,
        public auditTrailService: AuditTrailService,
        private messageService: MessageService,
        private confirmationService: ConfirmationService,
        private filterService: FilterService,
        private saveBelongingUseCase: SaveBelongingUseCase,
        private updateBelongingUseCase: UpdateBelongingUseCase,
        private getBelongingsBySubjectIdUseCase: GetBelongingsBySubjectIdUseCase,
        private deleteBelongingByIdUseCase: DeleteBelongingByIdUseCase,
        private createStaticResourceUseCase: CreateStaticResourceUseCase,
        private getStaticResourcesBySubjectIdAndNameUseCase: GetStaticResourcesBySubjectIdAndNameUseCase,
        private deleteStaticResourcesBySubjectIdAndNameAndNumberUseCase: DeleteStaticResourcesBySubjectIdAndNameAndNumberUseCase,
        private getKonektorPropertiesUseCase: GetKonektorPropertiesUseCase,
        private getSubjectByNumIdUseCase: GetSubjectByNumIdUseCase,
    ) {
        this.filterService.register('customDateRange', (value: any, filter: any): boolean => {
            if (!filter || (!filter.startDate && !filter.endDate)) {
            return true; // If no filter, show all
            }
            const dateValue = new Date(value).getTime();
            const startDate = filter.startDate ? new Date(filter.startDate).getTime() : null;
            const endDate = filter.endDate ? new Date(filter.endDate).getTime() : null;
            if (startDate && endDate) {
            return dateValue >= startDate && dateValue <= endDate;
            } else if (startDate) {
            return dateValue >= startDate;
            } else if (endDate) {
            return dateValue <= endDate;
            }
            return false;
        });
    }

    /* Lifecycle Hooks */

    ngOnInit() {
        this.isLoading = true;
        this.managerSettings = this.localStorageService.getSessionSettings()!;
        this.restrictReceptionRoles = this.managerSettings?.continued1?.prisonsSettings?.bioSignAuthRoles?.belongingsReceptionRoles ?? [];
        this.segmentedSearchReceptionRole = this.managerSettings?.continued1?.prisonsSettings?.bioSignAuthRoles?.belongingsReceptionMainRole ?? '';
        // console.log(this.segmentedSearchReceptionRole);
        this.restrictReturnRoles = this.managerSettings?.continued1?.prisonsSettings?.bioSignAuthRoles?.belongingsReturnRoles ?? [];
        this.segmentedSearchReturnRole = this.managerSettings?.continued1?.prisonsSettings?.bioSignAuthRoles?.belongingsReturnMainRole ?? '';
        // console.log(this.segmentedSearchReturnRole);
        this.getKonektorPropertiesUseCase.execute().subscribe({
            next: (data) => {
                if (data) {
                    this.konektorProperties = data;
                    if (data.apiGatewayGrpc) {
                        this.localStorageService.setApiGatewayURL(data.apiGatewayGrpc);
                    }
                    else {
                        this.localStorageService.destroyApiGatewayURL();
                    }
                }
            },
            error: (e) => {
                this.loggerService.error(e);
            },
        });
        this.responsiveOptions = [
            {
                breakpoint: '1199px',
                numVisible: 2,
                numScroll: 1
            },
            {
                breakpoint: '767px',
                numVisible: 1,
                numScroll: 1
            }
        ];
        this.items = [
            { label: this.translateService.instant('content.general') },
            { label: this.translateService.instant('content.belongings') },
            { label: this.translateService.instant('content.signatures') }
        ];
        this.stepOptions = [
            { key: this.translateService.instant('content.general'), value: "0" },
            { key: this.translateService.instant('content.belongings'), value: "1" },
            { key: this.translateService.instant('content.signatures'), value: "2" },
        ];
        this.canReadAndWrite = this.checkPermissions.hasReadAndWritePermissions(this.access_identifier);
        if (!this.canReadAndWrite) {
            this.readOnly = this.checkPermissions.hasReadPermissions(this.access_identifier);
        }
        let settings = this.localStorageService.getSessionSettings();
        if (settings) {
            this.loggerService.debug(settings.catalogs!);
            let options = settings.catalogs?.find((catalog) => catalog.parameter === this.belongingTypeParameter)?.options;
            this.belongingTypeOptions = options ? (JSON.parse(options) as string[]).map((option) => { return { key: option, value: this.getLabel(option, this.belongingTypeParameter) } }) : [{ key: this.translateService.instant('content.other'), value: this.translateService.instant('content.other') }];
        }
        this.getBelongingsBySubjectIdUseCase.execute({ id: this.userSubject?.id! }).then(
            (data) => {
                this.loggerService.debug("Belongings of subject: " + this.userSubject?.id);
                this.loggerService.debug(data);
                this.listOfBelongings = data;
            },
            (e) => {
                this.loggerService.error("Error getting belongings of subject: " + this.userSubject?.id);
                this.loggerService.error(e);
            }
        )
        .finally(() => {
            this.isLoading = false;
        });
    }

    ngOnChanges(changes?: SimpleChanges) {
        if (changes && changes['userSubject'] && changes['userSubject'].currentValue) {
            this.ngOnInit();
        }
        this.image = (this.userSubject?.pic == this.imagePlaceholder || this.userSubject?.pic == "" || this.userSubject?.pic == undefined || this.userSubject?.pic == null) ? this.imagePlaceholder : this.userSubject.pic.includes('data:image/jpeg;base64,') ? this.userSubject?.pic! : 'data:image/jpeg;base64,' + this.userSubject?.pic!;
        if (this.userReception) {
            this.userReceptionImage = (this.userReception.pic == this.imagePlaceholder || this.userReception.pic == "" || this.userReception.pic == undefined || this.userReception.pic == null) ? this.imagePlaceholder : this.userReception.pic.includes('data:image/jpeg;base64,') ? this.userReception?.pic! : 'data:image/jpeg;base64,' + this.userReception?.pic!;
        }
        if (this.userReturn) {
            this.userReturnImage = (this.userReturn.pic == this.imagePlaceholder || this.userReturn.pic == "" || this.userReturn.pic == undefined || this.userReturn.pic == null) ? this.imagePlaceholder : this.userReturn.pic.includes('data:image/jpeg;base64,') ? this.userReturn?.pic! : 'data:image/jpeg;base64,' + this.userReturn?.pic!;
        }
    }

    ngOnDestroy() {
        // Clean up the timeout if the component is destroyed
        this.closeConfirmationDialog();
        this.auditTrailService.resetInactivityMonitor();
    }

    resetInactivityMonitor() {
        this.startCheckingInactivity = false;
        this.confirmDialogTimeoutLimit = 0;
    }

    /* Methods */

    trackDataChange() {
        if (!this.belonging) {
            this.belonging = new BelongingEntity();
        }
        // if (this.isNew) this.belonging.id = undefined;
        this.belonging.subjectId = this.userSubject?.id;
        // if (this.form.get('belongingType')?.touched && this.form.get('belongingType')?.value) {
        //     this.belonging.belongingType = this.form.get('belongingType')?.value.key;
        // }
        // this.belonging.belongingDescription = this.form.get('belongingDescription')?.value;
        this.belonging.comments = this.form.get('comments')?.value;
        this.isDisableSaveButton = !this.form.valid;
    }

    // New Belonging

    newBelonging() {
        this.belonging = new BelongingEntity();
        this.belongingsList = [];
        this.showBelongingDialog = true;
    }

    onNewBelonging() {
        this.isLoading = true;
        if (!this.belonging) {
            this.belonging = new BelongingEntity();
        }
        this.belonging.id = undefined;
        this.belonging.subjectId = this.userSubject?.id;
        this.saveBelongingUseCase.execute({ belonging: this.belonging }).then(
            (data) => {
                this.loggerService.debug(data);
                this.listOfBelongings = [...this.listOfBelongings, data];
                this.belonging = data;
                this.getUserSignatures();
                const at_attributes: ExtraData[] = [
                    { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(data) },
                ];
                this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.ADD_BLG, 0, 'SUCCESS', '', at_attributes);
            },
            (e) => {
                this.loggerService.error(e);
                const at_attributes: ExtraData[] = [
                    { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(this.belonging) },
                    { name: AuditTrailFields.ERROR, value: JSON.stringify(e) }
                ];
                this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.ADD_BLG, 0, 'ERROR', '', at_attributes);
            }
        )
        .finally(() => {
            this.isLoading = false;
            this.operationType = OperationType.INSERT;
            this.activeIndex = 0;
            this.showBelongingDialog = true;
            this.isNew = true;
            this.showReceptionButton = true;
            this.showReceptionInfo = false;
            this.showReturnButton = false;
            this.showReturnInfo = false;
            this.editEnabled = true;
        });
    }

    saveBelonging(closeDialog: boolean = true) {
        if (this.form.valid && this.belonging) {
            this.isLoading = true;
            this.belonging.belongingsList = this.belongingsList;
            this.belonging.returnUserSignatureDate = this.belonging.returnUserSignatureDate ? this.belonging.returnUserSignatureDate : undefined;
            this.belonging.receptionUserSignatureDate = this.belonging.receptionUserSignatureDate ? this.belonging.receptionUserSignatureDate : undefined;
            this.belonging.subjectReceptionSignatureDate = this.belonging.subjectReceptionSignatureDate ? this.belonging.subjectReceptionSignatureDate : undefined;
            this.belonging.subjectReturnSignatureDate = this.belonging.subjectReturnSignatureDate ? this.belonging.subjectReturnSignatureDate : undefined;
            this.belonging.updatedAt = this.belonging.updatedAt ? this.belonging.updatedAt : undefined;
            this.belonging.createdAt = this.belonging.createdAt ? this.belonging.createdAt : undefined;
            this.updateBelongingUseCase.execute({ belonging: this.belonging }).then(
                (data) => {
                    this.loggerService.debug(data);
                    const index = this.listOfBelongings.findIndex((b) => b.id === data.id);
                    this.listOfBelongings[index] = data;
                    this.isNew = false;
                    if (!closeDialog) {
                        this.belonging = data;
                        this.getUserSignatures();
                        this.editBelonging(data);
                    }
                    else {
                        this.onCancelDialog();
                    }
                    let oldValue = this.listOfBelongings.find((b) => b.id === this.belonging?.id);
                    const at_attributes: ExtraData[] = [
                        { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(oldValue) },
                        { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(data) },
                    ];
                    this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.MOD_BLG, 0, 'SUCCESS', '', at_attributes);
                },
                (e) => {
                    this.loggerService.error(e);
                    let oldValue = this.listOfBelongings.find((b) => b.id === this.belonging?.id);
                    const at_attributes: ExtraData[] = [
                        { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(oldValue) },
                        { name: AuditTrailFields.ERROR, value: JSON.stringify(e) }
                    ];
                    this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.MOD_BLG, 0, 'ERROR', '', at_attributes);
                }
            )
            .finally(() => {
                this.isLoading = false;
            });
        }
    }

    // Edit Belonging

    editBelonging(belonging: BelongingEntity) {
        this.managerSettings = this.localStorageService.getSessionSettings()!;
        this.getKonektorPropertiesUseCase.execute().subscribe({
            next: (data) => {
                if (data) {
                    this.konektorProperties = data;
                    if (data.apiGatewayGrpc) {
                        this.localStorageService.setApiGatewayURL(data.apiGatewayGrpc);
                    }
                    else {
                        this.localStorageService.destroyApiGatewayURL();
                    }
                }
            },
            error: (e) => {
                this.loggerService.error(e);
            },
        });
        this.loggerService.debug(belonging);
        if(!belonging) {
            if (this.selectedBelongings.length === 1) {
                belonging = this.selectedBelongings[0];
            }
            else {
                return;
            }
        }
        this.form.reset();
        this.isNew = false;
        this.belonging = {...belonging};
        this.getUserSignatures();
        this.belonging.id = belonging.id;
        this.belongingsList = belonging.belongingsList ?? [];
        this.getPictures();
        this.form.get('comments')?.setValue(belonging.comments);
        this.showBelongingDialog = true;
        this.operationType = OperationType.UPDATE;
        this.activeIndex = 0;
        if (this.readOnly || !this.userIsVerified) {
            this.form.disable();
            this.form.controls['stepOptions'].enable();
            this.form.controls['stepOptions'].setValue(this.activeIndex.toString());
            this.rowForm.disable();
            this.belongingItemForm.disable();
            this.editEnabled = false;
            this.showReceptionInfo = ((belonging.subjectReceptionSignatureTech != null && belonging.subjectReceptionSignatureTech != undefined && belonging.subjectReceptionSignatureTech != '') &&
            (belonging.receptionUserSignatureTech != null && belonging.receptionUserSignatureTech != undefined && belonging.receptionUserSignatureTech != ''))
            this.showReceptionButton = !this.showReceptionInfo;
            this.showReturnInfo = ((belonging.subjectReturnSignatureTech != null && belonging.subjectReturnSignatureTech != undefined && belonging.subjectReturnSignatureTech != '') &&
            (belonging.returnUserSignatureTech != null && belonging.returnUserSignatureTech != undefined && belonging.returnUserSignatureTech != ''))
            this.showReturnButton = !this.showReturnInfo && !this.showReceptionButton;
        }
        else {
            this.form.enable();
            this.form.controls['stepOptions'].setValue(this.activeIndex.toString());
            if ((belonging.subjectReceptionSignatureTech != null && belonging.subjectReceptionSignatureTech != undefined && belonging.subjectReceptionSignatureTech != '')) {
                this.rowForm.disable();
                this.belongingItemForm.disable();
                this.editEnabled = false;
                if ((belonging.receptionUserSignatureTech != null && belonging.receptionUserSignatureTech != undefined && belonging.receptionUserSignatureTech != '')){
                    if ((belonging.subjectReturnSignatureTech != null && belonging.subjectReturnSignatureTech != undefined && belonging.subjectReturnSignatureTech != '') &&
                        (belonging.returnUserSignatureTech != null && belonging.returnUserSignatureTech != undefined && belonging.returnUserSignatureTech != '')) {
                            this.showReturnButton = false;
                            this.showReturnInfo = true;
                            this.showReceptionButton = false;
                            this.showReceptionInfo = true;
                    }
                    else {
                        this.showReturnButton = true;
                        this.showReturnInfo = false;
                        this.showReceptionButton = false;
                        this.showReceptionInfo = true;
                    }
                }
                else {
                    this.showReceptionButton = true;
                    this.showReceptionInfo = false;
                    this.showReturnButton = false;
                    this.showReturnInfo = false;
                }
            }
            else {
                this.rowForm.enable();
                this.belongingItemForm.enable();
                this.editEnabled = true;
                this.showReceptionButton = true;
                this.showReceptionInfo = false;
                this.showReturnButton = false;
                this.showReturnInfo = false;
            }
        }
        if (this.showReceptionButton || this.showReceptionInfo) {
            this.getUserReceptionInfo(belonging.receptionUserSignatureNumId!);
        }
        if (this.showReturnButton || this.showReturnInfo) {
            this.getUserReturnInfo(belonging.returnUserSignatureNumId!);
        }
    }

    getUserReceptionInfo(numId: string) {
        if (numId){
            this.getSubjectByNumIdUseCase.execute({ numId: numId }).then(
                (data) => {
                    this.userReception = data;
                    this.receptionSignatureData = {
                        id: data.id,
                        numId: data.numId,
                        tech: this.belonging?.receptionUserSignatureTech,
                        date: this.belonging?.receptionUserSignatureDate,
                        reason: undefined,
                        observation: undefined,
                    };
                },
                (e) => {
                    this.receptionSignatureData = undefined;
                    this.loggerService.error(e);
                }
            )
            .finally(() => {
                this.ngOnChanges();
            });
        }
    }

    getUserReturnInfo(numId: string) {
        if (numId){
            this.getSubjectByNumIdUseCase.execute({ numId: numId }).then(
                (data) => {
                    this.userReturn = data;
                    this.returnSignatureData = {
                        id: data.id,
                        numId: data.numId,
                        tech: this.belonging?.returnUserSignatureTech,
                        date: this.belonging?.returnUserSignatureDate,
                        reason: undefined,
                        observation: undefined,
                    };
                },
                (e) => {
                    this.returnSignatureData = undefined;
                    this.loggerService.error(e);
                }
            )
            .finally(() => {
                this.ngOnChanges();
            });
        }
    }

    // Delete Belonging

    deleteBelonging(belonging?: BelongingEntity) {
        let dataToDelete: BelongingEntity[] = [];
        if (this.selectedBelongings.length >= 1 && !belonging) {
            dataToDelete = this.selectedBelongings;
        } else if (belonging) {
            dataToDelete.push(belonging);
        }
        this.confirmDelete(dataToDelete);
    }

    confirmDelete(dataToDelete: BelongingEntity[]) {
        let message: string = ""
        if (dataToDelete.length == 1) {
            message = `${this.translateService.instant('messages.delete_single_record')}?`;
        } else {
            message = this.translateService.instant('messages.delete_multiple_records') + "<br>(" + dataToDelete.length + ")<br>";
        }
        this.confirmationService.confirm({
            message: message,
            header: this.translateService.instant('messages.delete_confirmation_header'),
            icon: 'pi pi-exclamation-triangle',
            rejectButtonStyleClass: "p-button-text",
            acceptButtonStyleClass: "ng-confirm-button",
            acceptIcon: "none",
            rejectIcon: "none",
            acceptLabel: this.translateService.instant("delete"),
            rejectLabel: this.translateService.instant("no"),
            accept: () => {
                this.resetInactivityMonitor();
                const at_attributes: ExtraData[] = [
                    { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(dataToDelete) },
                ];
                this.auditTrailService.auditTrailSelectReason(ReasonTypeEnum.SUBJECT, AuditTrailActions.DEL_BLG, ReasonActionTypeEnum.DELETE, () => { this.onSubmitDelete(dataToDelete); }, at_attributes);
            },
            reject: () => {
                this.resetInactivityMonitor();
            }
        });

        this.confirmDialogTimeoutLimit = (this.localStorageService.getSessionSettings()?.timeoutMessages ?? 10) * 1000
        this.startCheckingInactivity = false;
        setTimeout(() => this.startCheckingInactivity = true, 0);
    }

    async onSubmitDelete(dataToDelete: BelongingEntity[]) {
        this.isLoading = true;
        try {
            // Create an array of deletion promises
            await Promise.all(dataToDelete.map(record => this.deleteBelongingById(record)));

            // Success message
            this.messageService.add({
                severity: 'success',
                summary: this.translateService.instant('content.successTitle'),
                detail: this.translateService.instant('messages.success_general'),
                life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
            });

            // Clear selected belongings
            this.selectedBelongings = [];
        } catch (error: any) {
            this.messageService.add({
                severity: 'error',
                summary: this.translateService.instant('content.errorTitle'),
                detail: `${this.translateService.instant('messages.error_deleting_belongings')}: ${error.message}`,
                life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
            });

            this.loggerService.error('Error deleting belongings:');
            this.loggerService.error(error);
        } finally {
            this.isLoading = false;
        }
    }

    deleteBelongingById(belonging: BelongingEntity): Promise<void> {
        return new Promise(async (resolve, reject) => {
            try {
                // Fetch static resources
                const data = await this.getStaticResourcesBySubjectIdAndNameUseCase.execute({
                    subjectId: this.userSubject?.id!,
                    name: this.belongingsPhotoName + belonging?.id
                });

                if (data) {
                    this.belongingsPhotoList = data;
                } else {
                    this.belongingsPhotoList = [];
                }

                // Delete photos if available
                if (this.belongingsPhotoList.length > 0) {
                    await Promise.all(this.belongingsPhotoList.map(photo => this.onSubmitDeletePhoto(photo)));
                }

                // Delete belonging
                const deleteResponse = await this.deleteBelongingByIdUseCase.execute({ id: belonging.id! });

                const at_attributes: ExtraData[] = [
                    { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(belonging) },
                ];

                if (deleteResponse.success) {
                    this.listOfBelongings = this.listOfBelongings.filter(b => b.id !== belonging.id);
                    this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.DEL_BLG, 0, 'SUCCESS', '', at_attributes);
                    resolve();
                } else {
                    at_attributes.push({ name: AuditTrailFields.ERROR, value: JSON.stringify(deleteResponse) });
                    this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.DEL_BLG, 0, 'ERROR', '', at_attributes);
                    reject(new Error("Failed to delete belonging"));
                }
            } catch (error) {
                this.loggerService.error(error);
                const at_attributes: ExtraData[] = [{ name: AuditTrailFields.ERROR, value: JSON.stringify(error) }];
                this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.DEL_BLG, 0, 'ERROR', '', at_attributes);
                reject(error);
            }
        });
    }

    onCancelDialog() {
        if (this.isNew) {
            this.deleteBelongingById(this.belonging!);
        }
        this.belonging = undefined;
        this.isNew = true;
        this.showBelongingDialog = false;
        this.showBelongingPhotosDialog = false;
        this.form.enable();
        this.form.reset();
        this.belongingItemForm.enable();
        this.belongingItemForm.reset();
        this.belongingTypeSelected = undefined;
        this.belongingsList = [];
        this.belongingsPhotoList = [];
        this.showReceptionButton = false;
        this.showReceptionInfo = false;
        this.showReturnButton = false;
        this.showReturnInfo = false;
        this.userNumId = '';
    }

    onBelongingSelectionChange(event: any) { }

    // Table Row Edit

    onRowEditInit(item: BelongingItemEntity) {
        this.clonedBelongingItem[item.id?.toString() as string] = { ...item };
        this.rowForm.get('type')?.setValue(this.belongingTypeOptions.find((b) => b.key === item.type));
        this.rowForm.get('description')?.setValue(item.description);
        this.rowForm.get('comments')?.setValue(item.comments);
    }

    onRowEditSave(item: BelongingItemEntity, index: number) {
        if(this.rowForm.valid) {
            this.belongingsList[index].type = this.rowForm.get('type')?.value.key;
            this.belongingsList[index].description = this.rowForm.get('description')?.value;
            this.belongingsList[index].comments = this.rowForm.get('comments')?.value;
            delete this.clonedBelongingItem[item.id?.toString() as string];
            this.rowForm.reset();
        }
    }

    onRowEditCancel(item: BelongingItemEntity, index: number) {
        this.belongingsList[index] = this.clonedBelongingItem[item.id?.toString() as string];
        delete this.clonedBelongingItem[item.id?.toString() as string];
        this.rowForm.reset();
    }

    // Table Row Delete

    onRowRemoveItem(item: BelongingItemEntity) {
        this.belongingsList = this.belongingsList.filter((val, i) => val.id !== item.id);
    }

    // Belonging Photos

    getPictures() {
        this.isLoading = true;
        this.getStaticResourcesBySubjectIdAndNameUseCase.execute({ subjectId: this.userSubject?.id!, name: this.belongingsPhotoName + this.belonging?.id }).then(
            (data) => {
                if (data) {
                    this.belongingsPhotoList = data.sort((a, b) => {
                        if (a.number === undefined) return -1;  // Push 'undefined' to the end
                        if (b.number === undefined) return 1; // Push 'undefined' to the end
                        return b.number - a.number;            // Regular comparison if both are defined
                    });
                    if (this.belongingsPhotoList.length > 1) {

                        this.responsiveOptions = [
                            {
                                breakpoint: '1199px',
                                numVisible: 2,
                                numScroll: 1
                            },
                            {
                                breakpoint: '767px',
                                numVisible: 1,
                                numScroll: 1
                            }
                        ];
                    }
                }
                this.isLoading = false;
            },
            (e) => {
                this.loggerService.error('Error Getting User/Subject Static Resources:');
                this.loggerService.error(e);
                const at_attributes: ExtraData[] = [{ name: 'error', value: JSON.stringify(e) }];
                this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.GET_STATIC_RESOURCE_BY_SUBJECT_ID, 0, 'ERROR', '', at_attributes);
                this.messageService.add({
                    severity: 'error',
                    summary: this.translateService.instant('content.errorTitle'),
                    detail: `${this.translateService.instant('messages.error_downloading_image')}: ${e.message}`,
                    life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
                });
                this.belongingsPhotoList = [];
                this.isLoading = false;
            }
        );
    }

    showBelongingPhotos(rec: BelongingEntity) {
        this.isLoading = true;
        this.belonging = rec;
        this.getUserSignatures();
        this.isNew = false;
        this.getStaticResourcesBySubjectIdAndNameUseCase.execute({ subjectId: this.userSubject?.id!, name: this.belongingsPhotoName + rec?.id }).then(
            (data) => {
                if (data) {
                    this.belongingsPhotoList = data.sort((a, b) => {
                        if (a.number === undefined) return -1;  // Push 'undefined' to the end
                        if (b.number === undefined) return 1; // Push 'undefined' to the end
                        return b.number - a.number;            // Regular comparison if both are defined
                    });
                    if (this.belongingsPhotoList.length > 1) {

                        this.responsiveOptions = [
                            {
                                breakpoint: '1199px',
                                numVisible: 2,
                                numScroll: 1
                            },
                            {
                                breakpoint: '767px',
                                numVisible: 1,
                                numScroll: 1
                            }
                        ];
                    }
                }
                this.isLoading = false;
                this.showBelongingPhotosDialog = true;
            },
            (e) => {
                this.loggerService.error('Error Getting User/Subject Static Resources:');
                this.loggerService.error(e);
                const at_attributes: ExtraData[] = [{ name: 'error', value: JSON.stringify(e) }];
                this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.GET_STATIC_RESOURCE_BY_SUBJECT_ID, 0, 'ERROR', '', at_attributes);
                this.messageService.add({
                    severity: 'error',
                    summary: this.translateService.instant('content.errorTitle'),
                    detail: `${this.translateService.instant('messages.error_downloading_image')}: ${e.message}`,
                    life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
                });
                this.belongingsPhotoList = [];
                this.isLoading = false;
            }
        );
    }

    openImageDialog(staticResource: StaticResourceEntity, rec?: BelongingItemEntity) {
        if (!staticResource && rec) {
            staticResource = this.belongingsPhotoList.find((b) => b.number === Number(rec.id))!;
        }
        this.selectedCurrentResult = { ...staticResource };
        if (this.selectedCurrentResult?.content) {
            this.openFullImageDialog = true;
        }
    }

    onCameraResult(event: {action: string, staticResource: StaticResourceEntity}){
        switch (event.action) {
            case 'close':
                this.closeDialog();
                break;
            case 'delete':
                const at_attributes: ExtraData[] = [
                    { name: 'static_resource_name', value: event.staticResource.name! },
                    { name: 'static_resource_number', value: event.staticResource.number!.toString() }
                ];
                this.auditTrailService.auditTrailSelectReason(ReasonTypeEnum.SUBJECT, AuditTrailActions.DEL_STA_RES, ReasonActionTypeEnum.DELETE, () => { this.onSubmitDeletePhoto(event.staticResource); }, at_attributes);
                break;
            case 'create':
                this.onSubmitCreatePhoto(event.staticResource);
                break;
        }
    }

    onSubmitCreatePhoto(selectedCurrentResult: StaticResourceEntity) {
        if (selectedCurrentResult) {
            this.isLoading = true;
            let newStaticResource = new CreateStaticResourceEntity();
            // subjectId?: string;
            newStaticResource.subjectId = this.userSubject?.id!;
            // name?: string;
            newStaticResource.name = selectedCurrentResult.name;
            // number?: number;
            newStaticResource.number = selectedCurrentResult.number;
            // content?: string;
            newStaticResource.content = selectedCurrentResult.content;
            // async?: boolean;
            newStaticResource.async = false;
            this.createStaticResourceUseCase.execute({ createStaticResourceRequest: newStaticResource }).then(
                () => {
                    const at_attributes: ExtraData[] = [
                        { name: 'static_resource_name', value: newStaticResource.name! },
                        { name: 'static_resource_number', value: newStaticResource.number!.toString() }
                    ];
                    this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.ADD_STA_RES, 0, 'SUCCESS', '', at_attributes);
                    // this.messageService.add({
                    //     severity: 'success',
                    //     summary: this.translateService.instant('content.successTitle'),
                    //     detail: this.translateService.instant('messages.success_general'),
                    //     life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
                    // });
                    this.closeDialog();
                    this.getPictures();
                },
                (e) => {
                    this.loggerService.error('Error Creating User/Subject Static Resource:');
                    this.loggerService.error(e);
                    const at_attributes: ExtraData[] = [
                        { name: 'error', value: JSON.stringify(e) },
                        { name: 'static_resource_name', value: newStaticResource.name! },
                        { name: 'static_resource_number', value: newStaticResource.number!.toString() }
                    ];
                    this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.ADD_STA_RES, 0, 'ERROR', '', at_attributes);
                    this.messageService.add({
                        severity: 'error',
                        summary: this.translateService.instant('content.errorTitle'),
                        detail: `${this.translateService.instant('messages.error_uploading_image')}: ${e.message}`,
                        life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
                    });
                    this.isLoading = false;
                }
            );
        }
        else {
            this.messageService.add({
                severity: 'error',
                summary: this.translateService.instant("content.errorTitle"),
                detail: this.translateService.instant("messages.no_data_to_save"),
                life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
            });
        }
    }

    onSubmitDeletePhoto(selectedCurrentResult: StaticResourceEntity): Promise<void> {
        return new Promise(async (resolve, reject) => {
            if (!selectedCurrentResult) {
                this.messageService.add({
                    severity: 'error',
                    summary: this.translateService.instant("content.errorTitle"),
                    detail: this.translateService.instant("messages.no_data_to_save"),
                    life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
                });
                return reject(new Error("No data to delete"));
            }

            this.closeDialog();
            this.isLoading = true;

            const param = {
                subjectId: selectedCurrentResult.subjectId!,
                name: selectedCurrentResult.name!,
                number: selectedCurrentResult.number!
            };

            try {
                // Execute deletion
                await this.deleteStaticResourcesBySubjectIdAndNameAndNumberUseCase.execute(param);

                // // Show success message
                // this.messageService.add({
                //     severity: 'success',
                //     summary: this.translateService.instant('content.successTitle'),
                //     detail: this.translateService.instant('messages.success_general'),
                //     life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
                // });

                // Clear the belongings photo list
                this.belongingsPhotoList = [];

                // Refresh pictures (ensure it's awaited if needed)
                await this.getPictures();

                resolve();
            } catch (error: any) {
                this.loggerService.error('Error Deleting User/Subject Static Resource:');
                this.loggerService.error(error);

                const at_attributes: ExtraData[] = [
                    { name: 'error', value: JSON.stringify(error) },
                    { name: 'static_resource_name', value: param.name },
                    { name: 'static_resource_number', value: param.number.toString() }
                ];

                this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId,AuditTrailActions.DEL_STA_RES,0,'ERROR','',at_attributes);

                this.messageService.add({
                    severity: 'error',
                    summary: this.translateService.instant('content.errorTitle'),
                    detail: `${this.translateService.instant('messages.error_removing_image')}: ${error.message}`,
                    life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
                });

                reject(error);
            } finally {
                this.isLoading = false;
            }
        });
    }

    openCameraDialog(rec: BelongingItemEntity) {
        let staticResourceEntity = new StaticResourceEntity();
        staticResourceEntity.name = this.belongingsPhotoName + this.belonging?.id;
        staticResourceEntity.number = Number(rec.id);
        staticResourceEntity.createdAt = new Date();
        staticResourceEntity.content = '';
        this.selectedCurrentResult = { ...staticResourceEntity };
        this.showNewBelongingPhotoDialog = true;
    }

    async closeDialog() {
        this.selectedCurrentResult = undefined;
        this.openFullImageDialog = false;
        this.showNewBelongingPhotoDialog = false;
    }

    userReceptionSignatureResult(event: BioSignatureResult) {
        if (this.belonging) {
            this.belonging.receptionUserSignatureTech = event.tech;
            this.belonging.receptionUserSignatureDate = event.date;
            this.belonging.receptionUserSignatureNumId = event.numId;
            this.rowForm.disable();
            this.belongingItemForm.disable();
            this.editEnabled = false;
            this.receptionSignatureData = {}
            this.saveBelonging(false);
        }
    }

    userReturnSignatureResult(event: BioSignatureResult) {
        if (this.belonging) {
            this.belonging.returnUserSignatureTech = event.tech;
            this.belonging.returnUserSignatureDate = event.date;
            this.belonging.returnUserSignatureNumId = event.numId;
            this.rowForm.disable();
            this.belongingItemForm.disable();
            this.editEnabled = false;
            this.saveBelonging(false);
        }
    }

    getUserSignatures() {
        if (this.belonging) {
            if (this.belonging.receptionUserSignatureNumId) {
                this.getUserReceptionInfo(this.belonging.receptionUserSignatureNumId);
            }
            if (this.belonging.returnUserSignatureNumId) {
                this.getUserReturnInfo(this.belonging.returnUserSignatureNumId);
            }
        }
    }

    // Widget Functions

    openWidgetVerify() {
        this.widgetUrl = this.managerSettings?.widgetConfig?.url || "";
        this.isLoading = false;
        this.updateModified(true);
        this.subjectNumId = this.userSubject?.numId!;
        this.verifyReady = true;
    }

    onWidgetMatchResult(event: WidgetResult) {
        if (!this.verifyReady) {
            return;
        }
        switch (event.action) {
            case "verify":
                if (event.result == "success") {
                    if (event.data.isMatched && this.belonging) {
                        if (this.showReceptionButton) {
                            this.belonging.subjectReceptionSignatureTech = event.data.tech;
                            this.belonging.subjectReceptionSignatureDate = new Date();
                            this.rowForm.disable();
                            this.belongingItemForm.disable();
                            this.editEnabled = false;
                            this.saveBelonging(false);
                        }
                        else if (this.showReturnButton) {
                            this.belonging.subjectReturnSignatureTech = event.data.tech;
                            this.belonging.subjectReturnSignatureDate = new Date();
                            this.rowForm.disable();
                            this.belongingItemForm.disable();
                            this.editEnabled = false;
                            this.saveBelonging(false);
                        }
                        const at_attributes = [
                            { name: AuditTrailFields.REGISTRATION_CODE, value: 'VER_BIO' },
                        ]
                        this.auditTrailService.registerAuditTrailAction(this.subjectNumId, AuditTrailActions.SUBJECT_VERIFY, 0, 'SUCCESS', event.data.tech, at_attributes);
                    }
                }
                this.subjectNumId = '';
                this.verifyReady = false;
                break;
            case "process":
                this.updateModified(event.result.toLowerCase().includes("started"))
                break;
            case "close_verify":
            case "error":
                this.updateModified(false);
                this.subjectNumId = '';
                this.verifyReady = false;
                break;
        }
    }

    onWidgetSearchResult(event: WidgetResult) {
        if (!this.searchReady) {
            return;
        }
        this.loggerService.debug(event);
        switch (event.action) {
            case "verify":
                this.searchReady = false;
                if (event.result == "success") {
                    if (event.data.isMatched && this.belonging) {
                        if (this.showReceptionButton) {
                            this.belonging.receptionUserSignatureTech = event.data.tech;
                            this.belonging.receptionUserSignatureDate = new Date();
                            this.belonging.receptionUserSignatureNumId = this.userNumId;
                            this.rowForm.disable();
                            this.belongingItemForm.disable();
                            this.editEnabled = false;
                            this.saveBelonging(false);
                        }
                        else if (this.showReturnButton) {
                            this.belonging.returnUserSignatureTech = event.data.tech;
                            this.belonging.returnUserSignatureDate = new Date();
                            this.belonging.returnUserSignatureNumId = this.userNumId;
                            this.rowForm.disable();
                            this.belongingItemForm.disable();
                            this.editEnabled = false;
                            this.saveBelonging(false);
                        }
                        const at_attributes = [
                            { name: AuditTrailFields.REGISTRATION_CODE, value: 'VER_BIO' },
                        ]
                        this.auditTrailService.registerAuditTrailAction(this.subjectNumId, AuditTrailActions.USER_VERIFY, 0, 'SUCCESS', event.data.tech, at_attributes);
                    }
                }
                break;
            case 'search':
                const responseData = event.data.nId;
                if (responseData && this.belonging) {
                    if (this.showReceptionButton) {
                        this.belonging.receptionUserSignatureTech = this.tech;
                        this.belonging.receptionUserSignatureDate = new Date();
                        this.belonging.receptionUserSignatureNumId = responseData;
                        this.rowForm.disable();
                        this.belongingItemForm.disable();
                        this.editEnabled = false;
                        this.saveBelonging(false);
                    }
                    else if (this.showReturnButton) {
                        this.belonging.returnUserSignatureTech = this.tech;
                        this.belonging.returnUserSignatureDate = new Date();
                        this.belonging.returnUserSignatureNumId = responseData;
                        this.rowForm.disable();
                        this.belongingItemForm.disable();
                        this.editEnabled = false;
                        this.saveBelonging(false);
                    }
                    const at_attributes = [
                        { name: AuditTrailFields.REGISTRATION_CODE, value: 'IDN_BIO' },
                    ]
                    this.auditTrailService.registerAuditTrailAction(this.subjectNumId, AuditTrailActions.USER_SEARCH, 0, 'SUCCESS', this.tech, at_attributes);
                }
                this.searchReady = false;
                break;
            case "process":
                break;
            case "close_search":
            case "error":
                this.updateModified(false);
                this.searchReady = false;
                this.userNumId = '';
                this.tech = '';
                break;
        }
    }

    onNumIdDialogSubmit() {
        this.formNumId.markAllAsTouched();
        const numId = this.formNumId.get('numId')?.value;
        if (numId && numId != '') {
            this.formNumId.reset();
            this.showEnterNumId = false;
            this.userNumId = numId;
            this.startSearch();
        }
        else {
            this.messageService.add({
                severity: 'error',
                summary: this.translateService.instant("titles.important"),
                detail: this.translateService.instant("messages.ERROR_INVALID_NUMID"),
                life: (this.managerSettings?.timeoutNotification ?? 5) * 1000
            })
        }
    }

    widgetSearch(tech: string) {
        this.widgetUrl = this.managerSettings?.widgetConfig?.url || "";
        this.tech = tech;
        if (this.konektorProperties?.verificationEnabled) {
            if (this.konektorProperties?.verificationSubjectId) {
                this.userNumId = this.konektorProperties.verificationSubjectId;
                this.startSearch();
            }
            else {
                this.showEnterNumId = true;
                return;
            }
        }
        else {
            // this.userNumId = '';
            this.startSearch();
        }
    }

    closeNumIdDialog() {
        this.formNumId.reset();
        this.showEnterNumId = false;
    }

    startSearch() {
        let allowWidget = false;
        switch (this.tech) {
            case 'fingerprint':
                allowWidget = this.managerSettings?.payedTechnology?.dactilar == true && this.konektorProperties?.enabledTech?.dactilar == true && (this.userNumId == '' ? this.managerSettings?.allowSearch?.dactilar == true : this.managerSettings?.allowVerify?.dactilar == true);
                break;
            case 'facial':
                allowWidget = this.managerSettings?.payedTechnology?.facial == true && this.konektorProperties?.enabledTech?.facial == true && (this.userNumId == '' ? this.managerSettings?.allowSearch?.facial == true : this.managerSettings?.allowVerify?.facial == true);
                break;
            case 'iris':
                allowWidget = this.managerSettings?.payedTechnology?.iris == true && this.konektorProperties?.enabledTech?.iris == true && (this.userNumId == '' ? this.managerSettings?.allowSearch?.iris == true : this.managerSettings?.allowVerify?.iris == true);
                break;
        }
        if (allowWidget) {
            this.searchReady = true;
        }
        else {
            this.messageService.add({
                severity: 'error',
                summary: this.translateService.instant('titles.access_denied'),
                detail: this.translateService.instant('messages.error_technology_not_allowed'),
                life: (this.managerSettings?.timeoutNotification ?? 5) * 1000
            });
        }
    }

    openWidgetSearch() {}

    updateModified(modified: boolean) {
        this.localStorageService.setLockMenu(modified);
    }

    // Supporting Functions

    hasImage(rec: BelongingItemEntity): boolean {
        return this.belongingsPhotoList.some((b) => b.number === Number(rec.id));
    }

    getBelongingsStatus(belonging: BelongingEntity): string {
        if ((belonging.subjectReceptionSignatureTech != null && belonging.subjectReceptionSignatureTech != undefined && belonging.subjectReceptionSignatureTech != '') &&
            (belonging.receptionUserSignatureTech != null && belonging.receptionUserSignatureTech != undefined && belonging.receptionUserSignatureTech != '')) {
            if ((belonging.subjectReturnSignatureTech != null && belonging.subjectReturnSignatureTech != undefined && belonging.subjectReturnSignatureTech != '') &&
                (belonging.returnUserSignatureTech != null && belonging.returnUserSignatureTech != undefined && belonging.returnUserSignatureTech != '')) {
                return 'content.returned'
            }
            else {
                return 'content.received'
            }
        }
        else {
            return 'content.registered'
        }
    }

    addBelongingItem() {
        if (this.belongingItemForm.valid) {
            let belongingItem: BelongingItemEntity = {
                id: (this.belongingsList.length + 1).toString(),
                description: this.belongingItemForm.get('belongingDescription')?.value,
                type: this.belongingItemForm.get('belongingType')?.value.key,
                comments: this.belongingItemForm.get('comments')?.value,
            };
            this.belongingsList.push(belongingItem);
            this.belongingItemForm.reset();
        }
    }

    onFilter(event: any, dt: Table) {
        this.filteredValues = event.filteredValue;
        if (!event.filters['registrationDate'].value && !event.filters['subjectReceptionSignatureDate'].value && !event.filters['subjectReturnSignatureDate'].value) {
            this.rangeDates = null;
            this.formGroup.reset();
        }
    }

    applyDateRangeFilter(dt: Table, field: string) {
        this.rangeDates = this.formGroup.get('date')?.value;
        dt.filter({
            startDate: this.rangeDates ? this.rangeDates[0] : null,
            endDate: this.rangeDates ? this.rangeDates[1] : null
        }, field, 'customDateRange');
    }

    isRequiredField(field: string, form: FormGroup): boolean {
        return this.validatorService.isRequiredField(form, field);
    }

    isValid(field: string, form: FormGroup): boolean {
        return this.validatorService.isValidField(form, field);
    }

    isValidDate(dateString: string): boolean {
      const date = new Date(dateString);
      return !isNaN(date.getTime()) && date.toISOString().split('T')[0] != (new Date(0)).toISOString().split('T')[0];
    }

    closeConfirmationDialog() {
        this.confirmationService.close();
        this.resetInactivityMonitor();
    }

    onActiveIndexChange(event: number) {
      this.activeIndex = event;
      this.form.get('stepOptions')?.setValue(this.activeIndex.toString());
    }

    onActiveTabIndexChange(event: any){
      this.activeIndex = Number(event.value);
      this.form.get('stepOptions')?.setValue(this.activeIndex.toString());
    }

    onNext() {
        this.activeIndex += 1;
        this.form.get('stepOptions')?.setValue(this.activeIndex.toString());
    }

    onBack() {
        this.activeIndex -= 1;
        this.form.get('stepOptions')?.setValue(this.activeIndex.toString());
    }

    onClose() {
        this.onCancelDialog();
    }

    getTechIcon(tech: string): string {
        let fingerprintIcon: string = "verazial-common-frontend/assets/images/bio-tech-icons/sm/fingerprint.svg";
        let facialIcon: string = "verazial-common-frontend/assets/images/bio-tech-icons/sm/facial.svg";
        let irisIcon: string = "verazial-common-frontend/assets/images/bio-tech-icons/sm/iris.svg";
        switch (tech) {
            case 'fingerprint':
                return fingerprintIcon;
            case 'facial':
                return facialIcon;
            case 'iris':
                return irisIcon;
            default:
                return '';
        }
    }

    getLabel(key: string, catalogParameter: string): string {
        let reasonTranslations = this.managerSettings?.continued1?.translations?.find((t: TranslationGroup) => t.id === 'catalogs-' + catalogParameter)?.translations || [];
        let translations: LanguageRecordModel[] = reasonTranslations.find((t: TranslationModel) => t.key === key)?.translations || [];
        let translation = translations.find(t => t.languageCode == this.translateService.currentLang);
        if (translation && translation.value) {
            return translation.value
        }
        return key;
    }
}