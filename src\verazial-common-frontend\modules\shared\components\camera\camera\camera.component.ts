import { Component, ElementRef, EventEmitter, Input, OnChanges, OnDestroy, OnInit, Output, SimpleChange, SimpleChanges, ViewChild } from "@angular/core";
import { FormControl, FormGroup } from "@angular/forms";
import { TranslateService } from "@ngx-translate/core";
import { ConfirmationService, MessageService } from "primeng/api";
import { firstValueFrom, from, Observable } from "rxjs";
import { ExtraData } from "src/verazial-common-frontend/core/general/actionsV2/domain/entity/extra-data.interface";
import { KonektorResponseModel } from "src/verazial-common-frontend/core/general/konektor/data/model/konektor-response.model";
import { KonektorPropertiesEntity } from "src/verazial-common-frontend/core/general/konektor/domain/entity/konektor-properties.entity";
import { GetKonektorPropertiesUseCase } from "src/verazial-common-frontend/core/general/konektor/domain/use-cases/get-konektor-properties.use-case";
import { StopTakePhotoUseCase } from "src/verazial-common-frontend/core/general/konektor/domain/use-cases/stop-take-photo.use-case";
import { TakePhotoUseCase } from "src/verazial-common-frontend/core/general/konektor/domain/use-cases/take-photo.use-case";
import { GeneralSettings } from "src/verazial-common-frontend/core/general/manager/common/models/general-settings.model";
import { StaticResourceEntity } from "src/verazial-common-frontend/core/general/storage/domain/entity/static-resource.entity";
import { AuditTrailActions } from "src/verazial-common-frontend/core/models/audit-trail-actions.enum";
import { AuditTrailService } from "src/verazial-common-frontend/core/services/audit-trail.service";
import { ConsoleLoggerService } from "src/verazial-common-frontend/core/services/console-logger.service";
import { LocalStorageService } from "src/verazial-common-frontend/core/services/local-storage.service";

@Component({
    selector: 'app-camera',
    templateUrl: './camera.component.html',
    styleUrl: './camera.component.css',
    providers: [MessageService, ConfirmationService]
})
export class CameraComponent implements OnInit, OnChanges, OnDestroy {

    /* Inputs */
    @Input() canReadAndWrite: boolean = false;
    @Input() showCameraDialog: boolean = false;
    @Input() selectedCurrentResult: StaticResourceEntity | undefined;
    @Input() aspectRatio: number = 1;
    @Input() imagePlaceholder: string = 'verazial-common-frontend/assets/images/admin/physicalDataPlaceholder.svg';
    @Input() showNoDataMessage: string = 'content.noDataAvailable';
    /* Outputs */
    @Output() result = new EventEmitter<{ action: string, staticResource: StaticResourceEntity }>();

    /* Camera Components */
    @ViewChild('videoElement') videoElement!: ElementRef;
    @ViewChild('imageElement') imageElement!: ElementRef;
    @ViewChild('canvasElement') canvasElement!: ElementRef;
    @ViewChild('capturedImage') capturedImage!: ElementRef;
    videoStream: MediaStream | null = null;
    capturedImageSrc: string = '';
    captured: boolean = false;
    isNew: boolean = true;
    availableMediaCameras: MediaDeviceInfo[] = [];
    cameras: string[] = [];
    showCameraDropdown: boolean = false;
    cameraCapturing: boolean = false;
    konektorStream: string = '';
    konektorProperties: KonektorPropertiesEntity | undefined;
    managerSettings: GeneralSettings | undefined;
    showNoData: boolean = false;
    cameraError: boolean = false;
    cameraForm: FormGroup = new FormGroup({
        camera: new FormControl()
    });

    confirmDialogTimeoutLimit: number = 0;
    startCheckingInactivity: boolean = false;

    constructor(
        private localStorageService: LocalStorageService,
        private loggerService: ConsoleLoggerService,
        private translateService: TranslateService,
        private messageService: MessageService,
        private confirmationService: ConfirmationService,
        private auditTrailService: AuditTrailService,
        private getKonektorPropertiesUseCase: GetKonektorPropertiesUseCase,
        private takePhotoUseCase: TakePhotoUseCase,
        private stopTakePhotoUseCase: StopTakePhotoUseCase,
    ) { }

    ngOnInit() {
        this.managerSettings = this.localStorageService.getSessionSettings() as GeneralSettings;
        this.getKonektorPropertiesUseCase.execute().subscribe({
            next: (data) => {
                this.konektorProperties = data;
                if (data.apiGatewayGrpc) {
                    this.localStorageService.setApiGatewayURL(data.apiGatewayGrpc);
                }
                else {
                    this.localStorageService.destroyApiGatewayURL();
                }
            },
            error: (e) => {
                this.loggerService.error(e);
            }
        });
    }

    ngOnChanges(changes: SimpleChanges) {
        if (changes['showCameraDialog'] && this.showCameraDialog) {
            this.initializeCamera();
        }
    }

    ngOnDestroy() {
        // Clean up the timeout if the component is destroyed
        this.closeConfirmationDialog();
    }

    resetInactivityMonitor() {
        this.startCheckingInactivity = false;
        this.confirmDialogTimeoutLimit = 0;
    }

    async initializeCamera() {
        this.showNoData = false;
        this.availableMediaCameras = await this.getAvailableCameras();
        this.cameras = !this.managerSettings?.camerasFromKonektor ? this.getCameraLabels() : this.konektorProperties?.detectedDevices?.facial as string[];
        this.showCameraDropdown = this.cameras.length > 1;
        this.cameraForm.get('camera')?.setValue(this.getDevice());
        if (this.selectedCurrentResult && this.selectedCurrentResult.content) {
            this.capturedImageSrc = 'data:image/png;base64,' + this.selectedCurrentResult.content.replace('data:image/png;base64,', '');
            if (this.capturedImageSrc === '') {
                this.capturedImageSrc = this.imagePlaceholder;
                this.captured = false;
                this.isNew = true;
            }
            else {
                this.captured = true;
                this.isNew = false;
            }
        }
        else {
            this.capturedImageSrc = this.imagePlaceholder;
            this.captured = false;
            this.isNew = true;
        }
        if (this.canReadAndWrite) {
            if (!this.captured) this.startCamera();
        }
        else if (this.capturedImageSrc == this.imagePlaceholder) {
            this.showNoData = true;
        }
    }

    async changeCamera() {
        await this.stopCamera();
        this.startCamera();
    }

    async getAvailableCameras(): Promise<MediaDeviceInfo[]> {
        try {
            // Simply enumerate devices without starting the camera
            const devices = await navigator.mediaDevices.enumerateDevices();
            return devices.filter(device => device.kind === 'videoinput' &&
                !device.label.includes("IRIS-SCANNER") && !device.label.includes("UVC Camera"));
        } catch (error) {
            console.error("Error accessing devices:", error);
            return [];
        }
    }

    getCameraLabels(): string[] {
        let unnamedCounter = 1;
        return this.availableMediaCameras.map(camera => {
            if (camera.label) {
                return camera.label;
            } else {
                // Add a numbered label for unnamed cameras
                return `Unnamed Camera ${unnamedCounter++}`;
            }
        });
    }

    getDevice(): string | null {
        const prefDevice = this.konektorProperties?.prefDevice?.facial as string;
        const detectedDevices = this.konektorProperties?.detectedDevices?.facial as string[];
        let selectedDevice = this.cameraForm.get('camera')?.value;
        if (!this.managerSettings?.camerasFromKonektor) {
            var labels = this.getCameraLabels();
            var device = this.availableMediaCameras[0].label;
            if (selectedDevice) {
                const index = labels.findIndex(element => element.includes(selectedDevice!));
                if (index != -1) {
                    device = selectedDevice;
                }
            }
            else if (prefDevice) {
                const index = labels.findIndex(element => element.includes(prefDevice));
                if (index != -1) {
                    device = this.availableMediaCameras[index].label;
                }
            }
            selectedDevice = device;
            return device;
        }
        if (selectedDevice != "" && detectedDevices && detectedDevices.includes(selectedDevice)) {
            return selectedDevice;
        } else if (detectedDevices && detectedDevices.includes(prefDevice)) {
            selectedDevice = prefDevice;
            return prefDevice;
        } else if (detectedDevices && detectedDevices.length > 0) {
            selectedDevice = detectedDevices[0];
            return selectedDevice;
        } else {
            return null;
        }
    }

    async startCamera() {
        try {
            this.cameraCapturing = false;
            this.capturedImageSrc = '';
            this.isNew = true;
            this.captured = false;
            if (this.managerSettings?.camerasFromKonektor) {
                const camera = this.getDevice();
                this.takePhotoUseCase.execute({ camera: camera! }).subscribe({
                    next: async (reader: ReadableStreamDefaultReader<Uint8Array>) => {
                        this.cameraCapturing = true;
                        const decoder = new TextDecoder();
                        let ndjson = '';
                        let done = false;
                        const lineBuffer: string[] = [];
                        while (!done) {
                            const { done: readDone, value } = await reader.read();
                            if (readDone) {
                                break;
                            }
                            const chunk = decoder.decode(value, { stream: true });
                            ndjson += chunk;
                            const lines = ndjson.split('\n');
                            ndjson = lines.pop() || '';
                            lineBuffer.push(...lines);
                            while (lineBuffer.length) {
                                const line = lineBuffer.shift();
                                if (line && line.trim()) {
                                    try {
                                        const message = JSON.parse(line) as KonektorResponseModel;
                                        if (message.samples?.length == 1) this.konektorStream = message.samples![0].content!;
                                    } catch (error) {
                                        this.loggerService.error("Error parsing konektor message");
                                        this.loggerService.error(error!);
                                    }
                                }
                            }
                        }
                    },
                    error: (e) => {
                        this.loggerService.error("Error taking photo");
                        this.loggerService.error(e);
                        const at_attributes: ExtraData[] = [{ name: 'error', value: JSON.stringify(e) }];
                        this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.KONEKTOR_TAKE_PHOTO, 0, 'ERROR', '', at_attributes);
                        this.messageService.add({
                            severity: 'error',
                            summary: this.translateService.instant('titles.konektor'),
                            detail: this.translateService.instant("messages.konektor_connection_error"),
                            life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
                        });
                        this.closeDialog();
                    }
                });
            }
            else {
                this.startVideo().subscribe({
                    next: (stream: MediaStream) => {
                        this.captured = false;
                        this.cameraCapturing = true;
                        // Set the stream to the video element for display
                        this.videoStream = stream;  // Store the MediaStream
                        const video: HTMLVideoElement = this.videoElement.nativeElement;
                        video.srcObject = this.videoStream;
                    },
                    error: (e) => {
                        this.loggerService.error("Error during stream");
                        this.loggerService.error(e);
                    },
                    complete: () => {
                        this.loggerService.info("Stream completed");
                    }
                });
                // this.videoStream = await navigator.mediaDevices.getUserMedia({ video: true });
                // const video: HTMLVideoElement = this.videoElement.nativeElement;
                // video.srcObject = this.videoStream;
                // this.cameraCapturing = true;
            }
        } catch (err) {
            console.error('Error accessing the camera:', err);
        }
    }

    startVideo(): Observable<MediaStream> {
        this.cameraError = false;
        let label = this.cameraForm.get('camera')?.value || this.availableMediaCameras[0].label;
        const camera = this.availableMediaCameras.find(cam => cam.label === label || (!cam.label && label!.startsWith("Unnamed Camera")));
        if (!camera) {
            this.loggerService.error("Camera not found with label: " + label);
            return from(Promise.reject(new Error("Camera not found")));
        }
        return from(
            navigator.mediaDevices.getUserMedia({
                video: {
                    deviceId: { ideal: camera.deviceId },
                    width: { ideal: 640 },
                    height: { ideal: 480 }
                }
            }).then(stream => {
                return stream;
            }).catch(error => {
                this.loggerService.error("Error accessing the camera for streaming:");
                this.loggerService.error(error);
                this.cameraError = true;
                throw error;
            })
        );
    }

    captureImage() {
        const sourceType = this.managerSettings?.camerasFromKonektor ? 'image' : 'video';
        const canvas: HTMLCanvasElement = this.canvasElement.nativeElement;

        let sourceElement: HTMLVideoElement | HTMLImageElement;
        if (sourceType === 'video') {
            sourceElement = this.videoElement.nativeElement as HTMLVideoElement;
        } else {
            sourceElement = this.imageElement.nativeElement as HTMLImageElement;
        }

        const sourceWidth = sourceElement instanceof HTMLVideoElement ? sourceElement.videoWidth : sourceElement.naturalWidth;
        const sourceHeight = sourceElement instanceof HTMLVideoElement ? sourceElement.videoHeight : sourceElement.naturalHeight;

        // Determine the desired canvas dimensions for 3:4 aspect ratio
        const aspectRatio = this.aspectRatio;
        let cropWidth = sourceWidth;
        let cropHeight = sourceWidth / aspectRatio;

        if (cropHeight > sourceHeight) {
            cropHeight = sourceHeight;
            cropWidth = sourceHeight * aspectRatio;
        }

        // Calculate cropping offsets
        const cropX = (sourceWidth - cropWidth) / 2;
        const cropY = (sourceHeight - cropHeight) / 2;

        // Set canvas dimensions to 3:4 aspect ratio
        canvas.width = cropWidth;
        canvas.height = cropHeight;

        // Draw the cropped frame to the canvas
        const context = canvas.getContext('2d');
        if (context) {
            context.drawImage(
                sourceElement,
                cropX, // Start X of crop
                cropY, // Start Y of crop
                cropWidth, // Width of crop
                cropHeight, // Height of crop
                0, // X coordinate on canvas
                0, // Y coordinate on canvas
                canvas.width, // Canvas width
                canvas.height // Canvas height
            );

            // Extract image data from the canvas
            const imageData = canvas.toDataURL('image/png');
            this.capturedImageSrc = imageData;
            this.captured = true;
        }
        this.stopCamera();
    }

    async stopCamera() {
        this.cameraCapturing = false;
        if (this.konektorStream != '') {
            await firstValueFrom(this.stopTakePhotoUseCase.execute());
        }
        if (this.videoStream) {
            this.cameraCapturing = false;
            this.videoStream.getTracks().forEach(track => track.stop());
            this.videoStream = null;
        } else {
            this.loggerService.error("No media stream to stop");
        }
    }

    // Close
    async closeDialog() {
        this.captured = false;
        this.isNew = true;
        this.stopCamera();
        this.result.emit({ action: 'close', staticResource: { ... this.selectedCurrentResult } });
    }

    // Save
    submitPhoto() {
        if (this.selectedCurrentResult) {
            this.selectedCurrentResult.content = this.capturedImageSrc.replace('data:image/png;base64,', '');
            this.result.emit({ action: 'create', staticResource: { ... this.selectedCurrentResult } });
        }
        else {
            this.messageService.add({
                severity: 'error',
                summary: this.translateService.instant("content.errorTitle"),
                detail: this.translateService.instant("messages.no_data_to_save"),
                life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
            });
        }
    }

    // Delete
    confirmDelete() {
        this.confirmationService.confirm({
            message: `${this.translateService.instant('messages.delete_single_record')} <b>${this.selectedCurrentResult?.name + '-' + this.selectedCurrentResult?.number}</b>?`,
            header: this.translateService.instant('messages.delete_confirmation_header'),
            icon: 'pi pi-exclamation-triangle',
            rejectButtonStyleClass: "p-button-text",
            acceptButtonStyleClass: "ng-confirm-button",
            acceptIcon: "none",
            rejectIcon: "none",
            acceptLabel: this.translateService.instant("delete"),
            rejectLabel: this.translateService.instant("no"),
            accept: () => {
                this.resetInactivityMonitor();
                this.result.emit({ action: 'delete', staticResource: { ... this.selectedCurrentResult } });
            },
            reject: () => {
                this.resetInactivityMonitor();
            }
        });

        this.confirmDialogTimeoutLimit = (this.localStorageService.getSessionSettings()?.timeoutMessages ?? 10) * 1000
        this.startCheckingInactivity = false;
        setTimeout(() => this.startCheckingInactivity = true, 0);
    }

    closeConfirmationDialog() {
        this.confirmationService.close();
        this.resetInactivityMonitor();
    }
}