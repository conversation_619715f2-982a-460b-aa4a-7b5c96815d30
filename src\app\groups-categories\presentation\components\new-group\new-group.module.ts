import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { NewGroupComponent } from './new-group/new-group.component';
import { StepsModule } from 'primeng/steps';
import { InputTextModule } from 'primeng/inputtext';
import { ButtonModule } from 'primeng/button';
import { DropdownModule } from 'primeng/dropdown';
import { InputTextareaModule } from 'primeng/inputtextarea';
import { TranslateModule } from '@ngx-translate/core';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';
import { CalendarModule } from 'primeng/calendar';
import { TableModule } from 'primeng/table';
import { ToastModule } from 'primeng/toast';
import { InputSwitchModule } from 'primeng/inputswitch';
import { SelectButtonModule } from 'primeng/selectbutton';
import { LoadingSpinnerModule } from 'src/verazial-common-frontend/modules/shared/components/loading-spinner/loading-spinner.module';
import { IconFieldModule } from 'primeng/iconfield';
import { InputIconModule } from 'primeng/inputicon';
import { ListUserSubjectModule } from 'src/app/user-subject/presentation/components/list-user-subject/list-user-subject.module';
import { ScrollPanelModule } from 'primeng/scrollpanel';
import { InactivityMonitorModule } from 'src/verazial-common-frontend/modules/shared/components/inactivity-monitor/inactivity-monitor.module';

@NgModule({
  declarations: [
    NewGroupComponent
  ],
  imports: [
    CommonModule,
    /* Translate */
    TranslateModule,
    /* Foms */
    FormsModule,
    ReactiveFormsModule,
    /* PrimeNG */
    InputTextModule,
    StepsModule,
    ButtonModule,
    DropdownModule,
    InputTextareaModule,
    CalendarModule,
    TableModule,
    ToastModule,
    InputSwitchModule,
    SelectButtonModule,
    IconFieldModule,
    InputIconModule,
    ScrollPanelModule,
    /* Custom */
    LoadingSpinnerModule,
    ListUserSubjectModule,
    InactivityMonitorModule,
  ],
  exports: [
    NewGroupComponent
  ]
})
export class NewGroupModule { }
