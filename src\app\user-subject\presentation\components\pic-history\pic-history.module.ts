import { NgModule } from '@angular/core';
import { PicHistoryComponent } from './pic-history/pic-history.component';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { CardModule } from 'primeng/card';
import { ProgressSpinnerModule } from 'primeng/progressspinner';
import { ImageModule } from 'primeng/image';
import { DialogModule } from 'primeng/dialog';
import { ButtonModule } from 'primeng/button';
import { CarouselModule } from 'primeng/carousel';
import { CameraDialogModule } from 'src/verazial-common-frontend/modules/shared/components/camera-dialog/camera-dialog.module';
import { CameraModule } from 'src/verazial-common-frontend/modules/shared/components/camera/camera.module';
import { InactivityMonitorModule } from 'src/verazial-common-frontend/modules/shared/components/inactivity-monitor/inactivity-monitor.module';
// import { CarouselModule } from 'ngx-carousel-ease';


@NgModule({
  declarations: [
    PicHistoryComponent
  ],
  imports: [
    /* Angular Modules */
    CommonModule,
    /* Forms */
    FormsModule,
    ReactiveFormsModule,
    /* Translate */
    TranslateModule,
    /* PrimeNG Modules */
    CardModule,
    ProgressSpinnerModule,
    ImageModule,
    DialogModule,
    ButtonModule,
    CarouselModule,
    /* Custom Modules */
    InactivityMonitorModule,
    CameraDialogModule,
    CameraModule,
  ],
  exports: [
    PicHistoryComponent
  ]
})
export class PicHistoryModule { }
