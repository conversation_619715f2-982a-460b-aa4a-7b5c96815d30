import { Component, OnD<PERSON>roy, OnInit } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { MessageService, ConfirmationService } from 'primeng/api';
import { ExtraData } from 'src/verazial-common-frontend/core/general/actionsV2/domain/entity/extra-data.interface';
import { AddTenantRequestEntity } from 'src/verazial-common-frontend/core/general/tenant/domain/entity/add-tenant-request.entity';
import { TenantEntity } from 'src/verazial-common-frontend/core/general/tenant/domain/entity/tenant.entity';
import { AddTenantUseCase } from 'src/verazial-common-frontend/core/general/tenant/domain/use-cases/add-tenant.use-case';
import { DeleteTenantByIdUseCase } from 'src/verazial-common-frontend/core/general/tenant/domain/use-cases/delete-tenant-by-id.use-case';
import { GetAllTenantsUseCase } from 'src/verazial-common-frontend/core/general/tenant/domain/use-cases/get-all-tenants.use-case';
import { GetTenantByIdUseCase } from 'src/verazial-common-frontend/core/general/tenant/domain/use-cases/get-tenant-by-id.use-case';
import { UpdateTenantByIdUseCase } from 'src/verazial-common-frontend/core/general/tenant/domain/use-cases/update-tenant-by-id.use-case';
import { AccessIdentifier } from 'src/verazial-common-frontend/core/models/access-identifier.enum';
import { AuditTrailActions } from 'src/verazial-common-frontend/core/models/audit-trail-actions.enum';
import { AuditTrailFields } from 'src/verazial-common-frontend/core/models/audit-trail-fields.enum';
import { AuditTrailService, ReasonActionTypeEnum, ReasonTypeEnum } from 'src/verazial-common-frontend/core/services/audit-trail.service';
import { CheckPermissionsService } from 'src/verazial-common-frontend/core/services/check-permissions-service';
import { ConsoleLoggerService } from 'src/verazial-common-frontend/core/services/console-logger.service';
import { LocalStorageService } from 'src/verazial-common-frontend/core/services/local-storage.service';

@Component({
  selector: 'app-tenant-page',
  templateUrl: './tenant-page.component.html',
  styleUrl: './tenant-page.component.css',
  providers: [MessageService, ConfirmationService]
})
export class TenantPageComponent implements OnInit, OnDestroy {

  isLoading: boolean = false;
  isTenantListEmpty: boolean = true;
  listOfTenants: TenantEntity[] | undefined;
  showSavingTenant: boolean = false;

  confirmDialogTimeoutLimit: number = 0;
  startCheckingInactivity: boolean = false;

  // Access code identifier
  access_identifier: string = AccessIdentifier.TENANTS;
  canReadAndWrite: boolean = false;

  constructor(
    private confirmationService: ConfirmationService,
    private messageService: MessageService,
    private translate: TranslateService,
    private checkPermissions: CheckPermissionsService,
    private getAllTenantUseCase: GetAllTenantsUseCase,
    private saveTenantUseCase: AddTenantUseCase,
    private updateTenantUseCase: UpdateTenantByIdUseCase,
    private deleteTenantByIdUseCase: DeleteTenantByIdUseCase,
    private getTenantByIdUseCase: GetTenantByIdUseCase,
    private localStorageService: LocalStorageService,
    public auditTrailService: AuditTrailService,
    private loggerService: ConsoleLoggerService,
  ) { }

  ngOnInit(): void {
    this.isLoading = true;
    this.canReadAndWrite = this.checkPermissions.hasReadAndWritePermissions(this.access_identifier);
    this.getAllTenants();
  }

  ngOnDestroy() {
    // Clean up the timeout if the component is destroyed
    this.closeConfirmationDialog();
    this.auditTrailService.resetInactivityMonitor();
  }

  resetInactivityMonitor() {
    this.startCheckingInactivity = false;
    this.confirmDialogTimeoutLimit = 0;
  }

  // Get all tenants
  getAllTenants() {
    this.getAllTenantUseCase.execute({ offset: 0, limit: 10000 }).then(
      (data) => {
        this.listOfTenants = data;
        this.isLoading = false;
      },
      (e) => {
        this.isLoading = false;
        this.loggerService.error(e);
          const at_attributes: ExtraData[] = [
            { name: AuditTrailFields.ERROR, value: JSON.stringify(e) }
          ];
          this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.GET_ALL_TENANTS, 0, 'ERROR', '', at_attributes);
      }
    )
  }

  createNewTenant(event: any) {
    this.loggerService.debug(event);
  }

  // Update Tenant
  onSaveTenant(tenant: TenantEntity) {
    this.getTenantByIdUseCase.execute({ id: tenant.id! }).then(
      (data) => {
        const at_attributes: ExtraData[] = [
          { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(data) },
          { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(tenant) },
        ];
        this.auditTrailService.auditTrailSelectReason(ReasonTypeEnum.CONFIG, AuditTrailActions.MOD_TNT, ReasonActionTypeEnum.UPDATE, () => { this.updateTenant(tenant); }, at_attributes);
      },
      (e) => {
        this.loggerService.error(e);
        const at_attributes: ExtraData[] = [
          { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
          { name: AuditTrailFields.RECORD_ID, value: tenant.id!.toString() },
        ];
        this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.GET_TENANT_BY_ID, 0, 'ERROR', '', at_attributes);
      }
    );
  }

  // Add new tenant
  addTenant(tenant: AddTenantRequestEntity) {
    const at_attributes: ExtraData[] = [
      { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(tenant) },
    ];
    this.auditTrailService.auditTrailSelectReason(ReasonTypeEnum.CONFIG, AuditTrailActions.ADD_TNT, ReasonActionTypeEnum.CREATE, () => {
      this.showSavingTenant = true
      this.addNewTenant(tenant);
    }, at_attributes);
  }

  // Add new tenant
  addNewTenant(request: AddTenantRequestEntity) {
    this.saveTenantUseCase.execute({ tenant: request }).then(
      (data) => {
        // this.listOfTenants?.push(data);
        this.listOfTenants = [...this.listOfTenants!, data];
        this.messageService.add({
          severity: 'success',
          summary: this.translate.instant('content.successTitle'),
          detail: this.translate.instant('messages.success_general'),
          life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
        });
      },
      (e) => {
        this.messageService.add({
          severity: 'error',
          summary: this.translate.instant('content.errorTitle'),
          detail: `${this.translate.instant('messages.error_creating_tenant')}: ${e.message}`,
          life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
        });
        this.loggerService.error(e);
        const at_attributes: ExtraData[] = [
          { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
          { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(request) },
        ];
        this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.ADD_TNT, 0, 'ERROR', '', at_attributes);
      },
    )
    .finally(() => {
      this.showSavingTenant = false;
    });
  }

  // Update Tenant
  updateTenant(tenant: TenantEntity) {
    this.updateTenantUseCase.execute({ tenant: tenant }).then(
      (data) => {
        this.listOfTenants = this.listOfTenants?.filter((t) => tenant.id != t.id);
        // this.listOfTenants?.push(data);
        this.listOfTenants = [...this.listOfTenants!, data];
        this.messageService.add({
          severity: 'success',
          summary: this.translate.instant('content.successTitle'),
          detail: this.translate.instant('messages.success_general'),
          life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
        });
      },
      (e) => {
        this.messageService.add({
          severity: 'error',
          summary: this.translate.instant('content.errorTitle'),
          detail: `${this.translate.instant('messages.error_updating_tenant')}: ${e.message}`,
          life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
        });
        this.loggerService.error(e);
        this.getTenantByIdUseCase.execute({ id: tenant.id! }).then(
          (data) => {
            const at_attributes: ExtraData[] = [
              { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
              { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(data) },
              { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(tenant) },
            ];
            this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.MOD_TNT, 0, 'ERROR', '', at_attributes);
          },
          (e) => {
            this.loggerService.error(e);
            const at_attributes: ExtraData[] = [
              { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
              { name: AuditTrailFields.RECORD_ID, value: tenant.id!.toString() },
            ];
            this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.GET_TENANT_BY_ID, 0, 'ERROR', '', at_attributes);
          }
        );
      },
    )
  }

  // Confirmation Dialogs
  confirmDelete(tenant: TenantEntity) {
    this.confirmationService.confirm({
      message: `${this.translate.instant('messages.delete_single_record')} <b>${tenant.name}</b>?`,
      header: this.translate.instant('messages.delete_confirmation_header'),
      icon: 'pi pi-exclamation-triangle',
      rejectButtonStyleClass: "p-button-text",
      acceptButtonStyleClass: "ng-confirm-button",
      acceptIcon: "none",
      rejectIcon: "none",
      acceptLabel: this.translate.instant("delete"),
      rejectLabel: this.translate.instant("no"),
      accept: () => {
        this.resetInactivityMonitor();
        const at_attributes: ExtraData[] = [
          { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(tenant) },
        ];
        this.auditTrailService.auditTrailSelectReason(ReasonTypeEnum.CONFIG, AuditTrailActions.DEL_TNT, ReasonActionTypeEnum.DELETE, () => { this.deleteTenant(tenant); }, at_attributes);
      },
      reject: () => {
        this.resetInactivityMonitor();
      }
    });

    this.confirmDialogTimeoutLimit = (this.localStorageService.getSessionSettings()?.timeoutMessages ?? 10) * 1000
    this.startCheckingInactivity = false;
    setTimeout(() => this.startCheckingInactivity = true, 0);
  }

  // Delete Tenant
  deleteTenant(tenant: TenantEntity) {
    this.isLoading = true;
    this.deleteTenantByIdUseCase.execute({ id: tenant.id! }).then(
      (_) => {
        this.listOfTenants = this.listOfTenants?.filter((t) => tenant.id != t.id);
        this.listOfTenants = [...this.listOfTenants!];
        this.messageService.add({
          severity: 'success',
          summary: this.translate.instant('content.successTitle'),
          detail: this.translate.instant('messages.success_general'),
          life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
        });
      },
      (e) => {
        this.messageService.add({
          severity: 'error',
          summary: this.translate.instant('content.errorTitle'),
          detail: `${this.translate.instant('messages.error_deleting_tenant')}: ${e.message}`,
          life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
        });
        this.loggerService.error(e);
        const at_attributes: ExtraData[] = [
          { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
          { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(tenant) },
        ];
        this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.DEL_TNT, 0, 'ERROR', '', at_attributes);
      },
    )
    .finally(() => {
      this.isLoading = false;
    });

  }

  closeConfirmationDialog() {
    this.confirmationService.close();
    this.resetInactivityMonitor();
  }
}