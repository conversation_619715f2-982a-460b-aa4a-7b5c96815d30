<app-inactivity-monitor
    [inactivityTimeoutLimit]="confirmDialogTimeoutLimit"
    [startChecking]="startCheckingInactivity"
    (expired)="closeConfirmationDialog()"
></app-inactivity-monitor>
<app-inactivity-monitor
    [inactivityTimeoutLimit]="auditTrailService.confirmDialogTimeoutLimit"
    [startChecking]="auditTrailService.startCheckingInactivity"
    (expired)="auditTrailService.closeConfirmationDialog()"
></app-inactivity-monitor>
<div [formGroup]="form">
    <!--p-messages [(value)]="messages" [enableService]="false" [showTransitionOptions]="'500ms'" [hideTransitionOptions]="'500ms'" [closable]="false"></p-messages-->
    <p-toast></p-toast>
    <div class="container">
        <div class="content" >
            <div class="edit-header">
                <div class="content">
                    <p-button icon="pi pi-angle-left" [text]="true" (onClick)="onReturn()"></p-button>
                    <input id="disabled-input" type="text" pInputText [disabled]="true" formControlName="flowTitle" class="p-inputtext-sm"/>
                    <div class="last-update-div" *ngIf="showLastUpdate">
                        <div class="vertical-line"></div>
                        <label class="text">{{ "flow.last_update" | translate}}: {{ lastUpdate }}</label>
                    </div>
                </div>
                <div class="content">
                    <div class="vertical-line"></div>
                    <p-button label="{{ 'save' | translate }}" icon="pi pi-save"
                    [disabled]="!readAndWritePermissions"
                    (onClick)="saveFlow()"
                    [style]="{'width':'110px','height':'36px', 'color': '#FFFFFF' , 'border': 'none', 'background': '#0AB4BA', 'font-family': 'Open Sans', 'font-size': '14px'}"></p-button>
                    <p-button label="{{ publishText | translate}}" icon="pi pi-file-{{ publishIcon }}"
                    [disabled]="!readAndWritePermissions? isPublishDisabled: isPublishDisabled"
                    (onClick)="updateTaskFlow($event)"
                    ngStyle=""
                    [style]="isFlowPublished? {'height':'36px', 'color': '#FFFFFF' , 'border': 'none', 'background': '#64748B', 'font-family': 'Open Sans', 'font-size': '14px'}:
                    {'height':'36px', 'color': '#FFFFFF' , 'border': 'none', 'background': '#F59E0B', 'font-family': 'Open Sans', 'font-size': '14px'}"></p-button>
                </div>
            </div>
            <div class="main-content" [style]="{'border': 'solid 1px #DADEE3', 'height': '-webkit-fill-available'}">
                <div class="menu-actions">
                    <div class="actions card">
                        <label class="title">{{ "flow.add_action" | translate}}</label>
                        <div class="dashed-horizontal-line"></div>
                        @if(actions.length == 0){
                            <label class="text">{{ "flow.no_actions" | translate }}</label>
                        }
                        <div *ngFor="let action of actions">
                           <p-splitButton
                                #button
                                [disabled]="!readAndWritePermissions"
                                label="{{ action.name }}"
                                draggable="true"
                                (dragstart)="onDragStart($event, action)"
                                data-node="{{ action.id }}"
                                [model]="options"
                                (onDropdownClick)="onRightClick(action)"
                                styleClass="p-splitButton-custom">
                            </p-splitButton>
                            <!--p-contextMenu [target]="button" [model]="options" (onshow)="onShow()"></p-contextMenu-->
                        </div>
                    </div>
                    <div class="footer-menu">
                        <p-button
                        [disabled]="!readAndWritePermissions"
                        label="{{ 'flow.new_action' | translate }}"
                        icon="pi pi-plus"
                        iconPos="right"
                        (onClick)="saveAction()"
                        [style]="{'width':'188px','height':'36px', 'color': '#FFFFFF' , 'border': 'none', 'background': '#15294A', 'font-family': 'Open Sans', 'font-size': '14px'}"></p-button>
                    </div>
                </div>
                <div id="drawflow"
                    class="drawflow-class flex-grow-1 flex m-2 px-5 py-3 border-round "
                    (drop)="drop($event)"
                    (dragover)="allowDrop($event)">
                    <div class="zoom">
                        <div class="pi pi-search-minus" (click)="editor.zoom_out()"></div>
                        <div class="pi pi-search-plus" (click)="editor.zoom_in()"></div>
                    </div>
                    <div *ngIf="listNodesDrawFlow.length == 0" class="empty-flow">
                        <div class="content">
                            <div class="pi pi-plus py-3" [style]="{'color': '#009BA9', 'font-size': '1.7rem'}"></div>
                            <label> {{ "flow.drag_drop" | translate}} <b>{{ "flow.the_action" | translate}}</b>. </label>
                        </div>
                    </div>
                </div>
                <div class="components" [style]="hideStyle">
                    <div class="field">
                        <label class="label-text" for="actionNamePanel">{{ 'flow.name' | translate }}</label>
                        <input id="actionNamePanel" formControlName="actionNamePanel" type="text" pInputText/>
                    </div>
                    <div class="field">
                        <label class="label-text" for="actionDescriptionPanel">{{ 'flow.description' | translate }}</label>
                        <textarea formControlName="actionDescriptionPanel" rows="3" cols="30" pInputTextarea></textarea>
                    </div>
                    <div class="divider">
                        <p><label class="label-text">{{ 'flow.attributes' | translate }}</label></p>
                    </div>
                    <!--h4><span>{{ 'flow.attributes' | translate }}</span></h4-->
                    <div class="flex">
                        <div class="dropdown">
                            <p-dropdown appendTo="body" [options]="attributes" formControlName="attributeType" optionLabel="value" [showClear]="true" placeholder="{{ 'flow.select_element' | translate }}" (onChange)="changeAttribute($event)"></p-dropdown>
                        </div>
                        <div class="flex-shrink-0 flex align-items-center justify-content-center font-bold border-round">
                            <p-button icon="pi pi-plus"
                            [disabled]="!readAndWritePermissions"
                            (onClick)="addAttribute()"
                            [style]="{ 'color': '#FFFFFF' , 'border': 'none', 'background': '#15294A'}"></p-button>
                        </div>
                    </div>
                    <br>

                    <div *ngIf="dataComponentLoading">
                        <div *ngFor="let item of listAttributes">
                            <p-skeleton height="2rem" styleClass="mb-2"></p-skeleton>
                        </div>
                    </div>

                    <div [style]="attributeStyle">
                        <p-accordion [multiple]="true">
                            <div class="flex flex-column mt-1" *ngFor="let item of listAttributes; trackBy: trackByItems;">
                                @if(item.type == attributeTypes.INPUT){
                                    <p-accordionTab class="custom-accordion-style" header="{{ 'flow.input' | translate }}: {{item.name}}">
                                        <div class="field">
                                            <label class="label-text" for="inputText">{{ 'flow.name' | translate }}</label>
                                            <input [disabled]="!readAndWritePermissions" id="{{item.id}}-reason-textbox" type="text" pInputText placeholder="{{ 'flow.name' | translate }}" (input)="onChangeAttributeName(item)"/>
                                        </div>

                                        <div class="field">
                                            <label class="label-text" for="inputText">{{ 'flow.min_characters' | translate }}</label>
                                            <input [disabled]="!readAndWritePermissions" id="{{item.id}}-min-characters-textbox" type="number" pInputText value="0" min="1" max="1000000"/>
                                        </div>

                                        <div class="field">
                                            <label class="label-text" for="inputText">{{ 'flow.max_characters' | translate }}</label>
                                            <input [disabled]="!readAndWritePermissions" id="{{item.id}}-max-characters-textbox" type="number" pInputText value="0" min="1" max="1000000"/>
                                        </div>
                                        <div class="card flex justify-content-center gap-3">
                                            <label class="container-checkbox">{{ 'flow.user' | translate}}
                                                <input [disabled]="!readAndWritePermissions" type="checkbox" id="{{item.id}}-user-checkbox">
                                                <span class="checkmark"></span>
                                            </label>
                                            <label class="container-checkbox">{{ 'flow.required' | translate}}
                                                <input [disabled]="!readAndWritePermissions" type="checkbox" id="{{item.id}}-required-checkbox">
                                                <span class="checkmark"></span>
                                            </label>
                                        </div>
                                        <br>
                                        <div class="horizontal-line"></div>
                                        <div class="div-trash">
                                            <p-button icon="pi pi-trash" [disabled]="!readAndWritePermissions" [rounded]="true" [text]="true" (onClick)="deleteElement(item)" />
                                            <!--div class="pi pi-trash" (click)="deleteElement(item)"></div-->
                                        </div>
                                    </p-accordionTab>
                                }@else if(item.type == attributeTypes.DROPDOWN){
                                    <p-accordionTab class="custom-accordion-style" header="{{ 'flow.dropdown' | translate}}: {{item.name}}">
                                        <div class="field">
                                            <label class="label-text" for="inputText">{{ 'flow.name' | translate }}</label>
                                            <input [disabled]="!readAndWritePermissions" id="inputText" id="{{item.id}}-reason-textbox" type="text" pInputText placeholder="{{ 'flow.name' | translate }}" (input)="onChangeAttributeName(item)"/>
                                        </div>
                                        <div class="card p-fluid gap-3">
                                            <div class="flex gap-1">
                                                <div class="add-option">
                                                    <label class="label-text" for="{{item.id}}-options-chips">{{ 'flow.options' | translate }}</label>
                                                    <input
                                                        id="{{item.id}}-option-textbox"
                                                        type="text"
                                                        [disabled]="!readAndWritePermissions"
                                                        pInputText placeholder="{{ 'flow.option' | translate }}"
                                                        />
                                                </div>
                                                <div class="flex-shrink-0 flex align-items-center justify-content-center font-bold border-round" [style]="{'margin-top':'20px'}">
                                                    <p-button icon="pi pi-plus"
                                                        (onClick)="addOptionListBox(item.id)"
                                                        [style]="{ 'color': '#FFFFFF' , 'border': 'none', 'background': '#204887' }">
                                                    </p-button>
                                                </div >
                                                <div class="flex-shrink-0 flex align-items-center justify-content-center font-bold border-round" [style]="{'margin-top':'20px'}">
                                                    <p-button icon="pi pi-minus"
                                                        (onClick)="removeOptionListBox(item.id)"
                                                        [style]="{ 'color': '#FFFFFF' , 'border': 'none', 'background': '#204887' }"
                                                        [disabled]="removeOptionStatus"
                                                        >
                                                    </p-button>
                                                </div>
                                            </div>
                                            <div>
                                                <select [disabled]="!readAndWritePermissions" id="{{item.id}}-options-listbox" size="4" class="listbox"></select>
                                            </div>
                                        </div>
                                        <div class="card flex justify-content-left gap-3">
                                            <label class="container-checkbox">{{ 'flow.required' | translate}}
                                                <input [disabled]="!readAndWritePermissions" type="checkbox" id="{{item.id}}-required-checkbox">
                                                <span class="checkmark"></span>
                                            </label>
                                        </div>
                                        <br>
                                        <div class="horizontal-line"></div>
                                        <div class="div-trash">
                                            <p-button icon="pi pi-trash" [disabled]="!readAndWritePermissions" [rounded]="true" [text]="true" (onClick)="deleteElement(item)" />
                                        </div>
                                    </p-accordionTab>
                                }@else if(item.type == attributeTypes.TOGGLE){
                                    <p-accordionTab class="custom-accordion-style" header="{{ 'flow.toggle' | translate}}: {{item.name}}">
                                        <div class="field">
                                            <label class="label-text" for="inputText">{{ 'flow.name' | translate }}</label>
                                            <input [disabled]="!readAndWritePermissions" id="{{item.id}}-reason-textbox" type="text" pInputText placeholder="{{ 'flow.name' | translate }}" (input)="onChangeAttributeName(item)"/>
                                        </div>
                                        <div class="card flex justify-content-left gap-3">
                                            <label class="container-checkbox">{{ 'flow.required' | translate}}
                                                <input [disabled]="!readAndWritePermissions" type="checkbox" id="{{item.id}}-required-checkbox">
                                                <span class="checkmark"></span>
                                            </label>
                                        </div>
                                        <div class="horizontal-line"></div>
                                        <div class="div-trash">
                                            <p-button icon="pi pi-trash" [disabled]="!readAndWritePermissions" [rounded]="true" [text]="true" (onClick)="deleteElement(item)" />
                                        </div>
                                    </p-accordionTab>
                                }@else if(item.type == attributeTypes.BUTTON){
                                    <p-accordionTab class="custom-accordion-style" header="{{ 'flow.button' | translate}}: {{item.name}}">
                                        <div class="field">
                                            <label class="label-text" for="inputText">{{ 'flow.name' | translate }}</label>
                                            <input [disabled]="!readAndWritePermissions" id="{{item.id}}-reason-textbox" type="text" pInputText placeholder="{{ 'flow.name' | translate }}" (input)="onChangeAttributeName(item)"/>
                                        </div>
                                        <div class="card flex justify-content-center gap-3">
                                            <label class="container-checkbox">{{ 'flow.required' | translate}}
                                                <input [disabled]="!readAndWritePermissions" type="checkbox" id="{{item.id}}-required-checkbox">
                                                <span class="checkmark"></span>
                                            </label>
                                            <label class="container-checkbox">{{ 'flow.is_relay' | translate}}
                                                <input [disabled]="!readAndWritePermissions" type="checkbox" id="{{item.id}}-relay-checkbox">
                                                <span class="checkmark"></span>
                                            </label>
                                        </div>
                                        <div class="horizontal-line"></div>
                                        <div class="div-trash">
                                            <p-button icon="pi pi-trash" [disabled]="!readAndWritePermissions" [rounded]="true" [text]="true" (onClick)="deleteElement(item)" />
                                        </div>
                                    </p-accordionTab>
                                }@else if(item.type == attributeTypes.MESSAGE){
                                    <p-accordionTab class="custom-accordion-style" header="{{ 'flow.message' | translate}}: {{item.name}}">
                                        <div class="field">
                                            <label class="label-text" for="inputText">{{ 'flow.name' | translate }}</label>
                                            <input [disabled]="!readAndWritePermissions" id="{{item.id}}-reason-textbox" type="text" pInputText placeholder="{{ 'flow.name' | translate }}" (input)="onChangeAttributeName(item)"/>
                                        </div>
                                        <div class="field">
                                            <label class="label-text" for="inputText">{{ 'flow.message' | translate }}</label>
                                            <textarea
                                                [disabled]="!readAndWritePermissions"
                                                id="{{item.id}}-body-textarea"
                                                rows="5"
                                                cols="30"
                                                pInputTextarea >
                                            </textarea>
                                            <!-- input [disabled]="!readAndWritePermissions" id="{{item.id}}-body-textarea" type="text" pInputText placeholder="{{ 'flow.name' | translate }}" (input)="onChangeAttributeName(item)"/-->
                                        </div>
                                        <div class="field">
                                            <label class="label-text" for="inputText">{{ 'flow.severity' | translate }}</label>
                                            <p-dropdown
                                            appendTo="body"
                                            [options]="messageType"
                                            placeholder="{{ 'content.select' | translate}}"
                                            optionLabel="value"
                                            (onChange)="onComboboxChange($event)"
                                            id="{{item.id}}-severity-dropdown"
                                            ></p-dropdown>
                                            <!--input [disabled]="!readAndWritePermissions" id="{{item.id}}-body-textarea" type="text" pInputText placeholder="{{ 'flow.name' | translate }}" (input)="onChangeAttributeName(item)"/-->
                                        </div>
                                        <div class="card flex justify-content-start gap-3">
                                            <label class="container-checkbox">{{ 'flow.required' | translate}}
                                                <input [disabled]="!readAndWritePermissions" type="checkbox" id="{{item.id}}-required-checkbox">
                                                <span class="checkmark"></span>
                                            </label>
                                        </div>
                                        <div class="horizontal-line"></div>
                                        <div class="div-trash">
                                            <p-button icon="pi pi-trash" [disabled]="!readAndWritePermissions" [rounded]="true" [text]="true" (onClick)="deleteElement(item)" />
                                        </div>
                                    </p-accordionTab>
                                }
                            </div>
                        </p-accordion>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <p-confirmDialog [style]="{width: '395px'}"></p-confirmDialog>

    <p-dialog [(visible)]="showActionDialog" [style]="{ width: '392px' }" [header]="headerActionDialog" [modal]="true" styleClass="p-fluid">
        <ng-template pTemplate="content">
            <div class="flex align-content-center justify-content-center mt-3 gap-3">
                <div class="dialog-content" >
                    <div class="field">
                        <label class="label-form" for="actionName">{{ "flow.name" | translate}}</label>
                        <input type="text" pInputText id="actionName" formControlName="actionName"  required autofocus />
                    </div>
                    <div class="field">
                        <label class="label-form" for="actionDescription">{{ "flow.description" | translate}}</label>
                        <textarea id="actionDescription" pInputTextarea formControlName="actionDescription" required rows="2" cols="30" [autoResize]="false"></textarea>
                    </div>
                </div>
            </div>
        </ng-template>
         <ng-template pTemplate="footer">
            <div class="dialog-footer">
                <button pButton pRipple label="{{ 'cancel' | translate }}" class="p-button-text" (click)="closeActionDialog()"></button>
                <p-button label="{{ 'save' | translate }}" class="p-button-text" (click)="addNewAction()"
                [style]="{'color': '#FFFFFF' , 'background': '#204887' }"
                ></p-button>
            </div>
         </ng-template>
    </p-dialog>

    <p-dialog [(visible)]="showSaveFlowDialog" [style]="{ width: '380px' }" header="{{ 'flow.save_flow' | translate }}" [modal]="true" styleClass="p-fluid">
        <ng-template pTemplate="content">
            <div class="dialog-content flex" >
                <div class="field">
                    <label class="label-form" for="flowName">{{ "flow.name" | translate}}</label>
                    <input type="text" pInputText formControlName="flowName" id="flowName" required autofocus [class.ng-invalid]="flowNameError" [class.ng-dirty]="flowNameError"/>
                    <div *ngIf="flowNameError">
                        <small class="error" id="lastNames-help">{{ flowNameErrorMessage | translate }}</small>
                    </div>
                </div>
                <div class="field">
                    <label class="label-form" for="flowDescription">{{ "flow.description" | translate}}</label>
                    <textarea id="flowDescription" pInputTextarea formControlName="flowDescription" required rows="2" cols="30" [autoResize]="false" [class.ng-invalid]="flowDescriptionError" [class.ng-dirty]="flowDescriptionError"></textarea>
                    <div *ngIf="flowDescriptionError">
                        <small class="error" id="lastNames-help">{{ flowDescriptionErrorMessage | translate }}</small>
                    </div>
                </div>
                <div class="field">
                    <table>
                        <tr>
                            <td>{{ 'publish' | translate }}</td>
                            <td class="right"><p-inputSwitch id="isPublished" formControlName="isPublished" [(ngModel)]="checked"></p-inputSwitch></td>
                        </tr>
                    </table>
                </div>
            </div>
         </ng-template>
         <ng-template pTemplate="footer">
            <div class="dialog-footer">
                <button pButton pRipple label="{{ 'cancel' | translate }}" class="p-button-text" (click)="closeFlowDialog()"></button>
                <p-button label="{{ 'save' | translate }}" class="p-button-text" (click)="acceptSaveTaskFlow()"
                [style]="{'color': '#FFFFFF' , 'background': '#204887' }"
                ></p-button>
            </div>
         </ng-template>
    </p-dialog>
</div>
