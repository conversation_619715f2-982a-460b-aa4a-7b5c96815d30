<app-loading-spinner [isLoading]="isLoading"></app-loading-spinner>
<p-toast></p-toast>
<p-confirmDialog [style]="{width: '395px'}"></p-confirmDialog>
<app-inactivity-monitor
    [inactivityTimeoutLimit]="confirmDialogTimeoutLimit"
    [startChecking]="startCheckingInactivity"
    (expired)="closeConfirmationDialog()"
></app-inactivity-monitor>
<app-inactivity-monitor
    [inactivityTimeoutLimit]="auditTrailService.confirmDialogTimeoutLimit"
    [startChecking]="auditTrailService.startCheckingInactivity"
    (expired)="auditTrailService.closeConfirmationDialog()"
></app-inactivity-monitor>
<div class="subcontainer">
    <div *ngIf="listOfEntryExitAuths.length != 0 else empty" class="subcontainer-list gap-3">
        <div class="flex flex-row justify-content-between flex-wrap gap-2">
            <div class="flex flex-row align-items-center">
                <div class="pr-3">
                    <label class="subcontainer-title">{{ 'titles.entry_exit_authorizations' | translate}}</label>
                </div>
                <div class="tableNumSelectedRowsText flex flex-row align-items-center px-3 border-x-1 border-300">
                    @if(selectedEntryExitAuths.length > 0){
                        <div>
                            {{ selectedEntryExitAuths.length + ' ' + ('content.selected' | translate) }}
                        </div>
                        <button pButton [disabled]="!canReadAndWrite && !readOnly || selectedEntryExitAuths.length > 1" icon="pi pi-{{ readOnly || !userIsVerified ? 'eye' : 'pencil' }}" [text]="true" class="ml-3" style="padding: 0; width: 1.5rem;" (click)="onEditEntryExitAuth()"></button>
                        <button pButton [disabled]="!canReadAndWrite || !userIsVerified" icon="pi pi-trash" [text]="true" class="ml-2" style="padding: 0; width: 1.5rem;" (click)="deleteMultipleEntryExitAuths()"></button>
                    }
                </div>
            </div>
            <div class="flex flex-row flex-wrap justify-content-center gap-4 align-items-center">
                <p-iconField iconPosition="right">
                    <input pInputText type="text"
                        (input)="dt.filterGlobal($event.target.value, 'contains')"
                        placeholder="{{ 'content.search' | translate }}"
                    />
                    <p-inputIcon styleClass="pi pi-search"></p-inputIcon>
                </p-iconField>
                <div class="add-action-main-full">
                    <p-button
                        ngClass="add-action-main-full"
                        [disabled]="!(canReadAndWrite && userIsVerified)"
                        [style]="{'color': '#FFFFFF', 'border': 'none', 'background': '#0AB4BA', 'font-family': 'Open Sans', 'font-size': '14px'}"
                        label="{{ 'content.new_authorization' | translate }}"
                        icon="pi pi-plus" iconPos="right"
                        [rounded]="true"
                        (onClick)="onNewEntryExitAuth()"
                    ></p-button>
                </div>
                <div class="add-action-main-small">
                    <p-button
                        [disabled]="!(canReadAndWrite && userIsVerified)"
                        [style]="{'color': '#FFFFFF', 'border': 'none', 'background': '#0AB4BA', 'font-family': 'Open Sans', 'font-size': '14px'}"
                        icon="pi pi-plus"
                        [rounded]="true"
                        (onClick)="onNewEntryExitAuth()"
                    ></p-button>
                </div>
            </div>
        </div>
        <div></div>
        <p-table
            #dt
            [value]="listOfEntryExitAuths"
            (onFilter)="onFilter($event, dt)"
            [(selection)]="selectedEntryExitAuths"
            dataKey="id"
            [rowHover]="true"
            [paginator]="true"
            [rows]="10"
            [rowsPerPageOptions]="[5, 10, 20]"
            [scrollable]="true"
            scrollDirection="horizontal"
            [tableStyle]="{ 'min-width': '75rem' }"
            styleClass="fixed-table"
            [showCurrentPageReport]="true"
            currentPageReportTemplate="{{ 'content.showing' | translate }} {first} {{ 'content.to' | translate }} {last} {{ 'content.of' | translate}} {totalRecords} {{ 'content.records' | translate }}"
            [globalFilterFields]="[
                'id',
                'authReason',
                'authRegistrationDate',
                'authUserSignatureDate',
                'authUserId',
                'authStartDateTime',
                'authEndDateTime',
                'type',
                'status',
                'isCompleted',
                'createdBy',
                'createdAt',
                'updatedBy',
                'updatedAt',
            ]"
            [sortField]="'id'" [sortOrder]="1">
            <ng-template pTemplate="header">
                <tr>
                    <th style="width: 4rem"></th>
                    <th class="fixed-column" pSortableColumn="id"> {{ 'content.authCode' | translate }} <p-sortIcon field="id"></p-sortIcon></th>
                    <th class="fixed-column" pSortableColumn="authReason"> {{ 'content.reason' | translate }} <p-sortIcon field="authReason"></p-sortIcon></th>
                    <th class="fixed-column" pSortableColumn="authRegistrationDate"> {{ 'content.authRegistrationDate' | translate }} <p-sortIcon field="authRegistrationDate"></p-sortIcon></th>
                    <th class="fixed-column" pSortableColumn="authUserSignatureDate"> {{ 'content.authDate' | translate }} <p-sortIcon field="authUserSignatureDate"></p-sortIcon></th>
                    <th class="fixed-column" pSortableColumn="authUserId"> {{ 'content.authUser' | translate }} <p-sortIcon field="authUserId"></p-sortIcon></th>
                    <th class="fixed-column" pSortableColumn="authStartDateTime"> {{ 'content.authStartDateTime' | translate }} <p-sortIcon field="authStartDateTime"></p-sortIcon></th>
                    <th class="fixed-column" pSortableColumn="authEndDateTime"> {{ 'content.authEndDateTime' | translate }} <p-sortIcon field="authEndDateTime"></p-sortIcon></th>
                    <th class="fixed-column" pSortableColumn="type"> {{ 'content.type' | translate }} <p-sortIcon field="type"></p-sortIcon></th>
                    <th class="fixed-column" pSortableColumn="status"> {{ 'content.status' | translate }} <p-sortIcon field="status"></p-sortIcon></th>
                    <th class="fixed-column" pSortableColumn="isCompleted"> {{ 'content.isCompleted' | translate }} <p-sortIcon field="isCompleted"></p-sortIcon></th>
                    <th class="fixed-column" pSortableColumn="createdBy"> {{ 'content.createdBy' | translate }} <p-sortIcon field="createdBy"></p-sortIcon></th>
                    <th class="fixed-column" pSortableColumn="createdAt"> {{ 'content.createdAt' | translate }} <p-sortIcon field="createdAt"></p-sortIcon></th>
                    <th class="fixed-column" pSortableColumn="updatedBy"> {{ 'content.updatedBy' | translate }} <p-sortIcon field="updatedBy"></p-sortIcon></th>
                    <th class="fixed-column" pSortableColumn="updatedAt"> {{ 'content.updatedAt' | translate }} <p-sortIcon field="updatedAt"></p-sortIcon></th>
                    <th class="fixed-column" alignFrozen="right" pFrozenColumn [frozen]="true"></th>
                </tr>
                <tr>
                    <th style="width: 4rem">
                        <p-tableHeaderCheckbox/>
                    </th>
                    <th>
                        <p-columnFilter type="text" field="id" [showMenu]="false" matchMode="contains">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <input pInputText type="text" (input)="filter($event.target.value)" [value]="value">
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th>
                        <p-columnFilter type="text" field="authReason" [showMenu]="false" matchMode="contains">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <input pInputText type="text" (input)="filter($event.target.value)" [value]="value">
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th>
                        <p-columnFilter type="text" field="authRegistrationDate" [showMenu]="false" matchMode="customDateRange">
                            <ng-template pTemplate="filter" let-filter="filterCallback" [formGroup]="formDateFilter">
                                <p-calendar
                                    formControlName="date"
                                    selectionMode="range"
                                    (onSelect)="applyDateRangeFilter(dt, 'authRegistrationDate')"
                                    (onInput)="applyDateRangeFilter(dt, 'authRegistrationDate')"
                                    (onClickOutside)="applyDateRangeFilter(dt, 'authRegistrationDate')"
                                    placeholder="{{ 'content.select' | translate }}"
                                    dateFormat="{{ 'dateFormat' | translate }}"
                                    appendTo="body"
                                ></p-calendar>
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th>
                        <p-columnFilter type="text" field="authUserSignatureDate" [showMenu]="false" matchMode="customDateRange">
                            <ng-template pTemplate="filter" let-filter="filterCallback" [formGroup]="formDateFilter">
                                <p-calendar
                                    formControlName="date"
                                    selectionMode="range"
                                    (onSelect)="applyDateRangeFilter(dt, 'authUserSignatureDate')"
                                    (onInput)="applyDateRangeFilter(dt, 'authUserSignatureDate')"
                                    (onClickOutside)="applyDateRangeFilter(dt, 'authUserSignatureDate')"
                                    placeholder="{{ 'content.select' | translate }}"
                                    dateFormat="{{ 'dateFormat' | translate }}"
                                    appendTo="body"
                                ></p-calendar>
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th>
                        <p-columnFilter type="text" field="authUserId" [showMenu]="false" matchMode="subjectNames">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <input pInputText type="text" (input)="filter($event.target.value)" [value]="value">
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th>
                        <p-columnFilter type="text" field="authStartDateTime" [showMenu]="false" matchMode="customDateRange">
                            <ng-template pTemplate="filter" let-filter="filterCallback" [formGroup]="formDateFilter">
                                <p-calendar
                                    formControlName="date"
                                    selectionMode="range"
                                    (onSelect)="applyDateRangeFilter(dt, 'plannedDepartureDateTime')"
                                    (onInput)="applyDateRangeFilter(dt, 'plannedDepartureDateTime')"
                                    (onClickOutside)="applyDateRangeFilter(dt, 'plannedDepartureDateTime')"
                                    placeholder="{{ 'content.select' | translate }}"
                                    dateFormat="{{ 'dateFormat' | translate }}"
                                    appendTo="body"
                                ></p-calendar>
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th>
                        <p-columnFilter type="text" field="authEndDateTime" [showMenu]="false" matchMode="customDateRange">
                            <ng-template pTemplate="filter" let-filter="filterCallback" [formGroup]="formDateFilter">
                                <p-calendar
                                    formControlName="date"
                                    selectionMode="range"
                                    (onSelect)="applyDateRangeFilter(dt, 'plannedArrivalDateTime')"
                                    (onInput)="applyDateRangeFilter(dt, 'plannedArrivalDateTime')"
                                    (onClickOutside)="applyDateRangeFilter(dt, 'plannedArrivalDateTime')"
                                    placeholder="{{ 'content.select' | translate }}"
                                    dateFormat="{{ 'dateFormat' | translate }}"
                                    appendTo="body"
                                ></p-calendar>
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th>
                        <p-columnFilter field="type" matchMode="equals" [showMenu]="false">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <p-dropdown
                                    appendTo="body"
                                    [ngModel]="value"
                                    [options]="authTypeOptions"
                                    (onChange)="filter($event.value?.value)"
                                    placeholder="{{ 'content.select' | translate }}"
                                    [showClear]="true"
                                    optionLabel="label"
                                >
                                    <ng-template pTemplate="selectedItem">
                                        {{ getAuthType(value) | translate }}
                                    </ng-template>
                                    <ng-template let-option pTemplate="item">
                                        {{ option.label | translate }}
                                    </ng-template>
                                </p-dropdown>
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th>
                        <p-columnFilter type="text" field="status" [showMenu]="false" matchMode="contains">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <input pInputText type="text" (input)="filter($event.target.value)" [value]="value">
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th>
                        <p-columnFilter field="isCompleted" matchMode="equals" [showMenu]="false">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <p-dropdown
                                    appendTo="body"
                                    [ngModel]="value"
                                    [options]="[
                                        { label: 'options.true' | translate, value: true },
                                        { label: 'options.false' | translate, value: false }
                                    ]"
                                    (onChange)="filter($event.value?.value)"
                                    placeholder="{{ 'content.select' | translate }}"
                                    [showClear]="true"
                                    optionLabel="label"
                                >
                                    <ng-template pTemplate="selectedItem">
                                        {{ value !== undefined ? (value ? ('options.true' | translate) : ('options.false' | translate)) : '' }}
                                    </ng-template>
                                    <ng-template let-option pTemplate="item">
                                        {{ option.label }}
                                    </ng-template>
                                </p-dropdown>
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th>
                        <p-columnFilter type="text" field="createdBy" [showMenu]="false" matchMode="userNames">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <input pInputText type="text" (input)="filter($event.target.value)" [value]="value">
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th>
                        <p-columnFilter type="text" field="createdAt" [showMenu]="false" matchMode="customDateRange">
                            <ng-template pTemplate="filter" let-filter="filterCallback" [formGroup]="formDateFilter">
                                <p-calendar
                                    formControlName="date"
                                    selectionMode="range"
                                    (onSelect)="applyDateRangeFilter(dt, 'createdAt')"
                                    (onInput)="applyDateRangeFilter(dt, 'createdAt')"
                                    (onClickOutside)="applyDateRangeFilter(dt, 'createdAt')"
                                    placeholder="{{ 'content.select' | translate }}"
                                    dateFormat="{{ 'dateFormat' | translate }}"
                                    appendTo="body"
                                ></p-calendar>
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th>
                        <p-columnFilter type="text" field="updatedBy" [showMenu]="false" matchMode="userNames">
                            <ng-template pTemplate="filter" let-value let-filter="filterCallback">
                                <input pInputText type="text" (input)="filter($event.target.value)" [value]="value">
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th>
                        <p-columnFilter type="text" field="updatedAt" [showMenu]="false" matchMode="customDateRange">
                            <ng-template pTemplate="filter" let-filter="filterCallback" [formGroup]="formDateFilter">
                                <p-calendar
                                    formControlName="date"
                                    selectionMode="range"
                                    (onSelect)="applyDateRangeFilter(dt, 'updatedAt')"
                                    (onInput)="applyDateRangeFilter(dt, 'updatedAt')"
                                    (onClickOutside)="applyDateRangeFilter(dt, 'updatedAt')"
                                    placeholder="{{ 'content.select' | translate }}"
                                    dateFormat="{{ 'dateFormat' | translate }}"
                                    appendTo="body"
                                ></p-calendar>
                            </ng-template>
                        </p-columnFilter>
                    </th>
                    <th alignFrozen="right" pFrozenColumn [frozen]="true"></th>
                </tr>
            </ng-template>
            <ng-template pTemplate="body" let-auth let-rowIndex="rowIndex">
                <tr class="p-selectable-row" [pSelectableRow]="auth" [pSelectableRowIndex]="rowIndex">
                    <td>
                        <p-tableCheckbox [value]="auth"></p-tableCheckbox>
                    </td>
                    <td (click)="onEditEntryExitAuth(auth)" showDelay="1000" pTooltip="{{auth.id}}" tooltipPosition="top" class="ellipsis-cell">{{ auth.id }}</td>
                    <td (click)="onEditEntryExitAuth(auth)" showDelay="1000" pTooltip="{{auth.authReason}}" tooltipPosition="top" class="ellipsis-cell">{{ auth.authReason }}</td>
                    @if(isValidDate(auth.authRegistrationDate)){
                        <td (click)="onEditEntryExitAuth(auth)" showDelay="1000" pTooltip="{{auth.authRegistrationDate?.toISOString() | date:('dateTimeFormat' | translate)}}" tooltipPosition="top" class="ellipsis-cell">{{ auth.authRegistrationDate.toISOString() | date:('dateTimeFormat' | translate) }}</td>
                    }@else{
                        <td (click)="onEditEntryExitAuth(auth)" showDelay="1000" pTooltip="" tooltipPosition="top" class="ellipsis-cell"></td>
                    }
                    <!-- <td (click)="onEditEntryExitAuth(auth)" showDelay="1000" pTooltip="{{auth.authRegistrationDate}}" tooltipPosition="top" class="ellipsis-cell">{{ auth.authRegistrationDate }}</td> -->
                    @if(isValidDate(auth.authUserSignatureDate)){
                        <td (click)="onEditEntryExitAuth(auth)" showDelay="1000" pTooltip="{{auth.authUserSignatureDate?.toISOString() | date:('dateTimeFormat' | translate)}}" tooltipPosition="top" class="ellipsis-cell">{{ auth.authUserSignatureDate.toISOString() | date:('dateTimeFormat' | translate) }}</td>
                    }@else{
                        <td (click)="onEditEntryExitAuth(auth)" showDelay="1000" pTooltip="" tooltipPosition="top" class="ellipsis-cell"></td>
                    }
                    <!-- <td (click)="onEditEntryExitAuth(auth)" showDelay="1000" pTooltip="{{auth.authUserSignatureDate}}" tooltipPosition="top" class="ellipsis-cell">{{ auth.authUserSignatureDate }}</td> -->
                    <td (click)="onEditEntryExitAuth(auth)" showDelay="1000" pTooltip="{{getSubjectNamesById(auth.authUserId)}}" tooltipPosition="top" class="ellipsis-cell">{{ getSubjectNamesById(auth.authUserId) }}</td>
                    @if(isValidDate(auth.authStartDateTime)){
                        <td (click)="onEditEntryExitAuth(auth)" showDelay="1000" pTooltip="{{auth.authStartDateTime?.toISOString() | date:('dateTimeFormat' | translate)}}" tooltipPosition="top" class="ellipsis-cell">{{ auth.authStartDateTime.toISOString() | date:('dateTimeFormat' | translate) }}</td>
                    }@else{
                        <td (click)="onEditEntryExitAuth(auth)" showDelay="1000" pTooltip="" tooltipPosition="top" class="ellipsis-cell"></td>
                    }
                    <!-- <td (click)="onEditEntryExitAuth(auth)" showDelay="1000" pTooltip="{{auth.authStartDateTime}}" tooltipPosition="top" class="ellipsis-cell">{{ auth.authStartDateTime }}</td> -->
                    @if(isValidDate(auth.authEndDateTime)){
                        <td (click)="onEditEntryExitAuth(auth)" showDelay="1000" pTooltip="{{auth.authEndDateTime?.toISOString() | date:('dateTimeFormat' | translate)}}" tooltipPosition="top" class="ellipsis-cell">{{ auth.authEndDateTime.toISOString() | date:('dateTimeFormat' | translate) }}</td>
                    }@else{
                        <td (click)="onEditEntryExitAuth(auth)" showDelay="1000" pTooltip="" tooltipPosition="top" class="ellipsis-cell"></td>
                    }
                    <!-- <td (click)="onEditEntryExitAuth(auth)" showDelay="1000" pTooltip="{{auth.authEndDateTime}}" tooltipPosition="top" class="ellipsis-cell">{{ auth.authEndDateTime }}</td> -->
                    <td (click)="onEditEntryExitAuth(auth)" showDelay="1000" pTooltip="{{getAuthType(auth.type) | translate }}" tooltipPosition="top" class="ellipsis-cell">{{ getAuthType(auth.type) | translate }}</td>
                    <td (click)="onEditEntryExitAuth(auth)" showDelay="1000" pTooltip="{{auth.status ? ('status.' + auth.status | translate) : ''}}" tooltipPosition="top" class="ellipsis-cell">{{ auth.status ? ('status.' + auth.status | translate) : '' }}</td>
                    <td (click)="onEditEntryExitAuth(auth)" showDelay="1000" pTooltip="{{'options.' + auth.isCompleted | translate}}" tooltipPosition="top" class="ellipsis-cell">{{ 'options.' + auth.isCompleted | translate }}</td>
                    <td (click)="onEditEntryExitAuth(auth)" showDelay="1000" pTooltip="{{getUserNamesById(auth.createdBy)}}" tooltipPosition="top" class="ellipsis-cell">{{ getUserNamesById(auth.createdBy) }}</td>
                    @if(isValidDate(auth.createdAt)){
                        <td (click)="onEditEntryExitAuth(auth)" showDelay="1000" pTooltip="{{auth.createdAt?.toISOString() | date:('dateTimeFormat' | translate)}}" tooltipPosition="top" class="ellipsis-cell">{{ auth.createdAt.toISOString() | date:('dateTimeFormat' | translate) }}</td>
                    }@else{
                        <td (click)="onEditEntryExitAuth(auth)" showDelay="1000" pTooltip="" tooltipPosition="top" class="ellipsis-cell"></td>
                    }
                    <!-- <td (click)="onEditEntryExitAuth(auth)" showDelay="1000" pTooltip="{{auth.createdAt}}" tooltipPosition="top" class="ellipsis-cell">{{ auth.createdAt }}</td> -->
                    <td (click)="onEditEntryExitAuth(auth)" showDelay="1000" pTooltip="{{getUserNamesById(auth.updatedBy)}}" tooltipPosition="top" class="ellipsis-cell">{{ getUserNamesById(auth.updatedBy) }}</td>
                    @if(isValidDate(auth.updatedAt)){
                        <td (click)="onEditEntryExitAuth(auth)" showDelay="1000" pTooltip="{{auth.updatedAt?.toISOString() | date:('dateTimeFormat' | translate)}}" tooltipPosition="top" class="ellipsis-cell">{{ auth.updatedAt.toISOString() | date:('dateTimeFormat' | translate) }}</td>
                    }@else{
                        <td (click)="onEditEntryExitAuth(auth)" showDelay="1000" pTooltip="" tooltipPosition="top" class="ellipsis-cell"></td>
                    }
                    <!-- <td (click)="onEditEntryExitAuth(auth)" showDelay="1000" pTooltip="{{auth.updatedAt}}" tooltipPosition="top" class="ellipsis-cell">{{ auth.updatedAt }}</td> -->
                    <td alignFrozen="right" pFrozenColumn [frozen]="true" class="custom-border">
                        <div class="flex flex-row">
                            <button pButton pRipple [disabled]="!canReadAndWrite && !readOnly" icon="pi pi-{{ readOnly || !userIsVerified || !enableEdit(auth) ? 'eye' : 'pencil' }}" [text]="true" class="mr-2" style="padding: 0; width: 1.5rem;" (click)="onEditEntryExitAuth(auth)"></button>
                            <button pButton pRipple [disabled]="!canReadAndWrite || !userIsVerified" icon="pi pi-trash" [text]="true" class="mr-2" style="padding: 0; width: 1.5rem;" (click)="deleteEntryExitAuth(auth)"></button>
                        </div>
                    </td>
                </tr>
            </ng-template>
            <ng-template pTemplate="emptymessage">
                <tr>
                    <td colspan="5" class="py-4">{{ 'content.noDataAvailable' | translate }}</td>
                </tr>
            </ng-template>
        </p-table>
    </div>
</div>

<p-dialog [(visible)]="showEntryExitAuthDialog" styleClass="p-fluid" [style]="{'width': '70vw'}" [closable]="true" [modal]="true">
    <ng-template pTemplate="header">
        <div></div>
        <div class="dialog-title">
            {{ (operationType == opType.INSERT ? 'content.new_authorization' : 'content.edit_authorization') | translate }}
        </div>
    </ng-template>
    <ng-template pTemplate="content">
        <app-entry-exit-auth-edit
            [canReadAndWrite]="canReadAndWrite"
            [userIsVerified]="userIsVerified"
            [userSubject]="userSubject"
            [isPrisoner]="isPrisoner"
            [operationType]="operationType"
            [entryExitAuth]="entryExitAuth"
            [listOfAllSubjects]="listOfAllSubjects"
            [listOfAllRoles]="listOfAllRoles"
            [managerSettings]="managerSettings"
            [createUpdateButtonTitle]="createUpdateButtonTitle"
            (operationStatus)="operationStatus($event)"
        ></app-entry-exit-auth-edit>
    </ng-template>
</p-dialog>

<ng-template #empty>
    <div class="subcontainer-list gap-3">
        <div class="flex flex-row justify-content-between flex-wrap gap-2">
            <div class="flex flex-row align-items-center">
                <div class="pr-3">
                    <label class="subcontainer-title">{{ 'titles.entry_exit_authorizations' | translate}}</label>
                </div>
            </div>
        </div>
    </div>
    <div class="flex justify-content-center align-items-center">
        <app-empty
            [readAndWritePermissions]="canReadAndWrite && userIsVerified"
            buttonLabel="content.new_authorization"
            titleLabel="titles.no_entry_exit_auths_available"
            contentHeight="300px"
            (clicked)="onNewEntryExitAuth()">
        </app-empty>
    </div>
</ng-template>