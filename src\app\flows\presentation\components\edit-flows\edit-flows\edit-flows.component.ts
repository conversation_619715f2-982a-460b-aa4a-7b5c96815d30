import { DOCUMENT, formatDate } from '@angular/common';
import { AfterViewInit, Component, EventEmitter, Inject, Input, LOCALE_ID, OnChanges, OnDestroy, OnInit, Output, SimpleChanges, ViewEncapsulation } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { MessageService, ConfirmationService, MenuItem, Message } from 'primeng/api';
import { v4 as uuidv4 } from 'uuid';
import { CreateTaskFlowUseCase } from 'src/verazial-common-frontend/core/general/flow/domain/use-cases/task-flow/create-task-flow.use-case';
import { CreateDrawFlowUseCase } from 'src/verazial-common-frontend/core/general/flow/domain/use-cases/draw-flow/create-draw-flow.use-case';
import { GetDrawFlowByTaskFlowIdUseCase } from 'src/verazial-common-frontend/core/general/flow/domain/use-cases/draw-flow/get-draw-flow-by-task-flow-id.use-case';
import { UpdateTaskFlowUseCase } from 'src/verazial-common-frontend/core/general/flow/domain/use-cases/task-flow/update-task-flow.use-case';
import { UpdateDrawFlowUseCase } from 'src/verazial-common-frontend/core/general/flow/domain/use-cases/draw-flow/update-draw-flow.use-case';
import { LocalStorageService } from 'src/verazial-common-frontend/core/services/local-storage.service';
import { ConsoleLoggerService } from 'src/verazial-common-frontend/core/services/console-logger.service';
import { TaskFlowEntity } from 'src/verazial-common-frontend/core/general/flow/domain/entity/task-flow.entity';
import { ActionData } from 'src/verazial-common-frontend/core/general/flow/common/models/action-data.model';
import { AttributeData } from 'src/verazial-common-frontend/core/general/flow/common/models/attribute-data.model';
import { ActionAttribute } from 'src/verazial-common-frontend/core/general/flow/common/models/action-attributes.model';
import { AttributeType } from 'src/verazial-common-frontend/core/general/flow/common/enums/attribute-type.enum';
import Drawflow from 'drawflow';
import { ActionModel } from 'src/verazial-common-frontend/core/general/flow/common/models/action.model';
import { DrawFlowEntity } from 'src/verazial-common-frontend/core/general/flow/domain/entity/draw-flow.entity';
import { GenericKeyValue } from 'src/verazial-common-frontend/core/models/key-value.interface';
import { FlowMessageType } from 'src/verazial-common-frontend/core/models/flow-message-type.enum';
import { TranslateService } from '@ngx-translate/core';
import { ExtraData } from 'src/verazial-common-frontend/core/general/actionsV2/domain/entity/extra-data.interface';
import { AuditTrailFields } from 'src/verazial-common-frontend/core/models/audit-trail-fields.enum';
import { AuditTrailActions } from 'src/verazial-common-frontend/core/models/audit-trail-actions.enum';
import { AuditTrailService, ReasonActionTypeEnum, ReasonTypeEnum } from 'src/verazial-common-frontend/core/services/audit-trail.service';
import { GetTaskFlowByIdUseCase } from 'src/verazial-common-frontend/core/general/flow/domain/use-cases/task-flow/get-task-flow-by-id.use-case';


export class TaskFlowRelation {
  taskflow!: TaskFlowEntity;
  drawflow!: DrawFlowEntity;

  constructor(taskflow: TaskFlowEntity, drawflow: DrawFlowEntity) {
    this.taskflow = taskflow;
    this.drawflow = drawflow;
  }
}

@Component({
  selector: 'app-edit-flows',
  templateUrl: './edit-flows.component.html',
  styleUrl: './edit-flows.component.css',
  encapsulation: ViewEncapsulation.None,
  providers: [MessageService, ConfirmationService]
})
export class EditFlowsComponent implements OnInit, AfterViewInit, OnChanges, OnDestroy {
  // Inputs
  @Input() taskFlow!: TaskFlowEntity;
  @Input() readAndWritePermissions: boolean = false;
  // Outputs
  @Output() return = new EventEmitter<boolean>();

  editor!: Drawflow;

  messages: Message[] | undefined;
  showActionDialog: boolean = false;
  showComponent: boolean = false;
  divUpdated: boolean = false;
  editing: boolean = false;
  dataComponentLoading: boolean = false;
  removeOptionStatus: boolean = false;
  actions: ActionData[] = [];
  options: MenuItem[] | undefined;
  listAttributes: ActionAttribute[] = [];
  isPublishDisabled: boolean = true;
  showLastUpdate: boolean = false;
  mobile_item_selec: string = "";
  mobile_last_move: any;
  hideStyle: string = "display: none;";
  showAttributes: string = "display: none;";
  attributeStyle: string = "display: block;";
  headerActionDialog: string = "";
  lastUpdate: string = "";
  publishText: string = "";
  publishIcon: string = "import";
  autoUpdate: boolean = false;
  isFlowPublished: boolean = true;

  // Save Flow Dialog Error Messages
  flowNameError: boolean = false;
  flowNameErrorMessage: string = "";
  flowDescriptionError: boolean = false;
  flowDescriptionErrorMessage: string = "";

  menuTopLeftPosition = { x: 0, y: 0 };

  tempSelectedAction!: ActionData | undefined;

  tempData: AttributeData[] = [];

  selectedAction!: ActionData;
  selectedNodeId: number = 0;
  nodeCounter: number = 0;

  listNodesDrawFlow: ActionModel[] = [];

  attributes: any[] = [];

  showSaveFlowDialog: boolean = false;

  // assigmentType: any[] = [];

  attributeTypes = AttributeType;

  drawFlowTemp: DrawFlowEntity | undefined;

  messageType: GenericKeyValue[] = [
    { key: FlowMessageType.INFO, value: "info" },
    { key: FlowMessageType.SUCCESS, value: "success" },
    { key: FlowMessageType.WARN, value: "warn" },
    { key: FlowMessageType.ERROR, value: "error" },
  ];

  confirmDialogTimeoutLimit: number = 0;
  startCheckingInactivity: boolean = false;


  constructor(
    @Inject(DOCUMENT) private document: any,
    @Inject(LOCALE_ID) private locale: string,
    private translateService: TranslateService,
    private messageService: MessageService,
    private confirmationService: ConfirmationService,
    private fb: FormBuilder,
    private createTaskFlowUseCase: CreateTaskFlowUseCase,
    private createDrawFlowUseCase: CreateDrawFlowUseCase,
    private getDrawFlowByTaskFlowIdUseCase: GetDrawFlowByTaskFlowIdUseCase,
    private getTaskFlowByIdUseCase: GetTaskFlowByIdUseCase,
    private updateTaskFlowUseCase: UpdateTaskFlowUseCase,
    private updateDrawFlowUseCase: UpdateDrawFlowUseCase,
    private localStorageService: LocalStorageService,
    private consoleLogger: ConsoleLoggerService,
    public auditTrailService: AuditTrailService,
  ) { }

  public form: FormGroup = this.fb.group({
    actionNamePanel: [{ disabled: true }],
    actionDescriptionPanel: [{ disabled: true }],
    actionName: [],
    actionDescription: [],
    attributeType: [],
    isPublished: [],
    flowName: [],
    flowDescription: [],
    // assigmentType: [],
    profile: [],
    flowTitle: [],
    severityComboBox: []
  });

  ngOnInit(): void {

    this.actions = [];
    this.publishText = "flow.publish"
    this.publishIcon = "import";

    let newFlow: string = this.translateService.instant('flow.new_flow');

    this.form.controls['flowTitle'].setValue(newFlow);

    this.attributes = [
      { key: AttributeType.INPUT, value: "Input" },
      { key: AttributeType.DROPDOWN, value: "Dropdown" },
      { key: AttributeType.TOGGLE, value: "Toggle" },
      { key: AttributeType.BUTTON, value: "Button" },
      { key: AttributeType.MESSAGE, value: "Message" },
    ]

    let editAction: string = "";
    let removeAction: string = "";

    this.translateService.get("edit").subscribe((value: string) => { editAction = value });
    this.translateService.get("remove").subscribe((value: string) => { removeAction = value });

    this.options = [
      {
        label: editAction,
        icon: 'pi pi-fw pi-pencil',
        command: () => {
          this.editAction();
        }
      },
      {
        label: removeAction,
        icon: 'pi pi-fw pi-trash',
        command: () => {
          this.deleteActionDialogBox();
          // this.removeAction();
        }
      }
    ]

    var id = document.getElementById("drawflow");
    this.editor = new Drawflow(id!!);

    this.editor.reroute = true;
    this.editor.reroute_fix_curvature = true;

    this.editor.zoom_max = 1.6;
    this.editor.zoom_min = 0.5;

    this.editor.start();

    if (this.taskFlow) {
      if (this.taskFlow.flowActions) {
        this.listNodesDrawFlow = [];
        this.actions = [];
        let temp = JSON.parse(JSON.stringify(this.taskFlow.flowActions));
        let listNodeIds: number[] = [];
        this.form.controls['flowTitle'].setValue(this.taskFlow.name);

        this.showLastUpdate = true;
        this.lastUpdate = formatDate(this.taskFlow.updatedAt!!, 'yyyy-MM-dd HH:mm:ss', this.locale);
        this.isPublishDisabled = false;
        if (!this.taskFlow.isPublished) {
          this.publishText = "flow.publish";
          this.publishIcon = "import";
          this.isFlowPublished = true;
        } else {
          this.publishText = "flow.unpublished";
          this.publishIcon = "export";
          this.isFlowPublished = false;
        }

        this.autoUpdate = true;
        this.enableAutomaticSave();

        temp.forEach((action: ActionModel) => {
          let tempNode: ActionModel = {
            id: action.id,
            fromActions: action.fromActions,
            action: action.action
          }
          this.listNodesDrawFlow = [...this.listNodesDrawFlow, tempNode];
          //Add list of Node IDs
          listNodeIds.push(action.id!);
        })

        this.nodeCounter = Math.max(...listNodeIds);
        this.loadActions(this.taskFlow.flowActions);
        this.getDrawFlowByTaskFlowId(this.taskFlow.id!!);
      }
    }

    this.form.get('flowTitle')?.disable();
  }

  ngAfterViewInit(): void {
    this.initDrawFlow();
  }

  ngOnChanges(changes: SimpleChanges): void { }

  ngOnDestroy(): void {
    this.autoUpdate = false;
    // Clean up the timeout if the component is destroyed
    this.closeConfirmationDialog();
    this.auditTrailService.resetInactivityMonitor();
  }

  resetInactivityMonitor() {
    this.startCheckingInactivity = false;
    this.confirmDialogTimeoutLimit = 0;
  }

  loadActions(nodes: ActionModel[]) {
    this.actions = [];
    let tempDataNodes = JSON.parse(JSON.stringify(nodes));

    tempDataNodes.forEach((node: any) => {
      let findNode = [...this.actions.filter((_v, k) => _v.id == node.action.id)]
      if (findNode.length == 0) {
        this.actions = [...this.actions, node.action];
      }
    });
  }

  getDrawFlowByTaskFlowId(id: string) {
    this.getDrawFlowByTaskFlowIdUseCase.execute({ taskFlowId: id }).then(
      (response) => {
        this.drawFlowTemp = response;
        this.editor.import(JSON.parse(response.drawFlow!));
        this.loadAttributes();
      },
      (e) => {
        this.consoleLogger.error(e);
        const at_attributes: ExtraData[] = [
          { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
          { name: AuditTrailFields.RECORD_ID, value: id }
        ];
        this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.GET_DRAW_FLOW_BY_TASK_FLOW_ID, 0, 'ERROR', '', at_attributes);
      }
    )
  }

  loadAttributes() {
    this.listNodesDrawFlow.forEach((node) => {
      this.consoleLogger.error(node.action!);
      //update Component DIV
      this.selectedNodeId = node.id!;
      this.updateComponentDiv(node.action?.attributes!!)

    })
  }

  // Remove action using the ID
  removeAction() {
    if (this.tempSelectedAction) {

      this.listNodesDrawFlow.forEach((node) => {
        if (node.action?.id == this.tempSelectedAction?.id) {
          this.removeNodeConnections(node.id!);
        }
      });

      this.listNodesDrawFlow.forEach((node) => {
        if (node.action?.id == this.tempSelectedAction?.id) {

          delete this.editor.drawflow.drawflow.Home.data[node.id!];
          this.document.getElementById("node-" + node.id).remove();
          this.listNodesDrawFlow = [...this.listNodesDrawFlow.filter((_v, k) => _v.id != node.id)];

          // Remove node's connections
          let nodeIn = this.document.querySelectorAll(`svg.node_in_node-${node.id}`);
          let nodeOut = this.document.querySelectorAll(`svg.node_out_node-${node.id}`);
          nodeIn.forEach((connIn: any) => {
            connIn.remove();
          });

          nodeOut.forEach((connOut: any) => {
            connOut.remove();
          });
        }
      });
      this.actions = [...this.actions.filter((_v, k) => _v.id != this.tempSelectedAction?.id)];

      if (this.actions.length == 0) {
        this.hideStyle = "display: none;"
      }

      this.tempSelectedAction = undefined;
    }
  }

  // Removing connections
  removeNodeConnections(nodeId: number) {
    this.listNodesDrawFlow.forEach((node) => {
      if (this.editor.drawflow.drawflow.Home.data[node.id!].inputs[`input_1`]) {
        let connectionIn = this.editor.drawflow.drawflow.Home.data[node.id!].inputs[`input_1`];

        connectionIn.connections.forEach((conn) => {
          if (conn.node == String(nodeId)) {
            connectionIn.connections = [...connectionIn.connections.filter((_v, k) => _v.node != String(nodeId))];
          }
        });
      }

      if (this.editor.drawflow.drawflow.Home.data[node.id!].outputs[`output_1`]) {
        let connectionOut = this.editor.drawflow.drawflow.Home.data[node.id!].outputs[`output_1`];
        connectionOut.connections.forEach((conn) => {
          if (conn.node == String(nodeId)) {
            connectionOut.connections = [...connectionOut.connections.filter((_v, k) => _v.node != String(nodeId))];
          }
        });
      }

    });
  }

  //Edit action
  editAction() {
    if (this.tempSelectedAction) {
      this.hideStyle = "display: none;";
      this.headerActionDialog = this.translateService.instant("flow.edit_action");
      this.editing = true;
      this.form.controls['actionName'].setValue(this.tempSelectedAction.name);
      this.form.controls['actionDescription'].setValue(this.tempSelectedAction.description);
      this.showActionDialog = true;
    }
  }

  initDrawFlow() {
    this.editor.on('nodeSelected', (id) => {
      this.divUpdated = false;
      this.saveTempData(this.listAttributes, this.selectedNodeId, this.selectedAction)
      this.listAttributes = [];
      this.selectedNodeId = id;
      let data = this.editor.export().drawflow.Home.data[id];
      this.selectedAction = [...this.actions.filter((_v, k) => _v.id == data.name)][0];
      this.listNodesDrawFlow.forEach((node) => {
        if (node.id == this.selectedNodeId) {
          if (node.action?.attributes != undefined) {
            this.listAttributes = node.action.attributes;
          }
        }
      });

      //
      this.form.controls['actionNamePanel'].setValue(this.selectedAction.name);
      this.form.controls['actionDescriptionPanel'].setValue(this.selectedAction.description);
      this.form.get('actionNamePanel')?.disable();
      this.form.get('actionDescriptionPanel')?.disable();

      this.onSelectComponent();

      if (this.listAttributes.length > 0) {
        this.dataComponentLoading = true
        this.attributeStyle = "visibility: hidden;";
      }

      setTimeout(() => {
        this.dataComponentLoading = false;
        this.attributeStyle = "display: block;";
        this.loadAttributeValues(this.listAttributes);
      }, 500);
    });

    this.editor.on('moduleChanged', (name) => {
      this.consoleLogger.debug("Module Changed " + name);
    });

    this.editor.on('nodeUnselected', (id) => {
      this.hideStyle = "display: none;"
    });

    this.editor.on('nodeRemoved', (id) => {
      this.listNodesDrawFlow = [...this.listNodesDrawFlow.filter((_v, k) => _v.id != id)];
    });
  }

  loadAttributeValues(attributes: ActionAttribute[]) {
    attributes.forEach((attribute) => {
      attribute.attribute?.forEach((data) => {
        if (data.key == attribute.id + '-reason-textbox') {
          this.document.getElementById(data.key).value = data.value;
        } else if (data.key == attribute.id + '-max-characters-textbox') {
          this.document.getElementById(data.key).value = data.value;
        } else if (data.key == attribute.id + '-min-characters-textbox') {
          this.document.getElementById(data.key).value = data.value;
        } else if (data.key == attribute.id + '-user-checkbox') {
          this.document.getElementById(data.key).checked = data.value;
        } else if (data.key == attribute.id + '-required-checkbox') {
          this.document.getElementById(data.key).checked = data.value;
        } else if (data.key == attribute.id + '-relay-checkbox') {
          this.document.getElementById(data.key).checked = data.value;
        } else if (data.key == attribute.id + '-options-listbox') {
          this.addOptionListBox(attribute.id!, data.value as string);
        } else if (data.key == attribute.id + '-body-textarea') {
          this.document.getElementById(data.key).value = data.value;
        } else if (data.key == attribute.id + '-severity-dropdown') {

          // messageType
          let getById = this.document.getElementById(data.key);
          const labelElement = getById.querySelector('span[aria-label]');
          labelElement.innerText = data.value;
        }
      });
    });
  }

  saveTempData(listAttributes: ActionAttribute[], nodeId: number, action: ActionData) {
    listAttributes.forEach((attribute) => {
      if (attribute.type == AttributeType.INPUT) {
        this.listNodesDrawFlow.forEach((node) => {
          if (node.id == nodeId) {

            let idText = attribute.id + '-reason-textbox';
            let idMinText = attribute.id + '-min-characters-textbox';
            let idMaxText = attribute.id + '-max-characters-textbox';
            let idChkUser = attribute.id + '-user-checkbox';
            let idChkReq = attribute.id + '-required-checkbox';

            let data: AttributeData[] = [
              // Textbox
              {
                key: idText,
                value: this.document.getElementById(idText).value
              },
              // Min number of characters
              {
                key: idMinText,
                value: this.document.getElementById(idMinText).value
              },
              // max number of characters
              {
                key: idMaxText,
                value: this.document.getElementById(idMaxText).value
              },
              // Checkbox user
              {
                key: idChkUser,
                value: this.document.getElementById(idChkUser).checked == undefined ? false : this.document.getElementById(idChkUser).checked
              },
              // Checkbox required
              {
                key: idChkReq,
                value: this.document.getElementById(idChkReq).checked == undefined ? false : this.document.getElementById(idChkReq).checked
              }
            ];
            attribute.attribute = data;
          }
        })
      } else if (attribute.type == AttributeType.DROPDOWN) {
        this.listNodesDrawFlow.forEach((node) => {
          if (node.id == nodeId) {

            let idText = attribute.id + '-reason-textbox';
            // let idChkUser = attribute.id + '-user-checkbox';
            let idChkReq = attribute.id + '-required-checkbox';
            let idSelect = attribute.id + '-options-listbox';

            let listValues = this.document.getElementById(idSelect);
            let optionValues = [...listValues.options].map(o => o.value);
            let data: AttributeData[] = [
              // Textbox
              {
                key: idText,
                value: (<HTMLInputElement>document.getElementById(idText)).value
              },
              // Checkbox required
              {
                key: idChkReq,
                value: this.document.getElementById(idChkReq).checked == undefined ? false : this.document.getElementById(idChkReq).checked
              },
              {
                key: idSelect,
                value: optionValues,
              }
            ];
            attribute.attribute = data;
          }
        })
      } else if (attribute.type == AttributeType.TOGGLE) {
        let idText = attribute.id + '-reason-textbox';
        let idChkReq = attribute.id + '-required-checkbox';

        let data: AttributeData[] = [
          // Textbox
          {
            key: idText,
            value: (<HTMLInputElement>document.getElementById(idText)).value
          },
          // Checkbox required
          {
            key: idChkReq,
            value: this.document.getElementById(idChkReq).checked == undefined ? false : this.document.getElementById(idChkReq).checked
          }
        ];
        attribute.attribute = data;

      } else if (attribute.type == AttributeType.BUTTON) {
        let idText = attribute.id + '-reason-textbox';
        let idChkReq = attribute.id + '-required-checkbox';
        let idChkIsRelay = attribute.id + '-relay-checkbox';

        let data: AttributeData[] = [
          // Textbox
          {
            key: idText,
            value: (<HTMLInputElement>document.getElementById(idText)).value
          },
          // Checkbox required
          {
            key: idChkReq,
            value: this.document.getElementById(idChkReq).checked == undefined ? false : this.document.getElementById(idChkReq).checked
          },
          {
            key: idChkIsRelay,
            value: this.document.getElementById(idChkIsRelay).checked == undefined ? false : this.document.getElementById(idChkIsRelay).checked
          },
        ];
        attribute.attribute = data;
      } else if (attribute.type == AttributeType.MESSAGE) {
        this.listNodesDrawFlow.forEach((node) => {
          if (node.id == nodeId) {

            let idText = attribute.id + '-reason-textbox';
            let bodyTextArea = attribute.id + '-body-textarea';
            let severityDropdown = attribute.id + '-severity-dropdown';
            let idChkReq = attribute.id + '-required-checkbox';

            let getById = this.document.getElementById(severityDropdown);
            const labelElement = getById.querySelector('span[aria-label]');
            const selectedValue = labelElement.getAttribute('aria-label');

            let data: AttributeData[] = [
              // Textbox
              {
                key: idText,
                value: this.document.getElementById(idText).value
              },
              // Text Area
              {
                key: bodyTextArea,
                value: this.document.getElementById(bodyTextArea).value
              },
              // max number of characters
              {
                key: severityDropdown,
                value: selectedValue
              },
              // Checkbox required
              {
                key: idChkReq,
                value: this.document.getElementById(idChkReq).checked == undefined ? false : this.document.getElementById(idChkReq).checked
              }
            ];
            attribute.attribute = data;
          }
        })
      }
    });
  }

  onShow() {
    const selected = window.getSelection()?.toString();
  }

  saveAction() {
    this.showActionDialog = true;
    this.editing = false;
    this.headerActionDialog = this.translateService.instant("flow.add_action");
    this.cleanFields();
  }

  closeActionDialog() {
    this.showActionDialog = false;
    this.cleanFields();
  }

  cleanFields() {
    this.form.controls['actionName'].reset();
    this.form.controls['actionDescription'].reset();
    this.form.controls['actionName'].setValue("");
    this.form.controls['actionDescription'].setValue("");
  }

  saveFlow() {
    this.saveTempData(this.listAttributes, this.selectedNodeId, this.selectedAction);
    if (!this.taskFlow) {
      this.showSaveFlowDialog = true;
    } else {
      this.updateTaskFlowDialogBox();
    }
  }

  acceptSaveTaskFlow() {
    this.form.controls['flowName'].markAsTouched();
    this.form.controls['flowDescription'].markAsTouched();

    const name = this.form.controls['flowName'].value ?? "";
    const description = this.form.controls['flowDescription'].value ?? "";

    if (name == "" || description == "") {
      if (name == "") {
        this.flowNameError = true;
        this.flowNameErrorMessage = "messages.error_isRequiredField"
      }
      if (description == "") {
        this.flowDescriptionError = true;
        this.flowDescriptionErrorMessage = "messages.error_isRequiredField"
      }
    }
    else {
      this.taskFlow = {
        name: this.form.controls['flowName'].value,
        description: this.form.controls['flowDescription'].value,
        isPublished: this.form.controls['isPublished'].value != undefined ? this.form.controls['isPublished'].value : false,
        flowActions: this.listNodesDrawFlow
      }

      this.isPublishDisabled = false;

      if (!this.taskFlow.isPublished) {
        this.publishText = "flow.publish";
        this.publishIcon = "import";
        this.isFlowPublished = true;
      } else {
        this.publishText = "flow.unpublished";
        this.publishIcon = "export";
        this.isFlowPublished = false;
      }

      this.showLastUpdate = true;
      this.lastUpdate = formatDate(Date.now(), 'yyyy-MM-dd HH:mm:ss', this.locale);
      this.form.controls['flowTitle'].setValue(this.taskFlow.name);

      this.taskFlow.flowActions?.forEach(action => {
        let connections = this.editor.drawflow.drawflow.Home.data[action.id!].inputs['input_1'].connections;
        let listOfConnections: number[] = [];
        connections.forEach(conn => {
          listOfConnections.push(Number(conn.node))
        })
        action.fromActions = listOfConnections;
      })

      const at_attributes: ExtraData[] = [
        { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(this.taskFlow) },
      ];
      //this.auditTrailService.auditTrailSelectReason(ReasonTypeEnum.CONFIG, AuditTrailActions.ADD_FLW, ReasonActionTypeEnum.CREATE, () => {
        this.createTaskFlowUseCase.execute({ taskFlow: this.taskFlow }).then(
          (data) => {
            this.saveDrawFlowModel(data.id!!);
          },
          (e) => {
            this.consoleLogger.error(e);
            const at_attributes: ExtraData[] = [
              { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
              { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(this.taskFlow) },
            ];
            this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.ADD_FLW, 0, 'ERROR', '', at_attributes);
          }
        );
      //}, at_attributes,false);

      this.showSaveFlowDialog = false;
    }
  }

  saveDrawFlowModel(id: string) {
    let temDrawFlow = new DrawFlowEntity();
    temDrawFlow.taskFlowId = id,
      temDrawFlow.drawFlow = JSON.stringify(this.editor.export()),
      temDrawFlow.id = this.drawFlowTemp ? this.drawFlowTemp.id : undefined;

    const at_attributes: ExtraData[] = [
      { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(new TaskFlowRelation(this.taskFlow,temDrawFlow)) },
    ];
    this.auditTrailService.auditTrailSelectReason(ReasonTypeEnum.CONFIG, AuditTrailActions.ADD_FLW, ReasonActionTypeEnum.CREATE, () => {
      this.createDrawFlowUseCase.execute({ drawFlow: temDrawFlow }).then(
        (data) => {
          this.autoUpdate = true;
          this.enableAutomaticSave();
          this.messageService.add({
            severity: 'success', summary: this.translateService.instant("titles.success_operation"), detail: this.translateService.instant("messages.success_general"),
            life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
          });
        },
        (e) => {
          this.consoleLogger.error(e);
          const at_attributes: ExtraData[] = [
            { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
            { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(temDrawFlow) },
          ];
          this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.ADD_FLW, 0, 'ERROR', '', at_attributes);
        }
      )
    }, at_attributes);
  }

  closeFlowDialog() {
    this.showSaveFlowDialog = false;
  }

  changeAttribute(event: any) {

  }

  positionMobile(event: any) {
    this.mobile_last_move = event;
  }

  onDragStart(event: any, draggedObject: ActionData) {

    if (event.type === "touchstart") {
      this.mobile_item_selec = event.target.closest(".drag-drawflow").getAttribute('data-node');
    } else {
      event.dataTransfer.setData("node", JSON.stringify(draggedObject));
    }
  }

  drop(event: any) {
    if (event.type === "touchend") {
      /*
      var parentdrawflow = document.elementFromPoint( this.mobile_last_move.touches[0].clientX, this.mobile_last_move.touches[0].clientY).closest("#drawflow");
      if(parentdrawflow != null) {
        this.addNodeToDrawFlow(this.mobile_item_selec, this.mobile_last_move.touches[0].clientX, this.mobile_last_move.touches[0].clientY);
      }
      this.mobile_item_selec = '';
      */
    } else {
      event.preventDefault();
      let data = event.dataTransfer.getData("node");

      this.nodeCounter += 1;

      let newNode: ActionModel = {
        id: this.nodeCounter,
        action: JSON.parse(data),
        fromActions: []
      }

      this.addNodeToDrawFlow(newNode, event.clientX, event.clientY);
    }
  }

  onSelectComponent() {
    this.hideStyle = "display: inline-block;";
  }

  allowDrop(event: any) {
    event.preventDefault();
  }

  addNodeToDrawFlow(node: ActionModel, pos_x: number, pos_y: number) {
    if (this.editor.editor_mode === 'fixed') {
      return false;
    }
    pos_x = pos_x * (this.editor.precanvas.clientWidth / (this.editor.precanvas.clientWidth * this.editor.zoom)) - (this.editor.precanvas.getBoundingClientRect().x * (this.editor.precanvas.clientWidth / (this.editor.precanvas.clientWidth * this.editor.zoom)));
    pos_y = pos_y * (this.editor.precanvas.clientHeight / (this.editor.precanvas.clientHeight * this.editor.zoom)) - (this.editor.precanvas.getBoundingClientRect().y * (this.editor.precanvas.clientHeight / (this.editor.precanvas.clientHeight * this.editor.zoom)));

    var htmlComponent = `
    <div>
      <div class="flow-container">
        <label class="component-label">${this.translateService.instant('flow.name')}</label>
        <input class="text-action-component" type="text" id="${node.action?.id}-${node.id}-node-action" placeholder="${node.action?.name}" disabled>
        <div id="componentAttributeTitle-${node.id}"></div>
        <div class="action-container" id="${node.id}" [style]="${this.showAttributes}">
        </div>
      </div>
    </div>
    `;

    // Adding the action to the list of actions.
    this.listNodesDrawFlow = [...this.listNodesDrawFlow, node];
    this.editor.addNode(node.action?.id!!, 1, 1, pos_x, pos_y, String(node.id), {}, htmlComponent, false);

    return false;
  }

  // Delete an element Dialog box
  deleteElement(item: ActionAttribute) {
    this.confirmationService.confirm({
      message: this.translateService.instant('messages.message_update'),
      header: this.translateService.instant("flow.remove_attribute"),
      icon: 'pi pi-exclamation-triangle',
      acceptIcon: "none",
      rejectIcon: "none",
      rejectButtonStyleClass: "p-button-text",
      acceptButtonStyleClass: "ng-confirm-button",
      acceptLabel: this.translateService.instant("delete"),
      rejectLabel: this.translateService.instant("no"),
      accept: () => {
        this.resetInactivityMonitor();
        this.deleteAttribute(item);
      },
      reject: () => {
        this.resetInactivityMonitor();
      }
    });

    this.confirmDialogTimeoutLimit = (this.localStorageService.getSessionSettings()?.timeoutMessages ?? 10) * 1000
    this.startCheckingInactivity = false;
    setTimeout(() => this.startCheckingInactivity = true, 0);
  }

  // Delete an action Dialog box
  deleteActionDialogBox() {
    this.confirmationService.confirm({
      message: this.translateService.instant('messages.message_update') + " <b>" + this.tempSelectedAction?.name + "</b>?",
      header: this.translateService.instant("flow.remove_action"),
      icon: 'pi pi-exclamation-triangle',
      acceptIcon: "none",
      rejectIcon: "none",
      rejectButtonStyleClass: "p-button-text",
      acceptButtonStyleClass: "ng-confirm-button",
      acceptLabel: this.translateService.instant("delete"),
      rejectLabel: this.translateService.instant("no"),
      accept: () => {
        this.resetInactivityMonitor();
        this.removeAction();
      },
      reject: () => {
        this.resetInactivityMonitor();
      }
    });

    this.confirmDialogTimeoutLimit = (this.localStorageService.getSessionSettings()?.timeoutMessages ?? 10) * 1000
    this.startCheckingInactivity = false;
    setTimeout(() => this.startCheckingInactivity = true, 0);
  }

  // Delete an action Dialog box
  updateTaskFlowDialogBox() {
    this.confirmationService.confirm({
      message: this.translateService.instant('messages.message_update') + " <b>" + this.taskFlow.name + "</b>?",
      header: this.translateService.instant("flow.update_flow"),
      icon: 'pi pi-exclamation-triangle',
      acceptIcon: "none",
      rejectIcon: "none",
      rejectButtonStyleClass: "p-button-text",
      acceptButtonStyleClass: "ng-confirm-button",
      acceptLabel: this.translateService.instant("save"),
      rejectLabel: this.translateService.instant("no"),
      accept: () => {
        this.resetInactivityMonitor();
        this.updateTaskFlow();
      },
      reject: () => {
        this.resetInactivityMonitor();
      }
    });

    this.confirmDialogTimeoutLimit = (this.localStorageService.getSessionSettings()?.timeoutMessages ?? 10) * 1000
    this.startCheckingInactivity = false;
    setTimeout(() => this.startCheckingInactivity = true, 0);
  }

  enableAutomaticSave() {
    if (this.autoUpdate) {
      setInterval(() => {
        this.updateTaskFlow();
      }, 180000);
    }
  }

  updateTaskFlow(event?: any) {
    if (event) {
      this.taskFlow.isPublished = !this.taskFlow.isPublished;
      if (!this.taskFlow.isPublished) {
        this.publishText = "flow.publish";
        this.publishIcon = "import";
        this.isFlowPublished = true;
      } else {
        this.publishText = "flow.unpublished";
        this.publishIcon = "export";
        this.isFlowPublished = false;
      }
    }
    this.taskFlow.flowActions = this.listNodesDrawFlow;

    this.taskFlow.flowActions?.forEach(action => {
      let connections = this.editor.drawflow.drawflow.Home.data[action.id!].inputs['input_1'].connections;
      let listOfConnections: number[] = [];
      connections.forEach(conn => {
        listOfConnections.push(Number(conn.node))
      })
      action.fromActions = listOfConnections;
    })

    let drawFlowData = new DrawFlowEntity()

    drawFlowData.id = this.drawFlowTemp ? this.drawFlowTemp.id : undefined;
    drawFlowData.taskFlowId = this.taskFlow.id,
    drawFlowData.drawFlow = JSON.stringify(this.editor.export());

    let oldTaskFlow: TaskFlowEntity;

    this.getTaskFlowByIdUseCase.execute({ id: this.taskFlow.id!! }).then(
      (data) => {
        const at_attributes: ExtraData[] = [
          { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(data) },
          { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(this.taskFlow) },
        ];
        oldTaskFlow = data;
        //this.auditTrailService.auditTrailSelectReason(ReasonTypeEnum.CONFIG, AuditTrailActions.MOD_FLW, ReasonActionTypeEnum.UPDATE, () => {
          this.updateTaskFlowUseCase.execute({ taskFlow: this.taskFlow }).then(
            (data) => {
              this.consoleLogger.debug(data);
            },
            (e) => {
              this.consoleLogger.error(e);
              const at_attributes: ExtraData[] = [
                { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
                { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(data) },
                { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(this.taskFlow) },
              ];
              this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.MOD_FLW, 0, 'ERROR', '', at_attributes);
            }
          );
        //}, at_attributes);
      },
      (e) => {
        this.consoleLogger.error(e);
        const at_attributes: ExtraData[] = [
          { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
          { name: AuditTrailFields.RECORD_ID, value: this.taskFlow.id!.toString() }
        ];
        this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.GET_TASK_FLOW_BY_ID, 0, 'ERROR', '', at_attributes);
      }
    );

    this.getDrawFlowByTaskFlowIdUseCase.execute({ taskFlowId: drawFlowData.taskFlowId!! }).then(
      (data) => {
        const at_attributes: ExtraData[] = [
          { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(new TaskFlowRelation(oldTaskFlow,data)) },
          { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(new TaskFlowRelation(this.taskFlow,drawFlowData)) },
        ];
        this.auditTrailService.auditTrailSelectReason(ReasonTypeEnum.CONFIG, AuditTrailActions.MOD_FLW, ReasonActionTypeEnum.UPDATE, () => {
          this.updateDrawFlowUseCase.execute({ drawFlow: drawFlowData }).then(
            (data) => {
              this.consoleLogger.debug(data)
            },
            (e) => {
              this.consoleLogger.error(e)
              const at_attributes: ExtraData[] = [
                { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
                { name: AuditTrailFields.OLD_VALUE, value: JSON.stringify(data) },
                { name: AuditTrailFields.NEW_VALUE, value: JSON.stringify(drawFlowData) },
              ];
              this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.MOD_FLW, 0, 'ERROR', '', at_attributes);
            }
          )
        }, at_attributes);
      },
      (e) => {
        this.consoleLogger.error(e);
        const at_attributes: ExtraData[] = [
          { name: AuditTrailFields.ERROR, value: JSON.stringify(e) },
          { name: AuditTrailFields.RECORD_ID, value: drawFlowData.taskFlowId!! }
        ];
        this.auditTrailService.registerAuditTrailAction(this.localStorageService.getUser().numId, AuditTrailActions.GET_DRAW_FLOW_BY_TASK_FLOW_ID, 0, 'ERROR', '', at_attributes);
      }
    );

    this.lastUpdate = formatDate(Date.now(), 'yyyy-MM-dd HH:mm:ss', this.locale);
    this.taskFlow.flowActions = this.listNodesDrawFlow;
    this.messageService.add({
      severity: 'success', summary: this.translateService.instant("titles.success_operation"), detail: this.translateService.instant("messages.success_general"),
      life: (this.localStorageService.getSessionSettings()?.timeoutNotification ?? 5) * 1000
    });
  }

  // Deleting an attribute
  deleteAttribute(item: ActionAttribute) {

    this.listNodesDrawFlow.forEach((node) => {
      if (node.id == this.selectedNodeId) {

        if (node.action?.attributes != undefined) {
          node.action.attributes = [...node.action.attributes.filter((_v, k) => _v.id != item.id)];
          this.listAttributes = node.action.attributes;
        }
      }
    });

    this.updateComponentDiv(this.listAttributes);
  }

  // Adding a new acction
  addNewAction() {
    if (this.editing) {
      this.listNodesDrawFlow.forEach((node) => {
        if (node.action?.id == this.tempSelectedAction?.id) {
          node.action!.name!! = this.form.controls['actionName'].value;
          node.action!.description = this.form.controls['actionDescription'].value;
          this.document.getElementById(node.action?.id + '-' + node.id + '-node-action').value = this.form.controls['actionName'].value;
        }

      })

      this.actions.forEach((action) => {
        if (action.id == this.tempSelectedAction?.id) {
          action.name = this.form.controls['actionName'].value;
          action.description = this.form.controls['actionDescription'].value;
        }
      });

    } else {
      let newAction = new ActionData()

      newAction.id = uuidv4(),
        newAction.name = this.form.controls['actionName'].value,
        newAction.description = this.form.controls['actionDescription'].value

      this.actions = [...this.actions, newAction];
    }

    this.showActionDialog = false;

    //Cleaning fields
    this.form.controls['actionName'].setValue("");
    this.form.controls['actionDescription'].setValue("");
  }

  onRightClick(action: ActionData): void {
    this.tempSelectedAction = action;
  }

  addAttribute() {
    if (this.form.controls['attributeType'].value) {
      let newAttribute = new ActionAttribute()

      newAttribute.id = uuidv4(),
        newAttribute.type = this.form.controls['attributeType'].value.key


      this.listNodesDrawFlow.forEach((node) => {
        if (node.id == this.selectedNodeId) {
          if (node.action?.attributes != undefined) {
            node.action.attributes = [...node.action.attributes, newAttribute]
          } else {
            node.action!.attributes = [newAttribute];
          }
          this.listAttributes = node.action?.attributes!;
        }
      });

      this.updateComponentDiv(this.listAttributes);
    }

  }

  updateComponentDiv(attributes: ActionAttribute[]) {
    let element = this.document.getElementById(this.selectedNodeId);
    let attributeTitle = this.document.getElementById(`componentAttributeTitle-${this.selectedNodeId}`);

    element.innerHTML = "";
    attributeTitle.innerHTML

    if (attributes.length > 1) {
      attributeTitle.classList.add("attribute-header");
      attributeTitle.innerHTML = `<div class="divider">
                    <p><label class="label-div"> ${this.translateService.instant("flow.attributes")} </label></p>
                </div>`
      // attributeTitle.innerHTML = `<h5> ${this.translateService.instant("flow.attributes")} </h5>`;
      // attributeTitle.appendChild(newDiv);
    } else if (attributes.length == 0) {
      attributeTitle.classList.remove("attribute-header");
      attributeTitle.innerHTML = "";
    }

    attributes.forEach((item) => {
      var newDiv = this.document.createElement("div");
      if (item.type == AttributeType.INPUT) {
        newDiv.innerHTML = `<div class="attribute-box"> ${this.translateService.instant("flow.input")} <label id="${item.id}-input-label">${item.name != undefined ? ": " + item.name : ""}</label> </div>`;
      } else if (item.type == AttributeType.DROPDOWN) {
        newDiv.innerHTML = `<div class="attribute-box"> ${this.translateService.instant("flow.dropdown")} <label id="${item.id}-dropdown-label">${item.name != undefined ? ": " + item.name : ""}</label> </div>`;
      } else if (item.type == AttributeType.TOGGLE) {
        newDiv.innerHTML = `<div class="attribute-box"> ${this.translateService.instant("flow.toggle")} <label id="${item.id}-toggle-label">${item.name != undefined ? ": " + item.name : ""}</label></div>`;
      } else if (item.type == AttributeType.BUTTON) {
        newDiv.innerHTML = `<div class="attribute-box"> ${this.translateService.instant("flow.button")} <label id="${item.id}-button-label">${item.name != undefined ? ": " + item.name : ""}</label></div>`;
      } else if (item.type == AttributeType.MESSAGE) {
        newDiv.innerHTML = `<div class="attribute-box"> ${this.translateService.instant("flow.message")} <label id="${item.id}-message-label">${item.name != undefined ? ": " + item.name : ""}</label></div>`;
      }
      element.appendChild(newDiv);
    });
    // Updated DIV
    this.divUpdated = true;
  }

  // Tracking Items
  trackByItems(index: number, item: any) {
    return index;
  }

  // Adding options to the listBox.
  addOptionListBox(id: string, options?: string[] | boolean | string) {

    let select = this.document.getElementById(id + '-options-listbox');

    if (!options) {
      let newOption = this.document.getElementById(id + '-option-textbox').value;
      if (newOption != "") {
        select.value = "";
        let opt = this.document.createElement(`option`);
        opt.value = newOption;
        opt.innerHTML = newOption;
        select.appendChild(opt);
        this.document.getElementById(id + '-option-textbox').value = "";
      }
    } else {
      this.document.getElementById(id + '-options-listbox').innerHTML = "";
      if (typeof options == "object") {
        options.forEach((option) => {
          let opt = this.document.createElement(`option`);
          opt.value = option;
          opt.innerHTML = option;
          select.appendChild(opt);
        });
      }
    }
  }

  removeOptionListBox(id: string) {
    let select = this.document.getElementById(id + '-options-listbox');

    for (var i = 0; i < select.length; i++) {
      if (select.options[i].selected)
        select.remove(i);
    }

  }

  onChangeAttributeName(item: ActionAttribute) {
    this.listNodesDrawFlow.forEach((node) => {
      if (node.action?.id == item.id) {
        node.action!.name! = this.document.getElementById(`${item.id}-reason-textbox`).value;
      }
    });

    this.listAttributes.forEach((attribute) => {
      if (attribute.id == item.id) {
        attribute.name = this.document.getElementById(`${item.id}-reason-textbox`).value;
      }
    });

    if (item.type == AttributeType.INPUT) {
      let inputLabel = this.document.getElementById(`${item.id}-input-label`);
      inputLabel.innerHTML = ": " + this.document.getElementById(`${item.id}-reason-textbox`).value;
    } else if (item.type == AttributeType.DROPDOWN) {
      let dropdownLabel = this.document.getElementById(`${item.id}-dropdown-label`);
      dropdownLabel.innerHTML = ": " + this.document.getElementById(`${item.id}-reason-textbox`).value;
    } else if (item.type == AttributeType.TOGGLE) {
      let toggleLabel = this.document.getElementById(`${item.id}-toggle-label`);
      toggleLabel.innerHTML = ": " + this.document.getElementById(`${item.id}-reason-textbox`).value;
    } else if (item.type == AttributeType.BUTTON) {
      let buttonLabel = this.document.getElementById(`${item.id}-button-label`);
      buttonLabel.innerHTML = ": " + this.document.getElementById(`${item.id}-reason-textbox`).value;
    } else if (item.type == AttributeType.MESSAGE) {
      let messageLabel = this.document.getElementById(`${item.id}-message-label`);
      messageLabel.innerHTML = ": " + this.document.getElementById(`${item.id}-reason-textbox`).value;
    }
  }

  onReturn() {
    this.return.emit(true);
  }

  onComboboxChange(event: any) {
    const id = event.originalEvent['target'].id;
    const filteredId = id.split('_')[0];
    const element = document.getElementById(filteredId);
    if (element) {
      const spanWithAriaLabel = element.querySelector('span.p-dropdown-label');
      if (spanWithAriaLabel) {
        spanWithAriaLabel.textContent = event.value.value;
      }
    }
  }

  closeConfirmationDialog() {
    this.confirmationService.close();
    this.resetInactivityMonitor();
  }
}