<app-inactivity-monitor
    [inactivityTimeoutLimit]="auditTrailService.confirmDialogTimeoutLimit"
    [startChecking]="auditTrailService.startCheckingInactivity"
    (expired)="auditTrailService.closeConfirmationDialog()"
></app-inactivity-monitor>
<p-card>
    <p-carousel
        *ngIf="!loading && !parentLoading && userSubjectPicHistory.length > 0 else placeholder"
        [value]="userSubjectPicHistory"
        [numVisible]="3"
        [numScroll]="1"
        [circular]="userSubjectPicHistory.length > 1"
        [showIndicators]="true"
        [responsiveOptions]="responsiveOptions">
            <ng-template let-pDataItem pTemplate="item">
                <div class="flex flex-column align-items-center justify-content-center">
                    <div [ngClass]="pDataItem.description === 'uploaded' ? 'note-left' : ''">
                        <img
                            [ngClass]="pDataItem.description === 'uploaded' ? 'border-3 border-round border-gray-400' : ''"
                            [src]="'data:image/jpeg;base64,' + pDataItem.content"
                            [alt]="picHistoryName + '-' + pDataItem.number"
                            class="pDataImage"
                            (click)="openImageDialog(pDataItem)">
                    </div>
                    <div class="pDataDateLabel flex justify-content-center">
                        <div>{{ pDataItem.createdAt | date:'dd/MM/yyyy' }}</div>
                    </div>
                </div>
            </ng-template>
    </p-carousel>
</p-card>

<ng-template #placeholder>
    <div *ngIf="!loading && !parentLoading else loadingSpinner" class="flex justify-content-center m-5">
        {{ 'content.noDataAvailable' | translate }}
    </div>
    <ng-template #loadingSpinner>
        <div class="flex justify-content-center">
            <p-progressSpinner styleClass="w-5rem h-5rem" strokeWidth="8" fill="var(--surface-ground)" animationDuration=".5s" ariaLabel="loading" />
        </div>
    </ng-template>
</ng-template>

<!-- <p-dialog [(visible)]="openFullImageDialog" [modal]="true" [style]="{ background: '#DEE2E6' }" [draggable]="false" [resizable]="false">
    <ng-template pTemplate="headless">
        <div class="flex flex-column align-items-center m-3">
            <p-image
                [src]="'data:image/jpeg;base64,' + selectedImage?.content"
                [alt]="picHistoryName + '-' + selectedImage?.number"
                width="535"/>
            <p-button pRipple
                severity="secondary"class="m-2"
                label="{{'close' | translate}}" (click)="closeDialog()"></p-button>
        </div>
    </ng-template>
</p-dialog> -->

<!-- <app-camera-dialog
    [canReadAndWrite]="canReadAndWrite"
    [loading]="loading"
    [userSubject]="userSubject"
    [selectedCurrentResult]="selectedCurrentResult"
    [showCameraDialog]="openFullImageDialog"
    (result)="onCameraResult($event)"
></app-camera-dialog> -->
<app-camera
    [canReadAndWrite]="canReadAndWrite"
    [selectedCurrentResult]="selectedCurrentResult"
    [showCameraDialog]="openFullImageDialog"
    [aspectRatio]="1"
    (result)="onCameraResult($event)"
></app-camera>