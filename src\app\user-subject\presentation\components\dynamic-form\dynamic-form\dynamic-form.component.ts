import { ChangeDetector<PERSON><PERSON>, Component, EventEmitter, Input, OnChanges, OnDestroy, OnInit, Output, SimpleChanges } from "@angular/core";
import { FormBuilder, FormGroup, Validators } from "@angular/forms";
import { CustomFieldTypes } from "src/verazial-common-frontend/core/general/manager/common/models/custom-field-type.enum";
import { ConfirmationService } from "primeng/api";
import { TranslateService } from "@ngx-translate/core";
import { LocalStorageService } from "src/verazial-common-frontend/core/services/local-storage.service";
import { ValidatorService } from "src/verazial-common-frontend/modules/shared/services/validator.service";
import { RoleEntity } from "src/verazial-common-frontend/core/general/common/entity/role.entity";
import { capitalizeFirstLetter } from "src/verazial-common-frontend/core/util/supporting-functions";
import { LanguageRecordModel, TranslationModel } from "src/verazial-common-frontend/core/general/manager/common/models/translation.model";

export class DynamicFormAttributes {
    type?: CustomFieldTypes;
    label?: string;
    key?: string;
    value?: string;
    detailId?: string;
    required?: boolean;
    disabled?: boolean;
    options?: string[];
    group?: string;
    groupRole?: string;
    tooltip?: string;
    translations?: TranslationModel;
}

@Component({
    selector: 'app-dynamic-form',
    templateUrl: './dynamic-form.component.html',
    styleUrl: './dynamic-form.component.css',
    providers: [ConfirmationService]
})
export class DynamicFormComponent implements OnInit, OnDestroy, OnChanges {

    @Input() canReadAndWrite: boolean = false;
    @Input() showForm: boolean = true;
    @Input() formInStepper: boolean = false;
    @Input() controlsConfig: { [key: string]: DynamicFormAttributes } = {};
    @Input() userSubjectRoles: RoleEntity[] = [];
    @Input() groupTranslations?: TranslationModel[];
    @Output() formSubmitted = new EventEmitter<any>();
    @Output() formModified = new EventEmitter<boolean>();
    @Output() formCancel = new EventEmitter<boolean>();

    forms: { group: string, form: FormGroup }[]  = [];
    groups: { group: string, groupControls: { [key: string]: DynamicFormAttributes } }[] = [];

    confirmDialogTimeoutLimit: number = 0;
    startCheckingInactivity: boolean = false;

    // Modified
    modified: boolean = false;

    // Form Types
    inputField = CustomFieldTypes.INPUT;
    dropdownField = CustomFieldTypes.DROPDOWN;
    toggleField = CustomFieldTypes.TOGGLE;
    textareaField = CustomFieldTypes.TEXTAREA;

    constructor(
        private fb: FormBuilder,
        private confirmationService: ConfirmationService,
        private translateService: TranslateService,
        private localStorageService: LocalStorageService,
        private validator: ValidatorService,
        public cd: ChangeDetectorRef,
    ) { }

    /* Component Functions */

    ngOnInit() {
        this.initForm();
    }

    ngOnChanges(changes: SimpleChanges) {
        if (changes['canReadAndWrite']) {
            this.toggleFormState(changes['canReadAndWrite'].currentValue);
        }
    }

    ngOnDestroy() {
        // Clean up the timeout if the component is destroyed
        this.closeConfirmationDialog();
    }

    resetInactivityMonitor() {
        this.startCheckingInactivity = false;
        this.confirmDialogTimeoutLimit = 0;
    }

    /* Dynamic Form Functions */

    /**
     * Get the keys of an object
     * @param obj Object to get the keys from
     * @returns Array of keys
     */
    objectKeys(obj: any): string[] {
        return Object.keys(obj);
    }

    getGroupControls(group: string): { [key: string]: DynamicFormAttributes } {
        return this.groups.find(g => g.group === group)?.groupControls || {};
    }

    getLabel(key: string, group: string): string {
        let translations: LanguageRecordModel[] = [];
        if(this.controlsConfig[key].translations) {
            translations = this.controlsConfig[key].translations?.translations || [];
        }
        let translation = translations.find(t => t.languageCode == this.translateService.currentLang);
        if(translation && translation.value) {
            return translation.value
        }
        return this.controlsConfig[key].label || key;
    }

    getGroupHeader(group: string): string {
        let translatedGroup = this.groupTranslations?.find(t => t.key === group);
        if(translatedGroup) {
            let translation = translatedGroup.translations?.find(t => t.languageCode == this.translateService.currentLang);
            if(translation && translation.value) {
                return translation.value
            }
        }
        return group === 'general' ? this.translateService.instant('headers.general') : group;
    }

    /**
     * Initialize the form
     */
    initForm() {
        console.log(this.controlsConfig);
        console.log(this.groupTranslations)
        this.objectKeys(this.controlsConfig).forEach(key => {
            if (this.controlsConfig[key].group && this.controlsConfig[key].group != '') {
                // If the group exists, you can handle the groupRole or other logic here.
                if (this.controlsConfig[key].groupRole != '') {
                    // Perform operations based on groupRole, if needed.
                    if (this.userSubjectRoles.find(r => r.id === Number(this.controlsConfig[key].groupRole))) {
                        let groupRole = this.groups.find(g => g.group === this.controlsConfig[key].group);
                        if (!groupRole) {
                            groupRole = { group: this.controlsConfig[key].group!, groupControls: {} };
                            this.groups.push(groupRole);
                        }
                        if (groupRole && !groupRole.groupControls[key]) {
                            groupRole.groupControls[key] = this.controlsConfig[key];
                        }
                    }
                }
                else {
                    let groupAll = this.groups.find(g => g.group === this.controlsConfig[key].group);
                    if (!groupAll) {
                        groupAll = { group: this.controlsConfig[key].group!, groupControls: {} };
                        this.groups.push(groupAll);
                    }
                    if (groupAll && !groupAll.groupControls[key]) {
                        groupAll.groupControls[key] = this.controlsConfig[key];
                    }
                }
            } else {
                // Check if the group 'general' exists in the groups array.
                let generalGroup = this.groups.find(g => g.group === 'general');

                // If it doesn't exist, create it.
                if (!generalGroup) {
                    generalGroup = { group: 'general', groupControls: {} };
                    this.groups.push(generalGroup);
                }

                // Add the control to the groupControls if it doesn't already exist.
                if (!generalGroup.groupControls[key]) {
                    generalGroup.groupControls[key] = this.controlsConfig[key];
                }
            }
        });

        this.groups.forEach(group => {
            const formGroupConfig: any = {};
            for (const key of this.objectKeys(group.groupControls)) {
                const control = group.groupControls[key];
                formGroupConfig[key] = this.fb.control(
                    { value: (control.type === CustomFieldTypes.TOGGLE ? control.value == "true" : control.value), disabled: control.disabled || false },
                    control.required ? Validators.required : null
                );
            }
            this.forms.push({group: group.group, form: this.fb.group(formGroupConfig)});
        });


        // const formGroupConfig: any = {};
        // for (const key of this.objectKeys(this.controlsConfig)) {
        //     const control = this.controlsConfig[key];
        //     formGroupConfig[key] = this.fb.control(
        //         { value: (control.type === CustomFieldTypes.TOGGLE ? control.value == "true" : control.value), disabled: control.disabled || false },
        //         control.required ? Validators.required : null
        //     );
        // }
        // this.form.push(this.fb.group(formGroupConfig));

        // Set initial form state based on canReadAndWrite
        this.toggleFormState(this.canReadAndWrite);

        // Subscribe to form value changes
        this.forms.forEach(f => {
            f.form.valueChanges.subscribe(() => {
                this.modified = true;
                f.form.markAllAsTouched();
                this.formModified.emit(this.modified);
            });
        });
    }

    /**
     * Enable or disable form based on canReadAndWrite
     */
    public toggleFormState(canReadAndWrite: boolean) {
        if (canReadAndWrite) {
            this.forms.forEach(f => f.form.enable());
        } else {
            this.forms.forEach(f => f.form.disable());
        }
        // Optional: detect changes if needed
        this.cd.detectChanges();
    }

    /**
     * Submit the form
     */
    onSubmit(index: number, group: string) {
        if (index >= 0 && group != '') {
            if (this.forms[index].form.valid) {
                this.formSubmitted.emit(this.forms[index].form.value);
                let controls = this.groups.find(g => g.group === group)?.groupControls;
                if (controls) {
                    for (const key of this.objectKeys(controls)) {
                        const control = controls[key];
                        const formControl = this.forms[index].form.get(control.key as string);
                        if (control && formControl) {
                            if (control.type === CustomFieldTypes.TOGGLE) {
                                control.value = formControl.value ? "true" : "false";
                            }
                            else {
                                control.value = formControl.value;
                            }
                        }
                    }
                    this.modified = false;
                    this.formModified.emit(this.modified);
                }
            }
        }
        else {
            this.forms.forEach(f => {
                this.formSubmitted.emit(f.form.value);
                let controls = this.groups.find(g => g.group === group)?.groupControls;
                if (controls) {
                    for (const key of this.objectKeys(controls)) {
                        const control = controls[key];
                        const formControl = f.form.get(control.key as string);
                        if (control && formControl) {
                            if (control.type === CustomFieldTypes.TOGGLE) {
                                control.value = formControl.value ? "true" : "false";
                            }
                            else {
                                control.value = formControl.value;
                            }
                        }
                    }
                }
            });
            this.modified = false;
            this.formModified.emit(this.modified);
        }
    }

    /**
     * Cancel
     */
    onCancel(index: number, group: string) {
        if(this.modified){
            this.confirmationService.confirm({
                message: this.translateService.instant('messages.exitingWithoutSaving'),
                header: this.translateService.instant('titles.exitingWithoutSaving'),
                icon: 'pi pi-exclamation-triangle',
                acceptIcon: "none",
                acceptLabel: this.translateService.instant('options.true'),
                rejectIcon: "none",
                rejectLabel: this.translateService.instant('options.false'),
                rejectButtonStyleClass: "p-button-text",
                accept: () => {
                    this.resetInactivityMonitor();
                    this.formCancel.emit(true);
                    if(index >= 0 && group != '') {
                        this.resetForm(index, group);
                    }
                    else {
                        this.resetFormStepper();
                    }
                },
                reject: () => {
                    this.resetInactivityMonitor();
                }
            });

            this.confirmDialogTimeoutLimit = (this.localStorageService.getSessionSettings()?.timeoutMessages ?? 10) * 1000
            this.startCheckingInactivity = false;
            setTimeout(() => this.startCheckingInactivity = true, 0);
        }
        else {
            this.formCancel.emit(true);
            if(index >= 0 && group != '') {
                this.resetForm(index, group);
            }
            else {
                this.resetFormStepper();
            }
        }
    }

    /**
     * Reset Form
     */
    resetForm(index: number, group: string) {
        this.toggleFormState(this.canReadAndWrite); // Adjust form state based on
        let controls = this.groups.find(g => g.group === group)?.groupControls;
        if(controls) {
            this.forms[index].form.reset();
            for (const key of this.objectKeys(controls)) {
                const control = controls[key];
                const formControl = this.forms[index].form.get(control.key as string);
                if (formControl) {
                    formControl.markAsUntouched();
                    if (control.type === CustomFieldTypes.TOGGLE) {
                        formControl.setValue(control.value == "true");
                    }
                    else {
                        formControl.setValue(control.value);
                    }
                }
            }
            this.modified = false;
            this.formModified.emit(this.modified);
        }
    }

    resetFormStepper() {
        this.toggleFormState(this.canReadAndWrite); // Adjust form state based on
        this.forms.forEach(f => {
            let controls = this.groups.map(g => g.groupControls).reduce((acc, val) => ({...acc, ...val}), {});
            if(controls) {
                f.form.reset();
                for (const key of this.objectKeys(controls)) {
                    const control = controls[key];
                    const formControl = f.form.get(control.key as string);
                    if (formControl) {
                        formControl.markAsUntouched();
                        if (control.type === CustomFieldTypes.TOGGLE) {
                            formControl.setValue(control.value == "true");
                        }
                        else {
                            formControl.setValue(control.value);
                        }
                    }
                }
            }
        });
        this.modified = false;
        this.formModified.emit(this.modified);
    }

    closeConfirmationDialog() {
        this.confirmationService.close();
        this.resetInactivityMonitor();
    }

    isValid(field: string, formIndex: number) {
        const form = this.forms[formIndex].form;
        return this.validator.isValidField(form, field);
    }

    capitalizeFirstLetter(value: string) {
        return capitalizeFirstLetter(value);
    }
}